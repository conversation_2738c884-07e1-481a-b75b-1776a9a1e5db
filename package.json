{"scripts": {"lint": "eslint --ext js .", "lint:fix": "eslint --ext js . --fix", "local": "sls offline --stage dev", "deploy-dev": "sls deploy --stage dev", "prepare": "husky", "db:migrate": "sequelize db:migrate"}, "devDependencies": {"@aws-sdk/client-amplify": "^3.454.0", "@aws-sdk/client-lambda": "^3.388.0", "@aws-sdk/client-rekognition": "^3.540.0", "@aws-sdk/client-s3": "^3.367.0", "@aws-sdk/client-secrets-manager": "^3.386.0", "@aws-sdk/client-ses": "^3.556.0", "@aws-sdk/client-sqs": "^3.473.0", "@aws-sdk/lib-dynamodb": "^3.363.0", "@aws-sdk/s3-request-presigner": "^3.367.0", "drizzle-kit": "^0.24.2", "eslint": "^8.43.0", "eslint-config-google": "^0.14.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^9.0.11", "prettier": "^2.8.8", "prettier-plugin-organize-imports": "^3.2.2", "sequelize-auto": "^0.8.8", "sequelize-cli": "^6.6.2", "serverless": "^3.38.0", "serverless-lift": "^1.28.1", "serverless-offline": "^13.2.0", "serverless-offline-local-authorizers-plugin": "^1.2.0", "serverless-openapi-documenter": "^0.0.109", "serverless-plugin-common-excludes": "^4.0.0", "serverless-prune-versions": "^1.0.4", "serverless-sequelize-migrations": "^1.1.2"}, "dependencies": {"@supabase/supabase-js": "^2.45.4", "aws-serverless-swagger-ui": "^1.0.1", "axios": "^1.7.2", "bcryptjs-then": "^1.0.1", "crypto-js": "^4.2.0", "docxtemplater": "^3.55.3", "drizzle-orm": "^0.33.0", "dynamoose": "^3.2.1", "expo-server-sdk": "^3.14.0", "google-auth-library": "^9.7.0", "jsonwebtoken": "^9.0.0", "jwks-rsa": "^3.1.0", "moment": "^2.29.4", "nodemailer": "^6.9.7", "nodemailer-express-handlebars": "^6.1.0", "otplib": "^12.0.1", "paymentwall": "^2.0.0", "pg": "^8.12.0", "pizzip": "^3.1.7", "postgres": "^3.4.4", "qs": "^6.11.2", "sequelize": "^6.37.3", "sharp": "^0.32.1", "short-unique-id": "^5.0.3", "xlsx-js-style": "^1.2.0"}}