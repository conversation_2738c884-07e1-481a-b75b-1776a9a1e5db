service: trecenter-api
frameworkVersion: '3'

provider:
  name: aws
  runtime: nodejs18.x
  region: ap-southeast-1
  timeout: 15
  environment:
    ENV: ${param:env}
    JWT_SECRET: 7;,P~|X3dz$xb,,7w8[b|L{4ol5~7K
    JWT_EMAIL_VERIFICATION_SECRET: FC4494FE7BE15EE99EBC798C48F4E
    CLIENT_SECRET_KEY: 8B6AC4A31FE82E997282124BA2C6D
    ADMIN_SECRET: 7H3XqGcUsZy5RmrHuvacwhnJS4UhCaRY
    GOOGLE_CLIENT_ID: ${ssm:GOOGLE_CLIENT_ID}
    GOOGLE_CLIENT_SECRET: ${ssm:GOOGLE_CLIENT_SECRET}
    PAYMENT_RESULT_URL: ${param:web_app_domain}/ket-qua-thanh-toan
    MOMO_SERVICE: MomoService
    ZALOPAY_SERVICE: ZaloPayService
    VNPAY_SERVICE: VnpayService
    PAYMENTWALL_SERVICE: PaymentwallService
    PAYPAL_SERVICE: PaypalService
    S3_UPLOAD_BUCKET: ${param:web_bucket}
    UPLOAD_PATH: ${param:upload_path}
    CLIENT_UPLOAD_PATH: ${param:client_upload_path}
    PODCAST_UPLOAD_PATH: ${param:podcast_upload_path}
    COURSE_UPLOAD_PATH: ${param:course_upload_path}
    SENDER_EMAIL: ${param:sender_email}
    MEDIA_DOMAIN: ${param:media_domain}

    PLATFORM_ENDPOINT: ${param:platform_endpoint}

    DB_DIALECT: postgres
    DB_NAME: ${ssm:DB_NAME}
    DB_USERNAME: ${ssm:DB_USERNAME}
    DB_PASSWORD: ${ssm:DB_PASSWORD}
    DB_HOST: ${ssm:DB_HOST}
    DB_PORT: ${ssm:DB_PORT}

    REGION: ${aws:region}
    # WS_APIG_ENDPOINT:
    #   Fn::Join:
    #     - ''
    #     - - Ref: WebsocketsApi
    #       - .execute-api.
    #       - Ref: AWS::Region
    #       - .amazonaws.com/
    #       - ${param:env}

  httpApi: ${file(./yml/provider/http-api.yml)}
  iamRoleStatements: ${file(./yml/provider/iam-role.yml)}

  websocketsDescription: Serverless Websockets

constructs:
  order-queue:
    type: queue
    worker:
      handler: src/handler/payment/order-queue.handler
    fifo: true
    batchSize: 5
    alarm: <EMAIL>
    extensions:
      queue:
        Properties:
          ContentBasedDeduplication: true
          DeduplicationScope: messageGroup
          FifoThroughputLimit: perMessageGroupId
  auto-ticket-queue:
    type: queue
    worker:
      handler: src/handler/queue/auto-ticket-queue.handler
    fifo: true
    batchSize: 10
    alarm: <EMAIL>
    extensions:
      queue:
        Properties:
          ContentBasedDeduplication: true
          DeduplicationScope: messageGroup
          FifoThroughputLimit: perMessageGroupId

custom:
  enabled:
    local: true
    dev: true
    prod: false
  prune:
    automatic: true
    includeLayers: true
    number: 3
  serverless-offline:
    noPrependStageInUrl: true
    noAuth: true
  documentation: ${file(./yml/docs/serverless.doc.yml):documentation}
  migrationsPath: './database/migrations'

params:
  default:
    web_bucket: media-trecenter-${aws:accountId}-${aws:region}-${param:env}
    ffmpeg_layer_arn: arn:aws:lambda:${aws:region}:${aws:accountId}:layer:ffmpeg-layer:1
    upload_path: uploads/trecenter
    client_upload_path: uploads/trecenter/client
    podcast_upload_path: uploads/trecenter/podcast
    course_upload_path: uploads/trecenter/course
  prod:
    env: prod
    web_app_domain: https://faniesta.com
    cms_domain: https://prod.d3l0zu08f1jlh.amplifyapp.com
    media_domain: https://d2qqrwxf0dhc7w.cloudfront.net
    ticketops_domain: https://ops.faniesta.com
    sender_email: <EMAIL>
    platform_endpoint: https://eventista-platform-api.1vote.vn/v1
  dev:
    env: dev
    web_app_domain: https://test.faniesta.com
    cms_domain: https://develop.d266ssl1u9lvyf.amplifyapp.com
    media_domain: https://d3oxnqmevxbpbg.cloudfront.net
    ticketops_domain: https://ticketops.dev.faniesta.com
    sender_email: <EMAIL>
    platform_endpoint: https://dev-api-platform.1vote.vn/v1
  local:
    env: local
    web_app_domain: https://test.faniesta.com
    cms_domain: https://develop.d266ssl1u9lvyf.amplifyapp.com
    media_domain: https://d3oxnqmevxbpbg.cloudfront.net
    ticketops_domain: https://ticketops.dev.faniesta.com
    platform_endpoint: https://dev-api-platform.1vote.vn/v1

layers:
  ffmpeg:
    name: ffmpeg-layer
    path: layers/ffmpeg-layer

functions:
  # Websocket
  - ${file(./yml/functions/websocket.yml)}
  # cms
  - ${file(./yml/functions/cms.yml)}
  # End client
  - ${file(./yml/functions/client.yml)}
  - ${file(./yml/functions/payment-ipn.yml)}
  # Scheduler
  - ${file(./yml/functions/schedule.yml)}
  # Other
  - ${file(./yml/functions/other.yml)}
  - ${file(./yml/functions/invocations.yml)}
  # Courses
  - ${file(./yml/functions/course.yml)}

plugins:
  - serverless-offline
  - serverless-prune-versions
  - serverless-lift
  - serverless-openapi-documenter
  - serverless-sequelize-migrations
  - ./plugins/common-excludes.js

package:
  excludeDevDependencies: true
