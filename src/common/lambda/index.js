/**
 * Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
 * SPDX-License-Identifier: Apache-2.0
 */
const {InvokeCommand, LambdaClient, LogType, InvocationType} = require('@aws-sdk/client-lambda');

const client = new LambdaClient({
  region: process.env.REGION,
});

const invoke = async (funcName, payload) => {
  console.log('Lambda invoke function: ', funcName);
  console.log('Payload: ', payload);
  try {
    const command = new InvokeCommand({
      FunctionName: funcName,
      Payload: JSON.stringify(payload),
      LogType: LogType.Tail,
      InvocationType: InvocationType.Event,
    });
    await client.send(command);
  } catch (error) {
    console.log(`Error when invoke lambda: ${JSON.stringify(error)}`);
  }
};

module.exports.LambdaClient = {invoke};
