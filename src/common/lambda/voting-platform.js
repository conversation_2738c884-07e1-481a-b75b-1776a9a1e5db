/**
 * Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
 * SPDX-License-Identifier: Apache-2.0
 */
const {InvokeCommand, LambdaClient, LogType} = require('@aws-sdk/client-lambda');

const client = new LambdaClient({
  region: process.env.REGION,
  credentials: {
    accessKeyId: process.env.VOTING_PLATFORM_KEY_ID,
    secretAccessKey: process.env.VOTING_PLATFORM_ACCESS_KEY,
  },
});

const invoke = async (funcName, payload) => {
  console.log('Lambda invoke function: ', funcName);
  console.log('Payload: ', payload);
  const command = new InvokeCommand({
    FunctionName: `${process.env.VOTING_PLATFORM_LAMBDA_ARN}-${funcName}`,
    Payload: JSON.stringify(payload),
    LogType: LogType.Tail,
  });
  const {Payload} = await client.send(command);

  return JSON.parse(Buffer.from(Payload).toString());
};

module.exports.VotingPlatformClient = {invoke};
