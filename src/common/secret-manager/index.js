const {SecretsManagerClient, GetSecretValueCommand} = require('@aws-sdk/client-secrets-manager');

const client = new SecretsManagerClient({
  region: process.env.REGION,
});

module.exports.getSecretValue = async (secretName) => {
  try {
    const response = await client.send(
      new GetSecretValueCommand({
        SecretId: secretName,
        VersionStage: 'AWSCURRENT', // VersionStage defaults to AWSCURRENT if unspecified
      }),
    );

    return JSON.parse(response.SecretString);
  } catch (error) {
    // For a list of exceptions thrown, see
    // https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_GetSecretValue.html
    throw error;
  }
};
