// src/publisher.js
const {
  SQSClient,
  SendMessageBatchCommand,
  GetQueueAttributesCommand,
} = require('@aws-sdk/client-sqs');
const client = new SQSClient();

module.exports.sendMessageToQueue = async function (messageBody, messageGroupId) {
  // Send a message into SQS
  const command = new SendMessageBatchCommand({
    QueueUrl: process.env.QUEUE_URL,
    Entries: [
      {
        Id: messageGroupId,
        MessageGroupId: messageGroupId,
        MessageBody: JSON.stringify(messageBody),
      },
    ],
  });
  return await client.send(command);
};

module.exports.sendMessageToTicketQueue = async function (messageBody, messageGroupId) {
  // Send a message into SQS
  const command = new SendMessageBatchCommand({
    QueueUrl: process.env.TICKET_QUEUE_URL,
    Entries: [
      {
        Id: messageGroupId,
        MessageGroupId: messageGroupId,
        MessageBody: JSON.stringify(messageBody),
      },
    ],
  });
  return await client.send(command);
};

module.exports.countMesssageInQueue = async function () {
  const command = new GetQueueAttributesCommand({
    QueueUrl: process.env.TICKET_QUEUE_URL,
    AttributeNames: ['ApproximateNumberOfMessages'],
  });
  const response = await client.send(command);

  return response.Attributes.ApproximateNumberOfMessages;
};
