/**
 * Eventista's products
 */
class ProductTypes {
  static P_EVENT = 'event';
  static P_FANPASS = 'fanpass';
  static P_MERCHANDISE = 'merchandise';

  static includes(type) {
    return Object.values(this).includes(type);
  }

  static isEvent(type) {
    return this.P_EVENT === type.toLowerCase();
  }

  static isFanpass(type) {
    return this.P_FANPASS === type.toLowerCase();
  }
}

module.exports = ProductTypes;
