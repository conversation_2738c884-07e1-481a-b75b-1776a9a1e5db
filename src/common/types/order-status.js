class OrderStatus {
  static REVIEW = 'REVIEW';
  static OPEN = 'OPEN';
  static SUCCESS = 'SUCCESS';
  static FAILED = 'FAILED';
  static CANCELED = 'CANCELED';
  static EXPIRED = 'EXPIRED';
  static PENDING = 'PENDING';
  static CONFIRM = 'CONFIRM';

  static isProcessed(status) {
    return [this.SUCCESS, this.FAILED, this.CANCELED].includes(status);
  }

  static isFailed(status) {
    return [this.FAILED, this.CANCELED].includes(status);
  }

  static isExpired(status) {
    return this.EXPIRED === status;
  }
}

module.exports = OrderStatus;
