class UserRoles {
  static ADMIN = 'ADMIN';
  static USER = 'USER';
  static SUPER_ADMIN = 'SUPER_ADMIN';
  static STAFF = 'STAFF'; // checkin vé
  static REPORT = 'REPORT';

  static isValidRole(role) {
    const value = role?.toUpperCase();
    return !this.isSuperAdmin(value) && Object.values(this).includes(value);
  }

  static isAdmin(role) {
    return this.ADMIN === role?.toUpperCase();
  }

  static isSuperAdmin(role) {
    return this.SUPER_ADMIN === role?.toUpperCase();
  }

  static isUser(role) {
    return this.USER === role?.toUpperCase();
  }

  static isStaff(role) {
    return this.STAFF === role?.toUpperCase();
  }

  static isReport(role) {
    return [this.ADMIN, this.REPORT].includes(role);
  }

  static isCmsAccess(role) {
    return [this.ADMIN, this.REPORT, this.STAFF, this.SUPER_ADMIN].includes(role);
  }
}

module.exports = UserRoles;
