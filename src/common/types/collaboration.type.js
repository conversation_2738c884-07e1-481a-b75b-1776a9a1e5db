class CollaborationType {
  static EVENTISTA = 'eventista';
  static AGENCY = 'agency';
  static SEATMAP = 'seatmap';
  static SEATMAP_ZONE = 'seatmap_zone';

  static isEventista(type) {
    // default is Eventista
    if (!type) {
      return true;
    }
    return [this.EVENTISTA, this.SEATMAP].includes(type);
  }

  static isValid(type) {
    return Object.values(this).includes(type);
  }

  static isSeatmap(type) {
    return type === this.SEATMAP;
  }

  static isSeatmapZone(type) {
    return type === this.SEATMAP_ZONE;
  }
}

module.exports = CollaborationType;
