class TicketEventCategory {
  static CONCERT = 'concert';
  static LIVE_SHOW = 'live_show';
  static WORKSHOP = 'workshop';
  static FAN_MEETING = 'fan_meeting';

  static isValid(category) {
    return Object.values(this).includes(category);
  }

  static isValidAll(categories = []) {
    return categories.every((category) => this.isValid(category));
  }

  static getText(category) {
    switch (category) {
      case TicketEventCategory.CONCERT:
        return 'Concert';
      case TicketEventCategory.LIVE_SHOW:
        return 'Live-Show';
      case TicketEventCategory.WORKSHOP:
        return 'Workshop';
      case TicketEventCategory.FAN_MEETING:
        return 'Fan-Meeting';
    }
  }
}

module.exports = TicketEventCategory;
