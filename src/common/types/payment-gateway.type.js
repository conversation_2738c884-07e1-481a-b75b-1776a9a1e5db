class PaymentGatewayTypes {
  static FREE = 'free';
  static MOMO = 'momo';
  static ZALOPAY = 'zalopay';
  static ZALOPAY_VIETQR = 'zalopay_vietqr';
  static ZALOPAY_CC = 'zalopay_cc';
  static VNPAY = 'vnpay';
  static INTCARD = 'intcard';
  static PAYPAL = 'paypal';
  static PAYMENTWALL = 'paymentwall';
  static COD = 'cod';
  static MANUALLY = 'manually';

  static isVNPay(type) {
    return [this.VNPAY, this.INTCARD].includes(type);
  }

  static isZalopay(type) {
    return [this.ZALOPAY, this.ZALOPAY_VIETQR, this.ZALOPAY_CC].includes(type);
  }

  static isGlobal(type) {
    return [this.PAYMENTWALL, this.PAYPAL].includes(type);
  }

  static getIcon(type) {
    const logo = {
      [this.MOMO]: 'https://media-platform.1vote.vn/logo/MoMo_Logo.png',
      [this.ZALOPAY]: 'https://media-platform.1vote.vn/logo/ZaloPay_Logo.png',
      [this.ZALOPAY_VIETQR]: 'https://media-platform.1vote.vn/logo/ZaloPay_Logo.png',
      [this.ZALOPAY_CC]: 'https://media-platform.1vote.vn/logo/ZaloPay_Logo.png',
      [this.VNPAY]: 'https://media-platform.1vote.vn/logo/Icon-VNPAY-QR.png',
      [this.INTCARD]: 'https://media-platform.1vote.vn/logo/visa-master-card.jpg',
      [this.PAYPAL]: 'https://media-platform.1vote.vn/logo/paypal.png',
      [this.PAYMENTWALL]: 'https://media-platform.1vote.vn/logo/paymentwall-global.png',
    };

    return logo[type];
  }

  static getCurrencyCode(paymentGateway) {
    if (this.isGlobal(paymentGateway)) {
      return 'USD';
    }

    return 'VND';
  }
}

module.exports = PaymentGatewayTypes;
