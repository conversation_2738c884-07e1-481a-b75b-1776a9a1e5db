const AWS = require('aws-sdk');

const apig = new AWS.ApiGatewayManagementApi({
  endpoint: process.env.WS_APIG_ENDPOINT,
});

module.exports.sendMessage = async (connectionId, topicName, body) => {
  try {
    await apig
      .postToConnection({
        ConnectionId: connectionId,
        Data: JSON.stringify({
          topicName: topicName,
          body: body,
        }),
      })
      .promise();
  } catch (err) {
    // Ignore if connection no longer exists
    if (err.statusCode !== 400 && err.statusCode !== 410) {
      throw err;
    }
  }
};
