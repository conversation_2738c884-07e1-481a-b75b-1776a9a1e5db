const {orderBy} = require('lodash');
const {ConfigModel} = require('../models/ConfigModel');

const getPageConfig = async (page) => {
  try {
    const query = await ConfigModel.query('PK').eq(page).where('display').eq(true).all().exec();
    return orderBy(query.toJSON(), ['sort'], ['asc']);
  } catch (e) {
    console.log('Error getting homepage config. ', e.message);
    return [];
  }
};

const getConfigs = async () => {
  try {
    const query = await ConfigModel.scan().all().exec();
    return orderBy(query.toJSON(), ['sort'], ['asc']);
  } catch (e) {
    console.log('Error getting config. ', e.message);
    return null;
  }
};

const createConfig = async (data) => {
  try {
    return ConfigModel.create(data);
  } catch (e) {
    console.log('Error creating config. ', e.message);
    return null;
  }
};

const updateConfig = async (data) => {
  try {
    return ConfigModel.update(data);
  } catch (e) {
    console.log('Error updating config. ', e.message);
    return null;
  }
};

const deleteConfig = async (data) => {
  try {
    return ConfigModel.delete(data);
  } catch (e) {
    console.log('Error deleting config. ', e.message);
    return null;
  }
};

const batchUpdateConfigs = async (data) => {
  try {
    return ConfigModel.batchPut(data);
  } catch (e) {
    console.log('Error deleting config. ', e.message);
    return null;
  }
};

module.exports = {
  getPageConfig,
  getConfigs,
  createConfig,
  updateConfig,
  deleteConfig,
  batchUpdateConfigs,
};
