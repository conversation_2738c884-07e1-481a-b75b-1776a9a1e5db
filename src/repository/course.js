const {CourseModel} = require('../models/CourseModel');

const createLesson = async (data) => {
  try {
    return CourseModel.create(data);
  } catch (e) {
    console.log('Error creating lesson. ', e.message);
    return null;
  }
};

const getLesson = async (data) => {
  try {
    return CourseModel.get(data);
  } catch (e) {
    console.log('Error getting lesson. ', e.message);
    return null;
  }
};

const updateLesson = async (data) => {
  try {
    return CourseModel.update(data);
  } catch (e) {
    console.log('Error updating lesson. ', e.message);
    return null;
  }
};

const deleteLesson = async (data) => {
  try {
    return CourseModel.delete(data);
  } catch (e) {
    console.log('Error deleting lesson. ', e.message);
    return null;
  }
};

module.exports = {
  createLesson,
  getLesson,
  updateLesson,
  deleteLesson,
};
