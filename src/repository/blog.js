const {orderBy} = require('lodash');
const {BlogModel} = require('../models/BlogModel');
const {Courses, Creators} = require('../postgres/init-models');
const {Op} = require('sequelize');

const getSectionItems = async (items) => {
  try {
    const query = await BlogModel.batchGet(items);
    const data = [];
    for (const item of query.toJSON()) {
      if (item.children) {
        const children = await BlogModel.batchGet(
          item.children.map((i) => ({PK: i.id.split('_')[0], SK: i.id})),
        );
        item.children = children.toJSON();
      }
      item.sort = items.find(({PK, SK}) => PK === item.PK && SK === item.SK)?.position;
      data.push(item);
    }
    return orderBy(data, ['sort'], ['asc']);
  } catch (e) {
    return [];
  }
};

const getBlogs = async (type) => {
  try {
    let query;
    if (type) {
      query = await BlogModel.scan('type').in(type).where('delFlg').eq(false).all().exec();
    } else {
      query = await BlogModel.scan('delFlg').eq(false).all().exec();
    }
    return orderBy(query.toJSON(), ['sort'], ['asc']);
  } catch (e) {
    return [];
  }
};

const createBlog = async (data) => {
  try {
    return BlogModel.create(data);
  } catch (e) {
    return null;
  }
};

const updateBlog = async (data) => {
  try {
    return BlogModel.update(data);
  } catch (e) {
    return null;
  }
};

const getCourses = async (items) => {
  const courseIds = items.map((item) => item.PK);
  const courses = await Courses.findAll({
    attributes: ['id', 'name', 'shortDescription', 'banner', 'price'],
    where: {id: {[Op.in]: courseIds}, deleteFlag: false},
    include: [
      {
        model: Creators,
        as: 'creator',
        required: true,
        attributes: ['id', 'name', 'avatar', 'position'],
        where: {deleteFlag: false},
      },
    ],
    raw: true,
    nest: true,
  });

  return courses.map((course) => ({
    ...course,
    sort: courseIds.indexOf(`${course.id}`) + 1,
  }));
};

// const getBlogByType = async (type) => {
//   try {
//     const query = await BlogModel.query('type')
//       .eq(type)
//       .using('typeIndex')
//       .where('display')
//       .eq(true)
//       .where('delFlg')
//       .eq(false)
//       .all()
//       .exec();
//
//     return orderBy(query.toJSON(), ['sort'], ['asc']);
//   } catch (e) {
//
//     return [];
//   }
// };
//
// const getBlogByStatus = async (key, status) => {
//   try {
//     const query = await BlogModel.query('PK')
//       .eq(key)
//       .where('status')
//       .eq(status)
//       .where('display')
//       .eq(true)
//       .where('delFlg')
//       .eq(false)
//       .all()
//       .exec();
//     return orderBy(query.toJSON(), ['sort'], ['asc']);
//   } catch (e) {
//
//     return [];
//   }
// };
//
// const getWorkshopsByConcertId = async (eventId) => {
//   try {
//     const query = await BlogModel.query('PK')
//       .eq(eventId)
//       .where('display')
//       .eq(true)
//       .where('delFlg')
//       .eq(false)
//       .all()
//       .exec();
//     return orderBy(query.toJSON(), ['sort'], ['asc']);
//   } catch (e) {
//
//     return [];
//   }
// };

module.exports = {
  getSectionItems,
  getBlogs,
  createBlog,
  updateBlog,
  getCourses,
};
