const {UserModel} = require('../models/UserModel');

const getUserByEmail = async (email) => {
  try {
    const user = await UserModel.get({
      PK: email,
      delFlg: false,
    });

    return user;
  } catch (e) {
    console.log('Error while getting user by email', e);
  }

  return null;
};

const getUserByPhone = async (phone) => {
  try {
    const user = await UserModel.query('phone')
      .using('phoneIndex')
      .eq(phone)
      .where('delFlg')
      .eq(false)
      .all()
      .exec();
    if (user.length > 0) return user[0];
  } catch (e) {
    console.log('Error while getting user by phone', e);
  }

  return null;
};

const createUser = async (data) => {
  try {
    console.log('Create data: ', data);
    return UserModel.create(data);
  } catch (e) {
    console.log('Error while updating user', e);
  }
};

const updateUser = async (data) => {
  try {
    console.log('Update data: ', data);
    return UserModel.update(data);
  } catch (e) {
    console.log('Error while updating user', e);
  }
};

module.exports = {
  getUserByEmail,
  getUserByPhone,
  createUser,
  updateUser,
};
