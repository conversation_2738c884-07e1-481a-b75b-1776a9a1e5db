const dynamoose = require('dynamoose');
const CollaborationType = require('../common/types/collaboration.type');
const TicketTypes = require('../common/types/ticket.type');
const TicketEventCategory = require('../common/types/ticketing-category.type');

const schema = new dynamoose.Schema(
  {
    PK: {
      type: String,
      hashKey: true,
    },
    eventName: {
      type: String,
      required: true,
      index: {
        name: 'eventNameIndex',
      },
    },
    location: {
      type: String,
      required: true,
    },
    address: {
      type: String,
      required: true,
    },
    pathName: {
      type: String,
      required: true,
      index: {
        name: 'pathNameIndex',
      },
    },
    thumbnail: {
      type: String,
      required: false,
    },
    logo: {
      type: String,
      required: false,
    },
    venueMap: {
      type: String,
      required: false,
    },
    status: {
      type: String,
      index: {
        name: 'statusIndex',
      },
      enum: ['OPEN', 'CLOSE'],
      default: 'OPEN',
    },
    eventCalendar: {
      type: Array,
      required: true,
      schema: [
        {
          type: Object,
          schema: {
            calendarId: {
              type: String,
              required: true,
            },
            startDate: {
              type: Date,
              required: true,
            },
            endDate: {
              type: Date,
              required: true,
            },
            maxTicketPerUser: {
              type: Number,
              default: -1,
            },
          },
        },
      ],
    },
    socialShared: {
      type: Object,
      schema: {
        thumbnail: String,
        description: String,
      },
    },
    hostedBy: {
      type: String,
      required: false,
    },
    tickets: {
      type: Array,
      schema: [
        {
          type: Object,
          schema: {
            ticketId: String,
            ticketType: {
              type: String,
              required: true,
            },
            ticketDescription: String,
            ticketPrice: {
              type: Number,
              default: 0,
            },
            ticketOriginalPrice: {
              type: Number,
              default: 0,
            },
            ticketPriceInUSD: {
              type: Number,
              default: 0,
            },
            ticketOriginalPriceInUSD: {
              type: Number,
              default: 0,
            },
            seatType: {
              type: String,
              enum: ['auto', 'standing_ticket'],
              default: 'auto',
            },
            maxTicketPerUser: {
              type: Number,
              default: 0,
            },
            discountPercent: Number,
            combo: {
              type: Array,
              schema: [
                {
                  type: Object,
                  schema: {
                    value: Number,
                    percent: Number,
                  },
                },
              ],
            },
            seatColor: String,
            zones: {
              type: Array,
              schema: [
                {
                  type: Object,
                  schema: {
                    zoneId: String,
                    zoneName: String,
                  },
                },
              ],
            },
            calendarId: String,
            color: String,
          },
        },
      ],
    },
    eventDesctiptions: {
      type: Array,
      schema: [
        {
          type: Object,
          schema: {
            title: String,
            description: String,
          },
        },
      ],
    },
    maxTicketPerUser: {
      type: Number,
    },
    note: {
      type: String,
    },
    general: {
      type: Object,
      schema: {
        collaboration: {
          type: String,
          enum: [
            CollaborationType.EVENTISTA,
            CollaborationType.AGENCY,
            CollaborationType.SEATMAP,
            CollaborationType.SEATMAP_ZONE,
          ],
          default: CollaborationType.EVENTISTA,
        },
        ticketType: {
          type: String,
          enum: [TicketTypes.E_TICKET, TicketTypes.HARD_TICKET],
          default: TicketTypes.E_TICKET,
        },
        paymentInfoFields: {
          type: Array,
          schema: [
            {
              type: Object,
              schema: {
                key: {
                  type: String,
                  required: true,
                },
                text: {
                  type: String,
                  required: true,
                },
                type: {
                  type: String,
                  default: 'text',
                },
                isDefault: {
                  type: Boolean,
                  default: false,
                },
                isRequired: {
                  type: Boolean,
                  default: false,
                },
              },
            },
          ],
        },
      },
      get: (value) => value || {},
    },
    domain: String,
    categories: {
      type: Array,
      schema: [
        {
          type: String,
          enum: Object.values(TicketEventCategory),
        },
      ],
      default: [],
    },
    featuredFlg: {
      type: Boolean,
    },
    hideInMarketPlace: {
      type: Boolean,
      default: false,
    },
    tenantId: {
      type: String,
      required: false,
      index: {
        name: 'tenantIdIndex',
      },
    },
    visitors: {
      type: Array,
      schema: [
        {
          type: Object,
          schema: {
            name: String,
            picture: String,
            id: String,
          },
        },
      ],
    },
    videos: {
      type: Array,
      schema: [
        {
          type: String,
        },
      ],
    },
    images: {
      type: Array,
      schema: [
        {
          type: String,
        },
      ],
    },
    background: {
      type: String,
    },
    headingBackground: {
      type: String,
    },
    concertId: {
      type: String,
      index: {
        name: 'concertIdIndex',
      },
    },
    type: String,
    authRequired: Boolean,
  },
  {
    timestamps: true,
    saveUnknown: false,
  },
);

const EventModel = dynamoose.model(`Event-${process.env.ENV}`, schema, {
  create: false,
});

module.exports = {EventModel};
