const dynamoose = require('dynamoose');

const schema = new dynamoose.Schema(
  {
    fanpassId: {
      type: String,
      hashKey: true,
    },
    refId: {
      type: String,
      rangeKey: true,
    },
    orderId: {
      type: String,
      required: true,
      index: {
        name: 'orderIdIndex',
        throughput: 'ON_DEMAND',
      },
    },
    ticketId: {
      type: String,
      required: true,
      index: {
        name: 'ticketIdIndex',
        throughput: 'ON_DEMAND',
      },
    },
    ticketType: {
      type: String,
      required: true,
    },
    ticketDescription: {
      type: String,
    },
    ticketPrice: {
      type: Number,
    },
    userId: {
      type: String,
      required: true,
      index: {
        name: 'userIdIndex',
        throughput: 'ON_DEMAND',
      },
    },
    status: {
      type: String,
      required: true,
      enum: ['ACTIVE', 'INACTIVE'],
      default: 'ACTIVE',
    },
    paymentTime: {
      type: Date,
      required: true,
      index: {
        name: 'paymentTimeIndex',
        throughput: 'ON_DEMAND',
      },
    },
    paymentGateway: {
      type: String,
      required: true,
      index: {
        name: 'paymentGatewayIndex',
        throughput: 'ON_DEMAND',
      },
    },
    paymentInfo: {
      type: Object,
      required: true,
      schema: {
        name: String,
        email: String,
        phoneNumber: String,
        address: String,
      },
    },
    discountPercent: {
      type: Number,
    },
    totalAmount: {
      type: Number,
    },
  },
  {
    timestamps: false,
    saveUnknown: false,
  },
);

const UserFanpassModel = dynamoose.model(`UserFanpass-${process.env.ENV}`, schema, {
  // create: true,
  throughput: 'ON_DEMAND',
});

module.exports = {UserFanpassModel};
