const dynamoose = require('dynamoose');

const schema = new dynamoose.Schema(
  {
    merchantId: {
      type: String,
      hashKey: true,
    },
    productId: {
      type: String,
      rangeKey: true,
    },
    baseQuantity: {
      type: Number,
      required: true,
      default: 0,
    },
    quantity: {
      type: Number,
      required: true,
      default: 0,
    },
    holders: {
      type: Array,
      schema: [
        {
          type: Object,
          schema: {
            userId: String,
            orderId: String,
            quantity: Number,
            beginTime: Date,
          },
        },
      ],
      default: [],
    },
  },
  {
    timestamps: true,
    saveUnknown: false,
  },
);

const MerchantProductStorageModel = dynamoose.model(
  `MerchantProductStorage-${process.env.ENV}`,
  schema,
);

module.exports = {MerchantProductStorageModel};
