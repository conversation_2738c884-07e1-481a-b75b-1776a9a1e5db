const dynamoose = require('dynamoose');

const schema = new dynamoose.Schema(
  {
    id: {
      type: String,
      hashKey: true,
    },
    heroBanners: {
      type: Array,
      required: true,
      schema: [
        {
          type: Object,
          schema: {
            deepLink: String,
            imageUrl: String,
            layerImageUrl: String,
            titleImageUrl: String,
            videoUrl: String,
            startTime: Date,
            endTime: Date,
            description: String,
            title: String,
          },
        },
      ],
    },
    leaderboardBanners: {
      type: Array,
      schema: [
        {
          type: Object,
          schema: {
            position: {
              type: String,
              required: true,
              enum: ['top', 'mid', 'bot'],
            },
            deepLink: String,
            imageUrlWeb: String,
            imageUrlMobile: String,
          },
        },
      ],
    },
    eventHighlights: {
      type: Array,
      schema: [
        {
          type: Object,
          schema: {
            deepLink: String,
            title: String,
            imageUrl: String,
            layerImageUrl: String,
            videoUrl: String,
          },
        },
      ],
    },
    eventBanners: {
      type: Array,
      schema: [
        {
          type: Object,
          schema: {
            eventType: {
              type: String,
              enum: ['voting', 'ticketing', 'fanpass'],
            },
            imageUrl: String,
            layerImageUrl: String,
            videoUrl: String,
            deepLink: String,
          },
        },
      ],
    },
  },
  {
    timestamps: false,
    saveUnknown: false,
  },
);

const MarketplaceConfigModel = dynamoose.model(`MarketplaceConfig-${process.env.ENV}`, schema);

module.exports = {MarketplaceConfigModel};
