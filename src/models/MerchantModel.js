const dynamoose = require('dynamoose');

const schema = new dynamoose.Schema(
  {
    id: {
      type: String,
      hashKey: true,
    },
    tenantId: {
      type: String,
      required: false,
      index: {
        name: 'tenantIdIndex',
      },
    },
    name: {
      type: String,
      required: true,
      index: {
        name: 'nameIndex',
      },
    },
    descriptions: {
      type: Array,
      required: true,
      schema: [
        {
          type: Object,
          schema: {
            title: String,
            description: String,
            showMerchantInfo: {
              type: Boolean,
              default: false,
            },
          },
        },
      ],
    },
    shortDescription: {
      type: String,
    },
    pathName: {
      type: String,
      required: false,
      index: {
        name: 'pathNameIndex',
      },
    },
    logo: {
      type: String,
      required: true,
    },
    shopLogo: {
      type: String,
      required: true,
    },
    thumbnail: {
      type: String,
      required: false,
    },
    referenceSources: {
      type: Array,
      schema: [
        {
          type: Object,
          schema: {
            thumbnail: String,
            name: String,
            url: String,
          },
        },
      ],
    },
    shipmentPickAddress: {
      type: Object,
      schema: {
        province: String,
        district: String,
        address: String,
        name: String,
        phoneNumber: String,
      },
    },
    brands: {
      type: Array,
      schema: [
        {
          type: Object,
          schema: {
            id: String,
            name: String,
            logo: String,
          },
        },
      ],
    },
    domain: {
      type: String,
    },
  },
  {
    timestamps: true,
    saveUnknown: false,
  },
);

const MerchantModel = dynamoose.model(`Merchant-${process.env.ENV}`, schema);

module.exports = {MerchantModel};
