const dynamoose = require('dynamoose');
const PaymentGatewayTypes = require('../common/types/payment-gateway.type');

const schema = new dynamoose.Schema(
  {
    type: {
      type: String,
      hashKey: true,
      enum: Object.values(PaymentGatewayTypes).filter((gw) => gw !== PaymentGatewayTypes.FREE),
    },
    title: {
      type: String,
      required: true,
    },
    subTitle: {
      type: String,
      required: true,
    },
    thumbnailImage: {
      type: String,
      required: true,
    },
    order: Number,
    bankCode: String,
  },
  {
    timestamps: true,
    saveUnknown: false,
  },
);

const PaymentMethodModel = dynamoose.model(`PaymentMethod-${process.env.ENV}`, schema);

module.exports = {PaymentMethodModel};
