const dynamoose = require('dynamoose');

const schema = new dynamoose.Schema(
  {
    PK: {
      type: String,
      hashKey: true,
    },
    SK: {
      type: String,
      rangeKey: true,
      index: {
        name: 'skIndex',
        global: true,
      },
    },
    title: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      index: {
        name: 'typeIndex',
        global: true,
      },
    },
    status: {
      type: String,
      index: {
        name: 'statusIndex',
        global: true,
      },
    },
    description: String,
    content: String,
    icon: String,
    banner: String,
    ctaTitle: String,
    ctaUrl: String,
    view: Number,
    like: Number,
    share: Number,
    file: String,
    author: String,
    startTime: String,
    endTime: String,
    children: {
      type: Array,
      schema: [
        {
          type: Object,
          schema: {
            id: String,
            sort: Number,
          },
        },
      ],
    },
    button: {
      type: Object,
      schema: {
        url: String,
        title: String,
      },
    },
    active: {
      type: Boolean,
      default: true,
    },
    display: {
      type: Boolean,
      default: true,
    },
    sort: Number,
    delFlg: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
    saveUnknown: true,
  },
);

module.exports = {
  BlogModel: dynamoose.model(`Blog-${process.env.ENV}`, schema),
};
