const dynamoose = require('dynamoose');

const schema = new dynamoose.Schema(
  {
    PK: {
      type: String,
      hashKey: true,
    },
    SK: {
      type: String,
      rangeKey: true,
      index: {
        name: 'skIndex',
        global: true,
      },
    },
    title: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      index: {
        name: 'typeIndex',
        global: true,
      },
      enum: ['VIDEO', 'QUIZ'],
    },
    videoUrl: String,
    status: {
      type: String,
      index: {
        name: 'statusIndex',
        global: true,
      },
    },
    display: {
      type: Boolean,
      default: true,
    },
    sort: Number,
    delFlg: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
    saveUnknown: true,
  },
);

module.exports = {
  CourseModel: dynamoose.model(`Course-${process.env.ENV}`, schema),
};
