const dynamoose = require('dynamoose');

const schema = new dynamoose.Schema(
  {
    id: {
      type: String,
      hashKey: true,
    },
    userId: {
      type: String,
      required: true,
      index: {
        name: 'userIdIndex',
        rangeKey: 'sendAt',
        global: true,
      },
    },
    sendAt: {
      type: Number,
      required: true,
      index: {
        name: 'sendAtIndex',
        global: true,
      },
    },
    message: {
      type: Object,
      required: true,
      schema: {
        to: String,
        sound: {
          type: String,
          default: 'default',
        },
        title: String,
        body: String,
        data: Object,
      },
    },
    deleteFlag: {
      type: Boolean,
      default: false,
    },
    isRead: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: false,
    saveUnknown: ['message.data.**'],
  },
);

const NotificationModel = dynamoose.model(`Notification-${process.env.ENV}`, schema, {
  create: true,
});

module.exports = {NotificationModel};
