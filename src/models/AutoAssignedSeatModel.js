const dynamoose = require('dynamoose');

const schema = new dynamoose.Schema(
  {
    eventId: {
      type: String,
      hashKey: true,
    },
    currentSeat: {
      type: Number,
      required: true,
      default: 0,
    },
  },
  {
    timestamps: true,
    saveUnknown: false,
  },
);

const AutoAssignedSeatModel = dynamoose.model(`AutoAssignedSeat-${process.env.ENV}`, schema, {
  create: false,
});

module.exports = {AutoAssignedSeatModel};
