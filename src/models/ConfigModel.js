const dynamoose = require('dynamoose');
const {CONFIG_MODEL_KEY} = require('../common/types/config.type');

const schema = new dynamoose.Schema(
  {
    PK: {
      type: String,
      hashKey: true,
      enum: Object.values(CONFIG_MODEL_KEY),
    },
    SK: {
      type: String,
      rangeKey: true,
    },
    title: String,
    type: String,
    position: String,
    items: {
      type: Array,
      schema: [
        {
          type: Object,
          schema: {
            PK: String,
            SK: String,
            sort: Number,
          },
        },
      ],
    },
    navigationUrl: String,
    sort: Number,
    display: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
    saveUnknown: true,
  },
);

module.exports = {
  ConfigModel: dynamoose.model(`Config-${process.env.ENV}`, schema),
};
