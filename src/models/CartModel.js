const dynamoose = require('dynamoose');

const schema = new dynamoose.Schema(
  {
    userId: {
      type: String,
      hashKey: true,
    },
    merchantId: {
      type: String,
      rangeKey: true,
    },
    orderId: {
      type: String,
      required: false,
    },
    items: {
      type: Array,
      required: false,
      schema: [
        {
          type: Object,
          schema: {
            id: String,
            quantity: Number,
            type: String,
          },
        },
      ],
    },
  },
  {
    timestamps: true,
    saveUnknown: false,
  },
);

const CartModel = dynamoose.model(`Cart-${process.env.ENV}`, schema);

module.exports = {CartModel};
