const dynamoose = require('dynamoose');
const PaymentGatewayTypes = require('../common/types/payment-gateway.type');

const schema = new dynamoose.Schema(
  {
    tenantId: {
      type: String,
      hashKey: true,
    },
    name: {
      type: String,
      required: true,
    },
    domain: {
      type: String,
      required: true,
    },
    products: {
      type: Array,
      required: true,
      schema: [
        {
          type: Object,
          schema: {
            product: {
              type: String,
              required: true,
              enum: ['voting', 'ticketing', 'fanpass', 'merchandise'],
            },
            productId: String,
            paymentGateway: {
              type: Array,
              required: true,
              schema: [
                {
                  type: String,
                  required: true,
                  enum: Object.values(PaymentGatewayTypes),
                },
              ],
            },
          },
        },
      ],
    },
    users: {
      type: Array,
      schema: [
        {
          type: Object,
          schema: {
            userId: {
              type: String,
              required: true,
            },
            products: {
              type: Array,
              schema: [
                {
                  type: Object,
                  schema: {
                    product: {
                      type: String,
                      required: true,
                      enum: ['voting', 'ticketing', 'fanpass', 'merchandise'],
                    },
                    permissions: {
                      type: Array,
                      schema: [
                        {
                          type: String,
                          enum: ['REPORT', 'CONFIGURE'],
                        },
                      ],
                    },
                  },
                },
              ],
            },
          },
        },
      ],
    },
  },
  {
    timestamps: true,
    saveUnknown: false,
  },
);

const TenantModel = dynamoose.model(`Tenant-${process.env.ENV}`, schema, {
  create: false,
});

module.exports = {TenantModel};
