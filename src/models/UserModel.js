const dynamoose = require('dynamoose');
const UserRoles = require('../common/types/user-roles');
const UserGender = require('../common/types/user-gender');
const UserStatus = require('../common/types/user-status');

const schema = new dynamoose.Schema(
  {
    PK: {
      type: String,
      hashKey: true,
      required: true,
    },
    identityId: String,
    name: String,
    email: String,
    phone: {
      type: String,
      index: {
        global: true,
        name: 'phoneIndex',
        unique: true,
      },
      validate: (value) => /^\d+$/.test(value),
    },
    role: {
      type: String,
      enum: Object.values(UserRoles),
      default: UserRoles.USER,
    },
    password: String,
    status: {
      type: Number,
      enum: Object.values(UserStatus),
      default: UserStatus.UNVERIFIED,
    },
    gender: {
      type: Number,
      enum: Object.values(UserGender),
      default: UserGender.OTHER,
    },
    birthday: String,
    icCard: String,
    university: String,
    major: String,
    schoolYear: String,
    studentId: String,
    profileImg: String,
    redirectPath: String,
    googleIdentifier: String,
    otpSecret: String,
    otpExpiredTime: Number,
    address: String,
    verified: {
      type: Boolean,
      default: false,
    },
    schoolCertificate: String,
    refreshToken: String,
    delFlg: {
      type: Boolean,
      default: false,
    },
    notificationTokens: {
      type: Array,
      schema: [
        {
          type: Object,
          schema: {
            token: String,
            deviceId: String,
          },
        },
      ],
    },
  },
  {
    timestamps: true,
    saveUnknown: true,
  },
);

module.exports = {
  UserModel: dynamoose.model(`User-${process.env.ENV}`, schema),
};
