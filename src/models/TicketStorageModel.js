const dynamoose = require('dynamoose');

const schema = new dynamoose.Schema(
  {
    PK: {
      // Event id or something else
      type: String,
      hashKey: true,
    },
    // ticketId#calendarId
    ticketId: {
      type: String,
      rangeKey: true,
    },
    baseQuantity: {
      type: Number,
      required: true,
      default: 0,
    },
    quantity: {
      type: Number,
      required: true,
      default: 0,
    },
    maxTicketPerUser: {
      type: Number,
      default: 0,
    },
    show: {
      type: Boolean,
      default: true,
    },
    stopSelling: {
      type: Boolean,
      default: false,
    },
    holders: {
      type: Array,
      schema: [
        {
          type: Object,
          schema: {
            userId: String,
            orderId: String,
            ticketQuantity: Number,
            beginTime: Date,
          },
        },
      ],
      default: [],
    },
  },
  {
    timestamps: true,
    saveUnknown: false,
  },
);

const TicketStorageModel = dynamoose.model(`TicketStorage-${process.env.ENV}`, schema);

module.exports = {TicketStorageModel};
