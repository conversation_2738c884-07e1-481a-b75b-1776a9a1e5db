const dynamoose = require('dynamoose');
const MerchantProductType = require('../common/types/merchant-product.type');

const schema = new dynamoose.Schema(
  {
    id: {
      type: String,
      hashKey: true,
    },
    merchantId: {
      type: String,
      required: true,
      index: {
        name: 'merchantIdIndex',
      },
    },
    tenantId: {
      type: String,
      required: false,
      index: {
        name: 'tenantIdIndex',
      },
    },
    name: {
      type: String,
      required: true,
      index: {
        name: 'nameIndex',
      },
    },
    brandName: {
      type: String,
      required: true,
      index: {
        name: 'brandNameIndex',
      },
    },
    description: {
      type: String,
      required: false,
    },
    note: {
      type: String,
      required: false,
    },
    type: {
      type: String,
      required: true,
      default: MerchantProductType.DEAL_EVENT,
      index: {
        name: 'typeIndex',
      },
    },
    price: {
      type: Number,
      required: true,
    },
    thumbnail: {
      type: String,
      required: true,
    },
    images: {
      type: Array,
      required: false,
      schema: [
        {
          type: String,
        },
      ],
    },
    tags: {
      type: Array,
      required: false,
      schema: [
        {
          type: String,
        },
      ],
    },
    promotion: {
      type: Object,
      required: false,
      schema: {
        fixedValue: Number,
        percentValue: Number,
      },
    },
  },
  {
    timestamps: true,
    saveUnknown: false,
  },
);

const MerchantProductModel = dynamoose.model(`MerchantProduct-${process.env.ENV}`, schema);

module.exports = {MerchantProductModel};
