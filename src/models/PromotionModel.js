const dynamoose = require('dynamoose');

const schema = new dynamoose.Schema(
  {
    eventId: {
      type: String,
      hashKey: true,
    },
    code: {
      type: String,
      rangeKey: true,
    },
    appliesTo: {
      type: String,
      enum: ['event', 'ticket'],
      default: 'event',
    },
    active: {
      type: Boolean,
      default: true,
    },
    maxUsage: {
      type: Number,
    },
    discount: {
      type: Object,
      schema: {
        percent: Number,
        percentInUSD: Number,
        maxValue: Number,
        maxValueInUSD: Number,
      },
    },
    tickets: {
      type: Array,
      schema: [String],
    },
    type: {
      type: String,
      enum: ['auto', 'manually'],
      default: 'manually',
    },
    minOrderAmount: Number,
    minOrderAmountInUSD: Number,
  },
  {
    timestamps: true,
    saveUnknown: false,
  },
);

const PromotionModel = dynamoose.model(`Promotion-${process.env.ENV}`, schema, {
  create: false,
});

module.exports = {PromotionModel};
