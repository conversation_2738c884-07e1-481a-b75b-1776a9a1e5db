const dynamoose = require('dynamoose');

const schema = new dynamoose.Schema(
  {
    tenantId: {
      type: String,
      hashKey: true,
    },
    userId: {
      type: String,
      rangeKey: true,
    },
    permissions: {
      type: Array,
      schema: [
        {
          type: String,
          enum: ['CONFIGURATION', 'REPORT', 'CHECK_IN'],
        },
      ],
      required: true,
    },
  },
  {
    timestamps: true,
    saveUnknown: false,
  },
);

const TenantUserModel = dynamoose.model(`TenantUser-${process.env.ENV}`, schema);

module.exports = {TenantUserModel};
