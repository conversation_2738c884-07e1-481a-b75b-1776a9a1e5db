const dynamoose = require('dynamoose');
const PaymentGatewayTypes = require('../common/types/payment-gateway.type');

const schema = new dynamoose.Schema(
  {
    PK: {
      // Event id or something else
      type: String,
      hashKey: true,
    },
    orderId: {
      // order id
      type: String,
      rangeKey: true,
      index: {
        global: true,
        name: 'orderIdIndex',
      },
    },
    status: {
      type: String,
      required: true,
      index: {
        name: 'statusIndex',
      },
      enum: ['PENDING', 'REVIEW', 'OPEN', 'SUCCESS', 'FAILED', 'CANCELED', 'EXPIRED', 'CONFIRM'],
      default: 'PENDING',
    },
    userId: {
      type: String,
      required: true,
      index: {
        name: 'userIdIndex',
      },
    },
    productType: {
      type: String,
      required: true,
      index: {
        name: 'productTypeIndex',
      },
    },
    paymentGateway: {
      type: String,
      enum: Object.values(PaymentGatewayTypes),
      index: {
        name: 'paymentGatewayIndex',
      },
    },
    tickets: {
      type: Array,
      required: false,
      schema: [
        {
          type: Object,
          schema: {
            ticketDescription: String,
            ticketPrice: Number,
            discountPercent: Number,
            discountFlat: Number,
            duration: Number,
            quantity: Number,
            totalAmount: Number,
          },
        },
      ],
    },
    paymentInfo: {
      type: Object,
      schema: {
        name: String,
        email: String,
        phone: String,
        birthday: String,
        gender: Number,
        icCard: String,
        university: String,
        major: String,
        schoolYear: String,
        studentId: String,
        address: String,
      },
    },
    paymentTime: {
      type: Date,
    },
    deliveryStatus: {
      type: String,
      enum: ['pending', 'delivered', 'canceled'],
      default: 'pending',
    },
    products: {
      type: Array,
      required: false,
      schema: [
        {
          type: Object,
          schema: {
            id: String,
            name: String,
            price: Number,
            thumbnail: String,
            promotion: Object,
            quantity: Number,
            totalAmount: Number,
          },
        },
      ],
    },
    promotionUsed: {
      type: Object,
      schema: {
        code: String,
        discountPercent: Number,
        maxValue: Number,
      },
    },
    currencyCode: {
      type: String,
      enum: ['VND', 'USD'],
    },
    originalAmount: Number,
    amount: Number,
    amountInUSD: Number,
    userRefId: String,
    confirmAt: {
      type: Date,
    },
    sellFromPlatform: {
      type: String,
      default: 'web',
    },
  },
  {
    timestamps: true,
    saveUnknown: true,
  },
);

const OrderModel = dynamoose.model(`Order-${process.env.ENV}`, schema, {
  create: false,
});

module.exports = {OrderModel};
