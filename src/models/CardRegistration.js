const dynamoose = require('dynamoose');

const schema = new dynamoose.Schema(
  {
    PK: {
      type: String,
      hashKey: true,
    },
    name: String,
    icCard: String,
    email: String,
    phone: String,
    studentId: String,
    gender: {
      type: Number,
      enum: [1, 2, 3],
      default: 3,
    },
    birthday: String,
    university: String,
    major: String,
    schoolYear: String,
    icCardImgFront: String,
    icCardImgBack: String,
    studentCardFront: String,
    studentCardBack: String,
    schoolCertificate: String,
    status: {
      type: String,
      enum: ['pending', 'processing', 'success'],
      default: 'pending',
    },
  },
  {
    timestamps: true,
    saveUnknown: true,
  },
);

module.exports = {
  CardRegistrationModel: dynamoose.model(`CardRegistration-${process.env.ENV}`, schema),
};
