const dynamoose = require('dynamoose');

const schema = new dynamoose.Schema(
  {
    id: {
      type: String,
      hashKey: true,
    },
    thumbnail: {
      type: String,
      required: true,
    },
    fanpassName: {
      type: String,
      required: true,
      index: {
        name: 'fanpassNameIndex',
      },
    },
    pathName: {
      type: String,
      required: true,
      index: {
        name: 'pathNameIndex',
      },
    },
    socialShared: {
      type: Object,
      schema: {
        thumbnail: String,
        description: String,
      },
    },
    benefit: {
      type: Object,
      schema: {
        thumbnail: String,
        values: {
          type: Array,
          schema: [
            {
              type: String,
            },
          ],
        },
      },
    },
    ticket: {
      type: Object,
      schema: {
        thumbnail: String,
        values: {
          type: Array,
          schema: [
            {
              type: Object,
              schema: {
                ticketId: String,
                ticketType: {
                  type: String,
                  // enum: [''],
                },
                ticketDescription: String,
                ticketPrice: Number,
                ticketVariant: Number,
                discountPercent: Number,
                discountFlat: Number,
                duration: {
                  type: Number,
                  required: true,
                },
              },
            },
          ],
        },
      },
    },
    general: {
      type: Object,
      schema: {
        paymentInfoFields: {
          type: Array,
          schema: [
            {
              type: Object,
              schema: {
                key: {
                  type: String,
                  required: true,
                },
                text: {
                  type: String,
                  required: true,
                },
                type: {
                  type: String,
                  default: 'text',
                },
                isDefault: {
                  type: Boolean,
                  default: false,
                },
                isRequired: {
                  type: Boolean,
                  default: false,
                },
              },
            },
          ],
        },
      },
      get: (value) => value || {},
    },
    domain: String,
    membershipBenefits: {
      type: Array,
      schema: [
        {
          type: Object,
          schema: {
            title: String,
            values: {
              type: Array,
              schema: [String],
            },
          },
        },
      ],
    },
  },
  {
    timestamps: true,
    saveUnknown: false,
  },
);

const FanpassModel = dynamoose.model(`Fanpass-${process.env.ENV}`, schema);

module.exports = {FanpassModel};
