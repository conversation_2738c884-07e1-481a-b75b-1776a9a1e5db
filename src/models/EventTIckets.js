const dynamoose = require('dynamoose');

const schema = new dynamoose.Schema(
  {
    eventId: {
      type: String,
      hashKey: true,
    },
    refId: {
      type: String,
      rangeKey: true,
    },
    orderId: {
      type: String,
      required: true,
      index: {
        name: 'orderIdIndex',
        throughput: 'ON_DEMAND',
      },
    },
    ticketId: {
      type: String,
      required: true,
      index: {
        name: 'ticketIdIndex',
        throughput: 'ON_DEMAND',
      },
    },
    ticketType: {
      type: String,
      required: true,
    },
    ticketDescription: {
      type: String,
    },
    ticketPrice: {
      type: Number,
    },
    discountPercent: {
      type: Number,
    },
    totalAmount: {
      type: Number,
    },
    userId: {
      type: String,
      required: true,
      index: {
        name: 'userIdIndex',
        throughput: 'ON_DEMAND',
      },
    },
    checkedIn: {
      type: Boolean,
      required: true,
      default: false,
    },
    checkinTime: {
      type: Date,
      required: false,
    },
    paymentTime: {
      type: Date,
      required: true,
      index: {
        name: 'paymentTimeIndex',
        throughput: 'ON_DEMAND',
      },
    },
    paymentGateway: {
      type: String,
      required: true,
      index: {
        name: 'paymentGatewayIndex',
        throughput: 'ON_DEMAND',
      },
    },
    paymentInfo: {
      type: Object,
      required: true,
      schema: {
        name: String,
        email: String,
        phoneNumber: String,
      },
    },
    deliveryInfo: {
      type: Object,
      schema: {
        seatCode: String,
        qrText: String,
        qrUrl: String,
      },
    },
    calendarId: {
      type: String,
    },
    promotionApplied: {
      type: Object,
      schema: {
        code: String,
        percent: Number,
        maxValue: Number,
      },
    },
  },
  {
    timestamps: false,
    saveUnknown: true,
  },
);

const EventTicketsModel = dynamoose.model(`EventTickets-${process.env.ENV}`, schema, {
  create: false,
  throughput: 'ON_DEMAND',
});

module.exports = {EventTicketsModel};
