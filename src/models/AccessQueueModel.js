const dynamoose = require('dynamoose');

const schema = new dynamoose.Schema(
  {
    eventId: {
      type: String,
      hashKey: true,
    },
    calendarId: {
      type: String,
      rangeKey: true,
    },
    waitlistIds: {
      type: Array,
      required: false,
      schema: [String],
    },
    inQueueList: {
      type: Array,
      required: false,
      schema: [
        {
          type: Object,
          schema: {
            userId: String,
            createdAt: Number,
          },
        },
      ],
    },
  },
  {
    timestamps: true,
    saveUnknown: false,
  },
);

const AccessQueueModel = dynamoose.model(`AccessQueue-${process.env.ENV}`, schema);

module.exports = {AccessQueueModel};
