module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    'quiz-result',
    {
      id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      userId: {
        type: DataTypes.STRING(500),
        allowNull: false,
      },
      quizId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'quiz',
          key: 'id',
        },
      },
      score: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      totalAnswers: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      totalCorrectAnswer: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      submittedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      timeSpend: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
    },
    {
      sequelize,
      tableName: 'quiz-result',
      schema: 'public',
      timestamps: false,
      indexes: [
        {
          name: 'quiz-result_pkey',
          unique: true,
          fields: [{name: 'id'}],
        },
        {
          name: 'quiz_id_index',
          fields: [{name: 'quizId'}],
        },
        {
          name: 'user_id_index',
          fields: [{name: 'userId'}],
        },
        {
          name: 'user_id_quiz_id_index',
          fields: [{name: 'userId'}, {name: 'quizId'}],
        },
      ],
    },
  );
};
