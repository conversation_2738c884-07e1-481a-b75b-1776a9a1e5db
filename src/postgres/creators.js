module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    'creators',
    {
      id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING(500),
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      position: {
        type: DataTypes.STRING(500),
        allowNull: false,
      },
      avatar: {
        type: DataTypes.STRING(500),
        allowNull: false,
      },
      deleteFlag: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
    },
    {
      sequelize,
      tableName: 'creators',
      schema: 'public',
      timestamps: true,
      indexes: [
        {
          name: 'creators_name_index',
          fields: [{name: 'name'}],
        },
        {
          name: 'creators_pkey',
          unique: true,
          fields: [{name: 'id'}],
        },
      ],
    },
  );
};
