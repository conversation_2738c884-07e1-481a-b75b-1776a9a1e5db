module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    'courses',
    {
      id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      creatorId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'creators',
          key: 'id',
        },
      },
      name: {
        type: DataTypes.STRING(500),
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      shortDescription: {
        type: DataTypes.STRING(500),
        allowNull: true,
      },
      banner: {
        type: DataTypes.STRING(500),
        allowNull: false,
      },
      price: {
        type: DataTypes.DOUBLE,
        allowNull: false,
        defaultValue: 0,
      },
      deleteFlag: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
    },
    {
      sequelize,
      tableName: 'courses',
      schema: 'public',
      timestamps: true,
      indexes: [
        {
          name: 'courses_name_index',
          fields: [{name: 'name'}],
        },
        {
          name: 'courses_pkey',
          unique: true,
          fields: [{name: 'id'}],
        },
      ],
    },
  );
};
