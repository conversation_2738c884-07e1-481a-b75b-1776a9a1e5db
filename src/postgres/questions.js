module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    'questions',
    {
      id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      quizId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'quiz',
          key: 'id',
        },
      },
      text: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      answers: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      correctAnswer: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
    },
    {
      sequelize,
      tableName: 'questions',
      schema: 'public',
      timestamps: true,
      indexes: [
        {
          name: 'questions_pkey',
          unique: true,
          fields: [{name: 'id'}],
        },
      ],
    },
  );
};
