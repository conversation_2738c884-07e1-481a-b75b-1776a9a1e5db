module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    'quiz',
    {
      id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      courseId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'courses',
          key: 'id',
        },
      },
      name: {
        type: DataTypes.STRING(500),
        allowNull: false,
      },
      duration: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      startTime: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      endTime: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      section: {
        type: DataTypes.STRING(250),
        allowNull: true,
      },
      deleteFlag: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      type: {
        type: DataTypes.STRING(30),
        allowNull: false,
        defaultValue: 'course_test',
      },
      passScore: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 100,
      },
    },
    {
      sequelize,
      tableName: 'quiz',
      schema: 'public',
      timestamps: true,
      indexes: [
        {
          name: 'quiz_pkey',
          unique: true,
          fields: [{name: 'id'}],
        },
      ],
    },
  );
};
