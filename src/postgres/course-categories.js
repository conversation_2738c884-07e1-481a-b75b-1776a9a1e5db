module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    'course_categories',
    {
      id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING(500),
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      deleteFlag: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
    },
    {
      sequelize,
      tableName: 'course_categories',
      schema: 'public',
      timestamps: true,
      indexes: [
        {
          name: 'course_categories_name_index',
          fields: [{name: 'name'}],
        },
        {
          name: 'course_categories_pkey',
          unique: true,
          fields: [{name: 'id'}],
        },
      ],
    },
  );
};
