module.exports = function (sequelize, DataTypes) {
  return sequelize.define(
    'lessons',
    {
      id: {
        autoIncrement: true,
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
      },
      courseId: {
        type: DataTypes.INTEGER,
        references: {
          model: 'courses',
          key: 'id',
        },
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(500),
        allowNull: false,
      },
      duration: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      mediaUrl: {
        type: DataTypes.STRING(1000),
        allowNull: false,
      },
      mediaType: {
        type: DataTypes.STRING(30),
        allowNull: false,
      },
      section: {
        type: DataTypes.STRING(250),
        allowNull: true,
      },
      deleteFlag: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
    },
    {
      sequelize,
      tableName: 'lessons',
      schema: 'public',
      timestamps: true,
      indexes: [
        {
          name: 'lessons_name_index',
          fields: [{name: 'name'}],
        },
        {
          name: 'lessons_pkey',
          unique: true,
          fields: [{name: 'id'}],
        },
      ],
    },
  );
};
