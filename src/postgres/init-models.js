const DataTypes = require('sequelize').DataTypes;

const _courses = require('./courses');
const _creators = require('./creators');
const _lessons = require('./lessons');
const _courseCategories = require('./course-categories');
const _questions = require('./questions');
const _quiz = require('./quiz');
const _quizResult = require('./quiz-result');

const db = require('./');
const sequelize = db.sequelize;

function initModels() {
  const courses = _courses(sequelize, DataTypes);
  const creators = _creators(sequelize, DataTypes);
  const lessons = _lessons(sequelize, DataTypes);
  const courseCategories = _courseCategories(sequelize, DataTypes);
  const questions = _questions(sequelize, DataTypes);
  const quiz = _quiz(sequelize, DataTypes);
  const quizResult = _quizResult(sequelize, DataTypes);

  courses.belongsTo(creators, {as: 'creator', foreignKey: 'creatorId'});
  creators.hasMany(courses, {as: 'courses', foreignKey: 'creatorId'});
  courses.belongsTo(courseCategories, {
    as: 'category',
    foreignKey: 'categoryId',
  });
  courseCategories.hasMany(courses, {as: 'courses', foreignKey: 'categoryId'});
  lessons.belongsTo(courses, {as: 'course', foreignKey: 'courseId'});
  courses.hasMany(lessons, {as: 'lessons', foreignKey: 'courseId'});
  quiz.belongsTo(courses, {as: 'course', foreignKey: 'courseId'});
  courses.hasMany(quiz, {as: 'quizzes', foreignKey: 'courseId'});
  quiz.hasMany(questions, {as: 'questions', foreignKey: 'quizId'});
  questions.belongsTo(quiz, {as: 'quiz', foreignKey: 'quizId'});
  quizResult.belongsTo(quiz, {as: 'quiz', foreignKey: 'quizId'});
  quiz.hasMany(quizResult, {as: 'results', foreignKey: 'quizId'});

  return {
    Courses: courses,
    Creators: creators,
    Lessons: lessons,
    CourseCategories: courseCategories,
    Questions: questions,
    Quiz: quiz,
    QuizResult: quizResult,
  };
}

// module.exports = initModels;
module.exports.initModels = initModels;
module.exports.default = initModels;

module.exports = initModels();
