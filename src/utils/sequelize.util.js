const Sequelize = require('sequelize');

const ORDER_DEFAULT = ['createdAt', 'DESC'];
const DOT = '.';

const buildPagingParams = (data, limitDefault = 10) => {
  const limit = data?.limit || limitDefault;
  const pageNumber = data?.page || 1;
  const offset = limit * (pageNumber - 1);

  return {limit: Number(limit), offset};
};

const buildFullTextSearchParams = (data, fields = []) => {
  if (fields.length === 0) {
    return {};
  }

  const fullTextSearch = {};
  for (const field of fields) {
    let fieldData = field;
    if (field.includes(DOT)) {
      const fieldDataSplit = field.split(DOT);
      fieldData = fieldDataSplit[fieldDataSplit.length - 1];
    }
    if (!(data || {})[fieldData]) {
      continue;
    }
    fullTextSearch[field] = Sequelize.where(
      Sequelize.fn('LOWER', Sequelize.col(field)),
      'LIKE',
      `%${data[fieldData].trim().toLowerCase()}%`,
    );
  }

  return fullTextSearch;
};

const buildOrderParams = (data) => {
  let orderBy = ORDER_DEFAULT;
  if (!data?.orderBy) {
    return [ORDER_DEFAULT];
  }
  const splitFields = data.orderBy.split('-');
  if (splitFields.length !== 2) {
    return [ORDER_DEFAULT];
  }

  orderBy = [splitFields[0], splitFields[1]];

  return orderBy[0] !== 'createdAt' ? [orderBy, ORDER_DEFAULT] : [orderBy];
};

const trimSql = (sql) => {
  return sql
    .trim()
    .replace(/(\r\n|\n|\r)/g, '')
    .replace(/\s+/g, ' ');
};

const build = (data, fields) => {
  const {limit, offset} = buildPagingParams(data);
  const params = buildFullTextSearchParams(data, fields);
  const orderBy = buildOrderParams(data);

  const conditions = {};
  const otherParams = Object.keys(data || []).filter(
    (key) => ![...fields, 'orderBy', 'limit', 'page'].includes(key),
  );
  otherParams.forEach((key) => {
    conditions[key] = data[key];
  });

  return {
    limit,
    offset,
    params: {...params, ...conditions},
    orderBy,
  };
};

module.exports.SequelizeUtil = {
  buildPagingParams,
  buildFullTextSearchParams,
  buildOrderParams,
  trimSql,
  build,
};
