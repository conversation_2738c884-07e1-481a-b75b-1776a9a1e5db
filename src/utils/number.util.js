module.exports.convertNumberToPrice = (
  priceNumber,
  withoutVND = false,
  withoutCommaDelimiter = false,
) => {
  if (!priceNumber) {
    return `0${!withoutVND ? 'đ' : ''}`;
  }

  const commaDelimiter = !withoutCommaDelimiter ? ',' : '';
  try {
    const prevText = priceNumber.toString();
    let result = '';
    let count = 0;
    for (let i = prevText.length - 1; i >= 0; i--) {
      result = prevText[i] + result;
      ++count;
      if (count === 3 && i > 0) {
        if (prevText[i - 1] !== '-') {
          result = commaDelimiter + result;
          count = 0;
        }
      }
    }
    return `${result}${!withoutVND ? ' đ' : ''}`;
  } catch (error) {
    return '';
  }
};
