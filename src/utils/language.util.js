const mappingValueByLang = (data, curentLang) => {
  if (!data) {
    return null;
  }
  const keys = Object.keys(data);
  const obj = {};
  for (const key of keys) {
    let value = data[key];
    if (typeof value === 'boolean' || typeof value === 'number') {
      obj[key] = value;
      continue;
    }
    try {
      value = JSON.parse(data[key]);
      obj[key] = value[curentLang];
    } catch (_) {
      obj[key] = value;
    }
  }
  return obj;
};

const getLangFromRequest = (event) => {
  // console.log('getLangFromRequest :: event: ', event);
  const DEFAULT_LANGUAGE = 'vi';
  if (event?.headers?.lang) {
    return event.headers.lang;
  }
  if (!event?.cookies) {
    return DEFAULT_LANGUAGE;
  }

  const cookies = {};
  for (const item of event.cookies) {
    const parts = item.split('=');
    const key = parts.shift().trim();
    const value = decodeURI(parts.join('='));
    if (!!key) {
      cookies[key] = value;
    }
  }

  return cookies.i18next || cookies.lang || DEFAULT_LANGUAGE;
};

module.exports.LanguageUtil = {mappingValueByLang, getLangFromRequest};
