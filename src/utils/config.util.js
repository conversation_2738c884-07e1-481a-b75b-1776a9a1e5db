const {orderBy} = require('lodash');
const ConfigRepository = require('../repository/config');
const {getSectionItems, getCourses} = require('../repository/blog');
const {CONFIG_MODEL_SKEY} = require('../common/types/config.type');

const getContentByConfig = async (pageName) => {
  const configs = await ConfigRepository.getPageConfig(pageName);
  const sections = [];

  for (const config of configs) {
    let content = [];

    switch (config.position) {
      case CONFIG_MODEL_SKEY.BUTTON:
        break;
      case CONFIG_MODEL_SKEY.SECTION_COURSE:
        const items = orderBy(config.items, ['position'], ['asc']);
        content = await getCourses(items);
        break;
      default:
        content = await getSectionItems(config.items);
        break;
    }

    delete config.items;

    sections.push({
      ...config,
      content,
    });
  }

  return sections;
};

module.exports = {
  getContentByConfig,
};
