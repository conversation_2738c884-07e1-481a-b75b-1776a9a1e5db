const ResponseBuilder = {
  ok(payload) {
    const successMessage = payload?.successMessage || 'OK';
    delete payload?.successMessage;

    const body = {
      errorCode: 0,
      message: successMessage,
      data: payload,
    };

    return body;
  },

  badRequest({code, message, ...remainProps}) {
    const body = {
      errorCode: code,
      message,
      data: remainProps || null,
    };
    return body;
  },

  internalServerError(payload) {
    const body = {
      errorCode: 100500,
      message: 'Đã có lỗi xảy ra',
      data: {
        message: payload.message,
        stack: payload.stack,
      },
    };

    return body;
  },

  permissionDeined() {
    return {
      statusCode: 403,
      body: JSON.stringify({
        message: 'Permission denied',
      }),
    };
  },

  unauthorized() {
    return {
      statusCode: 401,
      body: JSON.stringify({
        message: 'Unauthorized',
      }),
    };
  },
};

module.exports = ResponseBuilder;
