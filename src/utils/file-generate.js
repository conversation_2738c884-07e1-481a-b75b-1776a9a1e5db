const {getFileBuffer, updateFileObject} = require('./s3.utils');
const PizZip = require('pizzip');
const Docxtemplater = require('docxtemplater');
const UserGender = require('../common/types/user-gender');

const generateStudentCertificate = async (user) => {
  const templateFile = await getFileBuffer(`${process.env.UPLOAD_PATH}/cert-template.docx`);

  const zip = new PizZip(templateFile);

  const doc = new Docxtemplater(zip, {
    paragraphLoop: true,
    linebreaks: true,
  });

  try {
    doc.render({
      name: user.name || '',
      birthday: user.birthday || '',
      gender: UserGender.getName(user.gender),
      icCard: user.icCard || '',
      phone: user.phone || '',
      email: user.email || '',
      university1: user.university || '',
      university2: user.university || '',
      major: user.major || '',
      studentId: user.studentId || '',
      schoolYear: user.schoolYear || '',
    });

    const buffer = doc.getZip().generate({type: 'nodebuffer'});
    const params = {
      Body: buffer,
      ContentType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    };
    const filename = `${user.name}-${Date.now()}.docx`;
    const key = `${process.env.CLIENT_UPLOAD_PATH}/${user.identityId}/cert/${filename}`;
    await updateFileObject(params, key);
    return {
      filename,
      filepath: `${process.env.MEDIA_DOMAIN}/${key}`,
    };
  } catch (error) {
    console.error(error);
  }
};

module.exports = {
  generateStudentCertificate,
};
