const _ = require('lodash');

module.exports.isEmpty = (value) => {
  if (typeof value === 'number' || _.isBoolean(value)) {
    return false;
  }
  return _.isEmpty(value) || (typeof value === 'string' && _.isEmpty(value.trim()));
};

module.exports.removeVietnameseTones = (str) => {
  if (!str) {
    return '';
  }
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
  str = str.replace(/đ/g, 'd');
  str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, 'A');
  str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, 'E');
  str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, 'I');
  str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, 'O');
  str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, 'U');
  str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, 'Y');
  str = str.replace(/Đ/g, 'D');
  // Some system encode vietnamese combining accent as individual utf-8 characters
  // Một vài bộ encode coi các dấu mũ, dấu chữ như một kí tự riêng biệt nên thêm hai dòng này
  str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ''); // ̀ ́ ̃ ̉ ̣  huyền, sắc, ngã, hỏi, nặng
  str = str.replace(/\u02C6|\u0306|\u031B/g, ''); // ˆ ̆ ̛  Â, Ê, Ă, Ơ, Ư
  // Remove extra spaces
  // Bỏ các khoảng trắng liền nhau
  str = str.replace(/ + /g, ' ');
  str = str.trim();
  // Remove punctuations
  // Bỏ dấu câu, kí tự đặc biệt
  str = str.replace(
    /!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|`|{|}|\||\\/g,
    ' ',
  );
  return str;
};

module.exports.getLangFromRequest = (event) => {
  // console.log('getLangFromRequest :: event: ', event);
  const DEFAULT_LANGUAGE = 'vi';
  if (event?.headers?.lang) {
    return event.headers.lang;
  }
  if (!event?.cookies) {
    return DEFAULT_LANGUAGE;
  }

  const cookies = {};
  for (const item of event.cookies) {
    const parts = item.split('=');
    const key = parts.shift().trim();
    const value = decodeURI(parts.join('='));
    if (!!key) {
      cookies[key] = value;
    }
  }

  return cookies.i18next || cookies.lang || DEFAULT_LANGUAGE;
};

/**
 * [Recursively parses a stringified JSON]
 * @param  {[type]} jsonString [stringified json to parse]
 * @return {[type]}            [normalized Javascript object]
 */

const isNumString = (str) => !isNaN(Number(str));

function deepParseJson(jsonString) {
  // if not stringified json rather a simple string value then JSON.parse will throw error
  // otherwise continue recursion
  if (typeof jsonString === 'string') {
    if (isNumString(jsonString)) {
      // if a numeric string is received, return itself
      // otherwise JSON.parse will convert it to a number
      return jsonString;
    }
    try {
      return deepParseJson(JSON.parse(jsonString));
    } catch (err) {
      return jsonString;
    }
  } else if (Array.isArray(jsonString)) {
    // if an array is received, map over the array and deepParse each value
    return jsonString.map((val) => deepParseJson(val));
  } else if (typeof jsonString === 'object' && jsonString !== null) {
    // if an object is received then deepParse each element in the object
    // typeof null returns 'object' too, so we have to eliminate that
    return Object.keys(jsonString).reduce((obj, key) => {
      const val = jsonString[key];
      obj[key] = isNumString(val) ? val : deepParseJson(val);
      return obj;
    }, {});
  } else {
    // otherwise return whatever was received
    return jsonString;
  }
}

module.exports.deepParseJson = deepParseJson;

module.exports.jsonToBase64 = (object) => {
  if (!object) {
    return null;
  }
  const json = JSON.stringify(object);
  return Buffer.from(json).toString('base64');
};

module.exports.base64ToJson = (base64String) => {
  const json = Buffer.from(base64String, 'base64').toString();
  return JSON.parse(json);
};
