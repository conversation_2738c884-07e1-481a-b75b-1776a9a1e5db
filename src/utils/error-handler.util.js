const {BaseError, PermissionDeniedError, UnauthorizationError} = require('../common/exceptions');
const ResponseBuilder = require('./response-builder.util');

module.exports.errorHandler = (data) => {
  if (data instanceof BaseError) {
    return ResponseBuilder.badRequest(data.getError());
  }

  if (data instanceof PermissionDeniedError) {
    return ResponseBuilder.permissionDeined();
  }

  if (data instanceof UnauthorizationError) {
    return ResponseBuilder.unauthorized();
  }

  return ResponseBuilder.internalServerError(data);
};
