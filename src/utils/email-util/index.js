const nodemailer = require('nodemailer');
const fs = require('fs');
const Handlebars = require('handlebars');
const {getSecretValue} = require('../../common/secret-manager');
const aws = require('@aws-sdk/client-ses');
// const {shouldSendEmail} = require('./email-checker');

const sendEmailMatBao = async (payload) => {
  try {
    const {host, user, password} = await getSecretValue('MailService');
    const transporter = nodemailer.createTransport({
      host,
      port: 465,
      secure: true,
      auth: {
        user,
        pass: password,
      },
    });

    const source = fs.readFileSync(__dirname + `/${payload.template}.html`, 'utf8');

    const template = Handlebars.compile(source);

    const data = {
      from: `Tre Center <${user}>`,
      to: payload.to,
      subject: payload.subject,
      html: template(payload.context),
    };

    if (payload.attachments) data.attachments = payload.attachments;

    const response = await transporter.sendMail(data);

    return {
      success: true,
      response,
      payload,
    };
  } catch (error) {
    console.log('sendEmail :: error: ', error.message);
    return {
      success: false,
      payload,
    };
  }
};

const getSESClient = (config) => {
  const sesClient = new aws.SES({
    credentials: {
      accessKeyId: config.accessKey,
      secretAccessKey: config.secretAccessKey,
    },
  });

  return sesClient;
};

// eslint-disable-next-line no-unused-vars
const sendEmailSES = async (payload) => {
  try {
    const config = await getSecretValue('SESConfig');
    const transporter = nodemailer.createTransport({
      SES: {ses: getSESClient(config), aws},
    });

    const source = fs.readFileSync(__dirname + `/${payload.template}.html`, 'utf8');

    const template = Handlebars.compile(source);

    const data = {
      from: `Tre Center <${config.senderEmail}>`,
      to: payload.to,
      subject: payload.subject,
      html: template(payload.context),
    };

    if (payload.attachments) data.attachments = payload.attachments;

    const response = await transporter.sendMail(data);

    return {
      success: true,
      response,
      payload,
    };
  } catch (error) {
    console.log('sendSESEmail :: error: ', error.message);
    return {
      success: false,
      payload,
    };
  }
};

module.exports.sendEmail = async (payload) => {
  // const validEmail = await shouldSendEmail(payload.to);
  // if (!validEmail) return false;
  const result = await sendEmailMatBao(payload);

  // if (process.env.ENV === 'prod') {
  //   result = await sendEmailSES(payload);
  // } else {
  //   result = await sendEmailMatBao(payload);
  // }

  console.log('Send email result: ', result);
};
