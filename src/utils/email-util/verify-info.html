<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="und" dir="auto" xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <style type="text/css">
      @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600');
    </style>

    <title></title>

    <!--[if (!mso)&(!ie)]>These<!-- -->
    <!--<![endif]-->
    <!--[if (!mso)&(!ie)]>are<!-- -->
    <!--<![endif]-->
    <!--[if (!mso)&(!ie)]>for<!-- -->
    <!--<![endif]-->
    <!--[if (!mso)&(!ie)]>outlook<!-- -->
    <!--<![endif]-->
    <!--[if (!mso)&(!ie)]>live<!-- -->
    <!--<![endif]-->
    <!--[if (!mso)&(!ie)]>that<!-- -->
    <!--<![endif]-->
    <!--[if (!mso)&(!ie)]>removes<!-- -->
    <!--<![endif]-->
    <!--[if (!mso)&(!ie)]>the first<!-- -->
    <!--<![endif]-->
    <!--[if (!mso)&(!ie)]>10 well-formed<!-- -->
    <!--<![endif]-->
    <!--[if (!mso)&(!ie)]>conditional comments<!-- -->
    <!--<![endif]-->
    <!--[if gte mso 9]>
      <xml>
        <o:OfficeDocumentSettings xmlns:o="urn:schemas-microsoft-com:office:office">
          <o:AllowPNG />
          <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
      </xml>
    <![endif]-->

    <style type="text/css">
      .dark-mode .bg-fffffe {
        background-color: #fffffe !important;
      }
      .dark-mode .color-313136 {
        color: #313136 !important;
      }
      .dark-mode .color-fffffe {
        color: #fffffe !important;
      }
      .dark-mode .color-94949e {
        color: #94949e !important;
      }
      .dark-mode .color-007aff {
        color: #007aff !important;
      }

      @media (prefers-color-scheme: dark) {
        html:not(.light-mode) .bg-fffffe {
          background-color: #fffffe !important;
        }
        html:not(.light-mode) .color-313136 {
          color: #313136 !important;
        }
        html:not(.light-mode) .color-fffffe {
          color: #fffffe !important;
        }
        html:not(.light-mode) .color-94949e {
          color: #94949e !important;
        }
        html:not(.light-mode) .color-007aff {
          color: #007aff !important;
        }
      }

      [data-ogsc] .bg-fffffe {
        background-color: #fffffe !important;
      }
      [data-ogsc] .color-313136 {
        color: #313136 !important;
      }
      [data-ogsc] .color-fffffe {
        color: #fffffe !important;
      }
      [data-ogsc] .color-94949e {
        color: #94949e !important;
      }
      [data-ogsc] .color-007aff {
        color: #007aff !important;
      }
    </style>

    <meta name="color-scheme" content="light dark" />

    <meta name="supported-color-schemes" content="light dark" />

    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

    <!--[if !mso]><!-->
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!--<![endif]-->

    <meta name="x-apple-disable-message-reformatting" />

    <style>
      table,
      tr,
      td {
        border-collapse: separate;
      }
      .kombai-text-klaviyo-block table tbody tr td {
        padding: 0 !important;
      }
      a[data-has-kombai-klaviyo-block] {
        pointer-events: all !important;
      }
      a[data-klaviyo-block-child] {
        pointer-events: all !important;
        vertical-align: initial !important;
      }
    </style>

    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <style type="text/css">
      u + div .kombai-email-compat__list-with-padding-left {
        padding-left: 0.5em !important;
      }
    </style>

    <!--[if mso]>
      <style type="text/css">
        v\:* {
          behavior: url(#default#VML);
          display: inline-block;
        }
        o\:* {
          behavior: url(#default#VML);
          display: inline-block;
        }
        w\:* {
          behavior: url(#default#VML);
          display: inline-block;
        }
        .ExternalClass {
          width: 100%;
        }
        table {
          mso-table-lspace: 0pt;
          mso-table-rspace: 0pt;
        }
        img {
          -ms-interpolation-mode: bicubic;
        }
        .ReadMsgBody {
          width: 100%;
        }
        a {
          background: transparent !important;
          background-color: transparent !important;
        }

        li {
          text-align: -webkit-match-parent;
          display: list-item;
          text-indent: -1em;
        }

        ul,
        ol {
          margin-left: 1em !important;
        }

        p {
          text-indent: 0;
        }
      </style>
    <![endif]-->
  </head>
  <body style="margin: 0; padding: 0">
    <div style="font-size: 0px; line-height: 1px; mso-line-height-rule: exactly; display: none; max-width: 0px; max-height: 0px; opacity: 0; overflow: hidden; mso-hide: all"></div>
    <center lang="und" dir="auto" style="width: 100%; table-layout: fixed; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%">
      <table
        class="bg-fffffe"
        cellpadding="0"
        cellspacing="0"
        border="0"
        role="presentation"
        bgcolor="white"
        width="640.00"
        style="background-color: white; width: 640px; border-spacing: 0; font-family: Inter, Tahoma, sans-serif; min-width: 640px"
      >
        <tr>
          <td valign="top" width="100.00%" style="padding-top: 44px; padding-bottom: 32px; width: 100%">
            <table cellpadding="0" cellspacing="0" border="0" role="presentation" width="100.00%" style="width: 100%; border-spacing: 0">
              <tr>
                <td style="padding-top: 10px; padding-bottom: 13.64px; padding-left: 24px; padding-right: 24px">
                  <!--[if mso]> <table role="presentation" border="0" cellspacing="0" cellpadding="0" width="592" style="width:592px;"> <tr> <td style="height:0.5px;" height="0.5px"> <![endif]-->
                  <div bgcolor="#808089" height="0.5" width="592" style="background-color: #808089; height: 0.5px; width: 592px; margin-top: -0.5px"></div>
                  <!--[if mso]></td></tr></table><![endif]-->
                </td>
              </tr>
              <tr>
                <td align="left" style="padding-top: 13.64px; padding-bottom: 12.36px; padding-left: 33.45px">
                  <span style="font-size: 22px; font-weight: 600; letter-spacing: -0.34px; text-align: left; line-height: 25.5px; mso-line-height-rule: exactly; color: #313136">
                    Xác nhận thông tin
                  </span>
                </td>
              </tr>
              <tr>
                <td align="left" style="padding-top: 12.36px; padding-bottom: 12px; padding-left: 31px; padding-right: 31px">
                  <p
                    class="color-313136"
                    width="100.00%"
                    style="
                      font-size: 17px;
                      font-weight: 400;
                      letter-spacing: -0.34px;
                      text-align: left;
                      line-height: 25.5px;
                      color: #313136;
                      mso-line-height-rule: exactly;
                      margin: 0;
                      padding: 0;
                      width: 100%;
                    "
                  >
                      <span style="line-height: 17px; display: block; mso-line-height-rule: exactly"><br /></span>
                      <span>Cảm ơn vì đã tham gia Trecenter,</span>
                      <span style="line-height: 17px; display: block; mso-line-height-rule: exactly"><br /></span>
                      <span>Hãy kiểm tra thông tin của bạn trong file đính kèm</span>
                  </p>
                </td>
              </tr>
              <tr>
                <td style="padding-top: 11.75px; padding-bottom: 11.75px; padding-left: 31.5px; padding-right: 31.5px">
                  <!--[if mso]> <table role="presentation" border="0" cellspacing="0" cellpadding="0" width="577" style="width:577px; border-top:1px solid #dedede;"> <tr> <td> <![endif]-->
                  <div width="577" style="border-top: 1px solid #dedede; width: 577px; mso-border-top-alt: none"></div>
                  <!--[if mso]></td></tr></table><![endif]-->
                </td>
              </tr>
              <tr>
                <td align="left" style="padding-top: 11.75px; padding-bottom: 8px; padding-left: 31px">
                  <p
                    class="color-313136"
                    style="font-size: 19.13px; font-weight: 600; letter-spacing: -0.38px; line-height: 23px; color: #313136; mso-line-height-rule: exactly; margin: 0; padding: 0"
                  >
                    Hỗ trợ
                  </p>
                </td>
              </tr>
              <tr>
                <td align="left" style="padding-top: 8px; padding-bottom: 4px; padding-left: 31px">
                  <p class="color-94949e" style="font-size: 15.11px; font-weight: 400; letter-spacing: -0.3px; color: #94949e; margin: 0; padding: 0; line-height: 23px; mso-line-height-rule: exactly">
                    Email: <EMAIL>
                  </p>
                </td>
              </tr>
              <tr>
                <td align="left" style="padding-top: 4px; padding-bottom: 4px; padding-left: 31px">
                  <p class="color-94949e" style="font-size: 15.11px; font-weight: 400; letter-spacing: -0.3px; color: #94949e; margin: 0; padding: 0; line-height: 23px; mso-line-height-rule: exactly">
                    <span style="line-height: 15.11px; display: block; mso-line-height-rule: exactly"><br /></span
                    ><span style="font-size: 15.11px; font-weight: 400; letter-spacing: -0.3px; text-align: left">Website: </span>
                    <span class="color-94949e" style="font-size: 15.11px; font-weight: 400; color: #94949e; letter-spacing: -0.3px; text-align: left">https://trecenter.vn</span>
                  </p>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    </center>
  </body>
</html>
