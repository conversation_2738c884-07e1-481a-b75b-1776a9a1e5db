const {getFileObject, getFileTxt, updateFileObject} = require('../s3.utils');

const sendCountsKey = `${process.env.UPLOAD_PATH}/sendCounts.json`;
const blacklistKey = `${process.env.UPLOAD_PATH}/blacklist.txt`;
const domainBlacklistKey = `${process.env.UPLOAD_PATH}/domainBlacklist.txt`;
const domainWhitelist = [
  'gmail.com',
  'hotmail.com',
  'outlook.com',
  'eventista.vn',
  'yahoo.com',
  'icloud.com',
];
const limitSendCount = 30;

const shouldSendEmail = async (email) => {
  const domain = email.split('@')[1];
  const blacklist = await getFileTxt(blacklistKey);
  const domainBlacklist = await getFileTxt(domainBlacklistKey);

  // Email nằm trong blacklist
  if (blacklist && blacklist.includes(email)) return false;
  // Domain nằm trong whitelist
  if (domainWhitelist.includes(domain)) return true;
  // Domain nằm trong blacklist
  if (domainBlacklist && domainBlacklist.includes(domain)) return false;

  // Email không nằm trong blacklist lẫn whitelist
  const counts = await getFileObject(sendCountsKey);
  if (!counts[domain] || counts[domain] < limitSendCount) {
    // Kiểm tra nếu domain chưa tồn tại thì khởi tạo
    if (!counts[domain]) counts[domain] = 1;
    else counts[domain] += 1;

    const data = {
      Body: JSON.stringify(counts, null, 2),
      ContentType: 'application/json',
    };

    await updateFileObject(data, sendCountsKey);
    console.log(`${domain} đã cập nhật số lần gửi email.`);

    return true;
  }

  console.log(`Domain ${domain} đã gửi quá ${limitSendCount} lần.`);
  return false;
};

module.exports = {
  shouldSendEmail,
};
