<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html
  lang="und"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:v="urn:schemas-microsoft-com:vml"
  xmlns:o="urn:schemas-microsoft-com:office:office"
  xmlns:w="urn:schemas-microsoft-com:office:word"
>
  <head>
    <!--[if (!mso)&(!ie)]>These<!-- -->
    <!--<![endif]-->
    <!--[if (!mso)&(!ie)]>are<!-- -->
    <!--<![endif]-->
    <!--[if (!mso)&(!ie)]>for<!-- -->
    <!--<![endif]-->
    <!--[if (!mso)&(!ie)]>outlook<!-- -->
    <!--<![endif]-->
    <!--[if (!mso)&(!ie)]>live<!-- -->
    <!--<![endif]-->
    <!--[if (!mso)&(!ie)]>that<!-- -->
    <!--<![endif]-->
    <!--[if (!mso)&(!ie)]>removes<!-- -->
    <!--<![endif]-->
    <!--[if (!mso)&(!ie)]>the first<!-- -->
    <!--<![endif]-->
    <!--[if (!mso)&(!ie)]>10 well-formed<!-- -->
    <!--<![endif]-->
    <!--[if (!mso)&(!ie)]>conditional comments<!-- -->
    <!--<![endif]-->
    <!--[if gte mso 9]>
      <xml>
        <o:OfficeDocumentSettings>
          <o:AllowPNG />
          <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
      </xml>
    <![endif]-->

    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600"
    />

    <title></title>

    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />

    <!--[if !mso]><!-->
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!--<![endif]-->

    <meta name="x-apple-disable-message-reformatting" />

    <style></style>

    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <style type="text/css">
      v\:* {
        behavior: url(#default#VML);
        display: inline-block;
      }
      o\:* {
        behavior: url(#default#VML);
        display: inline-block;
      }
      w\:* {
        behavior: url(#default#VML);
        display: inline-block;
      }
      .ExternalClass {
        width: 100%;
      }
      table {
        mso-table-lspace: 0pt;
        mso-table-rspace: 0pt;
      }
      img {
        -ms-interpolation-mode: bicubic;
      }
      .ReadMsgBody {
        width: 100%;
      }
    </style>
  </head>
  <body style="margin: 0; padding: 0">
    <div
      style="
        font-size: 0px;
        line-height: 1px;
        mso-line-height-rule: exactly;
        display: none;
        max-width: 0px;
        max-height: 0px;
        opacity: 0;
        overflow: hidden;
        mso-hide: all;
      "
    ></div>
    <center
      lang="und"
      dir="auto"
      style="
        width: 100%;
        table-layout: fixed;
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
        padding: 16px 0;
      "
    >
      <table
        cellpadding="0"
        cellspacing="0"
        border="0"
        role="presentation"
        bgcolor="white"
        width="622"
        style="
          background-color: white;
          width: 622px;
          border-spacing: 0;
          font-family: Inter, sans-serif;
          min-width: 622px;
        "
      >
        <tr>
          <td
            valign="top"
            class="force-w100"
            width="100.00%"
            style="
              padding-top: 34px;
              padding-bottom: 28px;
              width: 100%;
              box-shadow: rgba(17, 17, 26, 0.05) 0px 1px 0px,
                rgba(17, 17, 26, 0.1) 0px 0px 8px;
              border-radius: 5px;
            "
          >
            <table
              class="force-w100"
              cellpadding="0"
              cellspacing="0"
              border="0"
              role="presentation"
              width="100.00%"
              style="width: 100%; border-spacing: 0"
            >
              <tr>
                <td
                  align="left"
                  valign="top"
                  height="89"
                  style="padding-left: 28px; padding-right: 360px; height: 89px"
                >
                  <img
                    src="https://media-platform.1vote.vn/logo/eventista.jpg"
                    width="234"
                    style="
                      border: none;
                      max-width: initial;
                      object-fit: contain;
                      width: 234px;
                      display: block;
                    "
                  />
                </td>
              </tr>
              <tr>
                <td
                  align="center"
                  style="padding-top: 16px; padding-bottom: 13.5px"
                >
                  <table
                    cellpadding="0"
                    cellspacing="0"
                    border="0"
                    role="presentation"
                    bgcolor="white"
                    width="528"
                    height="275"
                    style="
                      border-radius: 8px;
                      border: 1px solid #bdccdb;
                      backdrop-filter: blur(12px);
                      background-color: white;
                      width: 528px;
                      height: 275px;
                      border-spacing: 0;
                      border-collapse: separate !important;
                    "
                  >
                    <tr>
                      <td
                        valign="top"
                        class="force-w100"
                        width="100.00%"
                        style="padding-top: 32px; width: 100%"
                      >
                        <table
                          class="force-w100"
                          cellpadding="0"
                          cellspacing="0"
                          border="0"
                          role="presentation"
                          width="100.00%"
                          style="width: 100%; border-spacing: 0"
                        >
                          <tr>
                            <td align="center" style="padding-bottom: 8px">
                              <!--[if mso]> <table role="presentation" border="0" cellspacing="0" cellpadding="0" align="center" width="182" style="width:182px;"> <tr> <td> <![endif]-->
                              <p
                                width="182"
                                style="
                                  font-size: 21.52px;
                                  font-weight: 600;
                                  letter-spacing: -0.43px;
                                  text-align: center;
                                  line-height: 26px;
                                  color: #313136;
                                  mso-line-height-rule: exactly;
                                  margin: 0;
                                  padding: 0;
                                  width: 182px;
                                "
                              >
                                Đặt lại mật khẩu
                              </p>
                              <!--[if mso]></td></tr></table><![endif]-->
                            </td>
                          </tr>
                          <tr>
                            <td
                              align="left"
                              style="
                                padding-top: 8px;
                                padding-bottom: 6px;
                                padding-left: 15px;
                                padding-right: 15px;
                              "
                            >
                              <!--[if mso]> <table role="presentation" border="0" cellspacing="0" cellpadding="0" align="center" width="500" height="121" style="width:500px; height:121px;"> <tr> <td> <![endif]-->
                              <p
                                width="500"
                                height="121"
                                style="
                                  font-size: 17px;
                                  font-weight: 400;
                                  letter-spacing: -0.34px;
                                  text-align: left;
                                  line-height: 25.5px;
                                  color: #313136;
                                  mso-line-height-rule: exactly;
                                  margin: 0;
                                  padding: 0;
                                  width: 500px;
                                  height: 121px;
                                "
                              >
                                <span
                                  >Bạn đã quên mật khẩu cho tài khoản Eventista. Nếu
                                  đúng, xin hãy nhấp vào đường link bên dưới để
                                  thiết lập lại mật khẩu. </span
                                ><span
                                  style="
                                    line-height: 17px;
                                    display: block;
                                    mso-line-height-rule: exactly;
                                  "
                                  ><br /></span
                                ><span
                                  >Nếu bạn không yêu cầu thiết lập lại mẩu khẩu,
                                  vui lòng bỏ qua email này.</span
                                >
                              </p>
                              <!--[if mso]></td></tr></table><![endif]-->
                            </td>
                          </tr>
                          <tr>
                            <td align="center" style="padding-top: 6px">
                              <!-- Button Component is detected here -->
                              <!--[if mso]> <table role="presentation" border="0" cellspacing="0" cellpadding="0" align="center" width="498" style="width:498px;"> <tr> <td> <![endif]-->
                              <div
                                role="button"
                                width="498"
                                style="width: 498px"
                              >
                                <!--[if mso]> <v:roundrect alt="button" rel="noopener" target="_blank" href="#" style="height:54.00px;v-text-anchor:middle;width:498.00px;" arcsize="29.63%" stroke="t" strokeweight="none" strokecolor="undefined" fillcolor="white"> <w:anchorlock/> <center> <![endif]-->
                                <a
                                  href="{{url}}"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  bgcolor="white"
                                  style="
                                    background-color: white;
                                    border: none;
                                    border-radius: 16px;
                                    background: linear-gradient(
                                        180deg,
                                        rgba(255, 255, 255, 0.2) 0%,
                                        rgba(255, 255, 255, 0) 100%
                                      ),
                                      linear-gradient(
                                        180deg,
                                        rgba(0, 0, 0, 0) 0%,
                                        rgba(0, 0, 0, 0.2) 100%
                                      ),
                                      #cc3367;
                                    box-shadow: 0px 12px 12px rgba(0, 0, 0, 0.1);
                                    cursor: pointer;
                                    min-width: 172px;
                                    display: block;
                                    text-align: center;
                                    text-decoration: none;
                                    mso-border-alt: none;
                                  "
                                  ><span
                                    style="
                                      font-size: 18px;
                                      font-weight: 600;
                                      letter-spacing: -0.07px;
                                      text-transform: uppercase;
                                      color: white;
                                      line-height: 54px;
                                      mso-line-height-rule: exactly;
                                    "
                                    >Tạo mật khẩu</span
                                  ></a
                                >
                                <!--[if mso]> </center> </v:roundrect> <![endif]-->
                              </div>
                              <!--[if mso]></td></tr></table><![endif]-->
                            </td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
              <tr>
                <td align="center" style="padding-top: 13.5px">
                  <table
                    cellpadding="0"
                    cellspacing="0"
                    border="0"
                    role="presentation"
                    style="border-spacing: 0"
                  >
                    <tr>
                      <td width="141" style="width: 141px">
                        <p
                          style="
                            font-size: 17px;
                            font-weight: 400;
                            letter-spacing: -0.34px;
                            color: #302cff;
                            margin: 0;
                            padding: 0;
                            line-height: 20px;
                            mso-line-height-rule: exactly;
                          "
                        >
                          <a href="https://eventistax.com" target="_blank">
                            Về Eventista
                          </a>
                        </p>
                      </td>
                      <td style="padding-left: 8px">
                        <p
                          style="
                            font-size: 17px;
                            font-weight: 400;
                            letter-spacing: -0.34px;
                            color: #302cff;
                            margin: 0;
                            padding: 0;
                            line-height: 20px;
                            mso-line-height-rule: exactly;
                          "
                        >
                          <a
                            href="https://eventistax.com/term-of-use/"
                            target="_blank"
                            >Điều khoản sử dụng</a
                          >
                        </p>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    </center>
  </body>
</html>
