const XLSX = require('xlsx-js-style');

const jsonToExcel = (jsonData) => {
  // Create a new workbook
  const workbook = XLSX.utils.book_new();

  // Convert JSON data to a worksheet
  const worksheet = XLSX.utils.json_to_sheet(jsonData);

  // Add the worksheet to the workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

  // Write the workbook to a binary string
  return XLSX.write(workbook, {bookType: 'xlsx', type: 'buffer'});
};

const drawBorder = (sheet, fromRow = undefined, fromCol = undefined, style = undefined) => {
  const range = XLSX.utils.decode_range(sheet['!ref']); // Get the range of the worksheet
  const borderStyle = ['top', 'bottom', 'left', 'right'].reduce((styles, edge) => {
    return {
      ...styles,
      [edge]: style || {
        style: 'thin',
        color: {rgb: 'black'},
      },
    };
  }, {});

  for (let row = fromRow || range.s.r; row <= range.e.r; row++) {
    // Loop through all rows
    for (let col = fromCol || range.s.c; col <= range.e.c; col++) {
      // Loop through all columns
      const cellRef = XLSX.utils.encode_cell({r: row, c: col}); // Get cell reference (e.g., 'A1')

      // Add the style to the cell
      if (!sheet[cellRef]) sheet[cellRef] = {}; // Ensure cell exists
      sheet[cellRef].s = {
        ...sheet[cellRef].s,
        border: {
          ...borderStyle,
        },
      };
    }
  }
};

const setDefaultColumnsWidth = (sheet, width) => {
  const range = XLSX.utils.decode_range(sheet['!ref']); // Get the range of the worksheet
  for (let col = range.s.c; col <= range.e.c; col++) {
    sheet['!cols'] = [{wch: width}];
  }

  sheet['!cols'] = new Array(range.e.c + 1).fill({wch: width});
};

module.exports = {
  jsonToExcel,
  drawBorder,
  setDefaultColumnsWidth,
};
