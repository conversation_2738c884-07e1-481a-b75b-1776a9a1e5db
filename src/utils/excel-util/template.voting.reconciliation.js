const XLSX = require('xlsx-js-style');
const {PaymentGateway, PaymentFlatFee, PaymentTxFee} = require('./../../constants/payment-gateway');

const buildSheet = (records) => {
  const paymentGWData = Object.keys(PaymentGateway).reduce((init, key) => {
    return {
      ...init,
      [PaymentGateway[key]]: {
        totalTx: 0,
        totalRevenue: 0,
        totalFee: 0,
      },
    };
  }, {});

  records.forEach((record) => {
    if (record.paymentStatus !== 'SUCCESS') {
      return;
    }

    let pmGw;
    switch (record.paymentGateway.toLowerCase()) {
      case 'free':
      case 'bonus_point':
        return;
      case 'paypal':
        pmGw = PaymentGateway.PAYPAL;
        break;
      case 'momo':
        pmGw = PaymentGateway.MOMO;
        break;
      case 'paymentwall':
        pmGw = PaymentGateway.PAYMENTWALL;
        break;
      case 'vnpay':
        pmGw = PaymentGateway.VNPAY_LOCAL;
        break;
      case 'intcard':
        pmGw = PaymentGateway.VNPAY_CREDIT_CARD;
        break;
      case 'zalopay_vietqr':
        pmGw = PaymentGateway.ZALOPAY_VIETQR;
        break;
      case 'zalopay':
        pmGw = PaymentGateway.ZALOPAY;
        break;
      case 'zalopay_cc':
        pmGw = PaymentGateway.ZALOPAY_CC;
        break;
    }
    if (!(pmGw in paymentGWData)) {
      console.warn('Payment not exist: ', record.paymentGateway);
      return;
    }

    const amount = record.amount;
    paymentGWData[pmGw].totalTx++;
    paymentGWData[pmGw].totalRevenue += amount;
    paymentGWData[pmGw].totalFee +=
      PaymentFlatFee[pmGw] + (amount * PaymentTxFee[pmGw]) / 100 + Number.EPSILON;
  });

  const sheet = XLSX.utils.aoa_to_sheet([['', '', '', 'Payment Fee', '', '', '']]);
  XLSX.utils.sheet_add_aoa(
    sheet,
    [
      [
        'Payment Gateway',
        'Total Transaction',
        'Total Revenue',
        'Flat Fee',
        'Transaction Fee',
        'Fee',
        'Payout',
      ],
    ],
    {
      origin: -1,
    },
  );
  Object.keys(paymentGWData).forEach((pmName) => {
    const totalFee = Math.round(paymentGWData[pmName].totalFee * 100) / 100;

    XLSX.utils.sheet_add_aoa(
      sheet,
      [
        [
          pmName,
          paymentGWData[pmName].totalTx,
          paymentGWData[pmName].totalRevenue,
          PaymentFlatFee[pmName],
          PaymentTxFee[pmName],
          totalFee,
          paymentGWData[pmName].totalRevenue - totalFee,
        ],
      ],
      {
        origin: -1,
      },
    );
  });

  // handle specific style/format
  const borderStyle = ['top', 'bottom', 'left', 'right'].reduce((styles, edge) => {
    return {
      ...styles,
      [edge]: {
        style: 'thin',
        color: {rgb: 'black'},
      },
    };
  }, {});

  sheet['!merges'] = [
    {s: {r: 0, c: 3}, e: {r: 0, c: 5}}, // Merge cells D1:F1
  ];
  ['D1', 'E1', 'F1'].forEach((cell) => {
    sheet[cell].s = {
      fill: {
        fgColor: {rgb: 'FFFF00'}, // Set background color to yellow
      },
      border: {
        ...borderStyle,
      },
    };
  });

  return sheet;
};

module.exports = {
  buildSheet,
};
