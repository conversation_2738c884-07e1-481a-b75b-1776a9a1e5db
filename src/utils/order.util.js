const calcTicketPrice = (t, priceKey = 'ticketPrice') => {
  const discountPercent = t.discountPercent || 0;
  const price = t[priceKey] || 0;
  const ticketPrice = price - price * (discountPercent / 100);
  let amount = ticketPrice * t.quantity;

  if (!!t.combo?.length && t.quantity > 1) {
    const matchRecord = t.combo.find((c) => c.value === t.quantity);
    if (matchRecord) {
      amount -= Math.round(amount * (matchRecord.percent / 100));
    } else {
      const maxValue = Math.max(...t.combo.map((c) => c.value));
      const maxRecord = t.combo.find((c) => c.value === maxValue);
      amount -= Math.round(amount * (maxRecord.percent / 100));
    }
  }

  return amount;
};

const calcMerchantProductPrice = (t) => {
  let discountAmount = 0;
  if (t.promotion?.percentValue) {
    discountAmount = t.price * (t.promotion.percentValue / 100);
  } else if (t.promotion?.fixedValue) {
    discountAmount = t.promotion.fixedValue;
  }

  const amount = (t.price - discountAmount) * t.quantity;

  return amount;
};

const calcTicketPriceWithPromotion = (t, discount) => {
  const finalPriceVn = t.finalPriceVn || t.ticketPrice;
  const finalPriceUsd = t.finalPriceUsd || t.ticketPriceInUSD;

  const discountPercent = discount.percent || 0;
  let discountAmount = finalPriceVn * (discountPercent / 100);
  if (discountAmount > discount.maxValue) {
    discountAmount = discount.maxValue;
  }

  const ticketPrice = finalPriceVn - discountAmount;
  const amount = ticketPrice * t.quantity;

  const discountPercentInUSD = discount.percentInUSD || 0;
  let discountAmountInUSD = finalPriceUsd * (discountPercentInUSD / 100);
  discountAmountInUSD = Math.round(discountAmountInUSD * 100) / 100;
  if (discountAmountInUSD > discount.maxValueInUSD) {
    discountAmountInUSD = discount.maxValueInUSD;
  }
  const ticketPriceInUSD = finalPriceUsd - discountAmountInUSD;
  const amountInUSD = ticketPriceInUSD * t.quantity;

  // todo: nếu ticket có apply theo combo
  // if (!!t.combo?.length && t.quantity > 1) {
  //   const matchRecord = t.combo.find((c) => c.value === t.quantity);
  //   if (matchRecord) {
  //     amount -= Math.round(amount * (matchRecord.percent / 100));
  //   } else {
  //     const maxValue = Math.max(...t.combo.map((c) => c.value));
  //     const maxRecord = t.combo.find((c) => c.value === maxValue);
  //     amount -= Math.round(amount * (maxRecord.percent / 100));
  //   }
  // }

  return {amount, amountInUSD};
};

const calcAllTicketPriceWithPromotion = (tickets, promotion) => {
  let ticketApplied = tickets;
  let ticketNotApplied = [];
  if (promotion.appliesTo === 'ticket') {
    ticketApplied = tickets.filter((item) => promotion.tickets.some((id) => id === item.id));
    ticketNotApplied = tickets.filter((item) => !ticketApplied.some((t) => t.id === item.id));
  }
  const ticketAppliedPrices = ticketApplied.map((t) => {
    const {amount, amountInUSD} = calcTicketPriceWithPromotion(t, promotion.discount);
    return {
      totalAmount: amount || 0,
      totalAmountInUSD: amountInUSD || 0,
    };
  });
  const amountWithPromotion = ticketAppliedPrices.reduce(
    (total, ticket) => total + ticket.totalAmount,
    0,
  );
  const amountInUSDWithPromotion = ticketAppliedPrices.reduce(
    (total, ticket) => total + ticket.totalAmountInUSD,
    0,
  );

  const ticketNptAppliedPrices = ticketNotApplied.map((t) => ({
    totalAmount: calcTicketPrice(t),
    totalAmountInUSD: calcTicketPrice(t, 'ticketPriceInUSD'),
  }));
  const amount = ticketNptAppliedPrices.reduce((total, ticket) => total + ticket.totalAmount, 0);
  const amountInUSD = ticketNptAppliedPrices.reduce(
    (total, ticket) => total + ticket.totalAmountInUSD,
    0,
  );

  return {
    amount: amountWithPromotion + amount,
    amountInUSD: amountInUSDWithPromotion + amountInUSD,
  };
};

module.exports.OrderUtil = {
  calcTicketPrice,
  calcMerchantProductPrice,
  calcTicketPriceWithPromotion,
  calcAllTicketPriceWithPromotion,
};
