const {BadRequestError} = require('../common/exceptions');
const {FIELD_TOO_LONG, FIELD_TOO_SHORT} = require('../constants/error-code');

const isNumberOrBoolean = (value) => ['number', 'boolean'].includes(typeof value);

const isNullOrEmpty = (value) => !value || value.length === 0;

const requireParams = (requestBody = '{}', requiedFields, index = -1) => {
  return new Promise((resolve, reject) => {
    const data = JSON.parse(requestBody);
    if (!requiedFields || requiedFields.length === 0) {
      resolve();
      return;
    }
    let missingFields = [];
    if (!data || JSON.stringify(data) === '{}') {
      missingFields = [...requiedFields];
    } else {
      for (const key of requiedFields) {
        let value = data[key];
        const nestedKey = key.split('.');
        if (nestedKey.length === 2) {
          value = (data[nestedKey[0]] || {})[nestedKey[1]];
        }
        if (!isNumberOrBoolean(value) && isNullOrEmpty(value)) {
          let keyName = key;
          if (index !== -1) {
            keyName = `${key}[${index}]`;
          }
          missingFields.push(keyName);
        }
      }
    }
    if (missingFields.length === 0) {
      resolve();
      return;
    }

    const error = {
      code: 100001,
      message: 'Missing required values',
      params: missingFields,
    };

    reject(new BadRequestError(error));
  });
};

const cleanData = (data) => {
  if (!data) return data;
  const cleanData = {};
  Object.keys(data).forEach((key) => {
    const value = data[key];
    cleanData[key] = typeof value === 'string' ? value.trim() : value;
  });
  return cleanData;
};

const validateMaxLength = (fieldName, value, maxLength) => {
  if (value.length > maxLength)
    throw new BadRequestError({
      code: FIELD_TOO_LONG,
      message: `${fieldName} error. ${fieldName} needs to be longer than ${maxLength} characters.`,
    });
};

const validateMinLength = (fieldName, value, minLength) => {
  if (value.length < minLength)
    throw new BadRequestError({
      code: FIELD_TOO_SHORT,
      message: `${fieldName} error. ${fieldName} needs to be shorter than ${minLength} characters.`,
    });
};

const validateRequireFields = (data = {}, requiedFields) => {
  return new Promise((resolve, reject) => {
    if (!requiedFields || requiedFields.length === 0) {
      resolve();
      return;
    }
    let missingFields = [];
    // check type object
    if (
      !(typeof data === 'object' && !Array.isArray(data) && data !== null) ||
      !Object.keys(data)
    ) {
      missingFields = [...requiedFields];
    } else {
      for (const key of requiedFields) {
        if (!['boolean'].includes(typeof data[key]) && (!data[key] || data[key].length === 0)) {
          missingFields.push(key);
        }
      }
    }
    if (missingFields.length === 0) {
      resolve();
      return;
    }

    const error = {
      code: 100001,
      message: 'Missing required values',
      params: missingFields,
    };

    reject(new BadRequestError(error));
  });
};

const isValidDate = (date) => {
  return date instanceof Date && !isNaN(date);
};
module.exports = {
  validateMaxLength,
  requireParams,
  validateMinLength,
  cleanData,
  validateRequireFields,
  isValidDate,
};
