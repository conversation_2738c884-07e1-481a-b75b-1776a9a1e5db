const {PutObjectCommand, GetObjectCommand, S3Client} = require('@aws-sdk/client-s3');
const {getSignedUrl} = require('@aws-sdk/s3-request-presigner');

const s3Client = new S3Client();
const s3Bucket = process.env.S3_UPLOAD_BUCKET;

const uploadFile = async (key, body, contentType) => {
  const command = new PutObjectCommand({
    Bucket: s3Bucket,
    Key: key,
    Body: body,
    ContentType: contentType,
  });

  try {
    await s3Client.send(command);
    return getSignedUrl(
      s3Client,
      new GetObjectCommand({
        Bucket: s3Bucket,
        Key: key,
      }),
      {expiresIn: 3600},
    );
  } catch (err) {
    console.error(err);
  }
};

// Hàm chuyển đổi ReadableStream sang chuỗi
function streamToString(stream) {
  return new Promise((resolve, reject) => {
    const chunks = [];
    stream.on('data', (chunk) => chunks.push(chunk));
    stream.on('end', () => resolve(Buffer.concat(chunks).toString('utf-8')));
    stream.on('error', reject);
  });
}

const streamToBuffer = async (stream) => {
  const chunks = [];
  for await (const chunk of stream) {
    chunks.push(chunk);
  }
  return Buffer.concat(chunks);
};

async function getFile(key) {
  const params = {
    Bucket: s3Bucket,
    Key: key,
  };

  try {
    const command = new GetObjectCommand(params);
    const data = await s3Client.send(command);
    return data.Body;
  } catch (error) {
    console.error('Error fetching file', error);
    return null;
  }
}

// Hàm để đọc file JSON từ S3
async function getFileObject(key) {
  const body = await getFile(key);
  if (body) {
    const data = await streamToString(body);
    if (data) return JSON.parse(data);
  }
  return {};
}

// Hàm để đọc file txt từ S3
async function getFileTxt(key) {
  const data = await getFile(key);
  if (data)
    return streamToString(data)
      .split('\n')
      .map((line) => line.trim());
  return [];
}

async function getFileBuffer(key) {
  const data = await getFile(key);
  if (data) return streamToBuffer(data);
  return null;
}

// Hàm để ghi data vào S3
async function updateFileObject(data, key) {
  const params = {
    Bucket: s3Bucket,
    Key: key,
    ...data,
  };

  try {
    const command = new PutObjectCommand(params);
    await s3Client.send(command);
    console.log('File updated successfully');
  } catch (error) {
    console.error('Error updating file in S3:', error);
  }
}

module.exports = {
  uploadFile,
  getFileObject,
  getFileTxt,
  getFileBuffer,
  updateFileObject,
};
