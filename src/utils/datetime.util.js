const {orderBy} = require('lodash');

const getTicketEventTime = (eventCalendar) => {
  if (!eventCalendar?.length) return {};
  if (eventCalendar.length === 1) {
    return eventCalendar[0];
  }
  const calendar = orderBy(eventCalendar, ['startTime'], ['asc']);

  const firstDay = calendar[0];
  const lastDay = calendar[calendar.length - 1];

  return {
    startDate: firstDay?.startDate,
    endDate: lastDay?.endDate,
  };
};

module.exports.DatetimeUtil = {getTicketEventTime};
