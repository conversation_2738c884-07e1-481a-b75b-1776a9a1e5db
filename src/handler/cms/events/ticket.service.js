const {EventModel} = require('../../../models/EventModel');
const ticketModel = require('../../../../drizzle/models/ticket');
const {ResourceNotFoundError} = require('../../../common/exceptions');

const getTickets = async (data) => {
  const event = await EventModel.get({PK: data.eventId});
  if (!event) {
    throw new ResourceNotFoundError('Không tìm thấy sự kiện');
  }
  const calendarInfo = event.eventCalendar.find((item) => item.calendarId === data.calendarId);
  if (!calendarInfo) {
    throw new ResourceNotFoundError('Ngày diễn ra sự kiện không đúng');
  }
  return await ticketModel.getTicketsByRow(data);
};

const deleteTickets = async (data) => {
  const event = await EventModel.get({PK: data.eventId});
  if (!event) {
    throw new ResourceNotFoundError('<PERSON>hông tìm thấy sự kiện');
  }
  const calendarInfo = event.eventCalendar.find((item) => item.calendarId === data.calendarId);
  if (!calendarInfo) {
    throw new ResourceNotFoundError('Ngày diễn ra sự kiện không đúng');
  }
  await ticketRowModel.deleteTicketRow(data);
};

const getTicketsByFilter = async (data, queryParams) => {
  const event = await EventModel.get({PK: data.eventId});
  if (!event) {
    throw new ResourceNotFoundError('Không tìm thấy sự kiện');
  }
  const calendarInfo = event.eventCalendar.find((item) => item.calendarId === data.calendarId);
  if (!calendarInfo) {
    throw new ResourceNotFoundError('Ngày diễn ra sự kiện không đúng');
  }
  return await ticketModel.getTicketsByFilter(data, queryParams);
};

module.exports.TicketServices = {
  getTickets,
  deleteTickets,
  getTicketsByFilter,
};
