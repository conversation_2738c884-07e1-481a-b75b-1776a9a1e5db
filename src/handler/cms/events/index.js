const {pick} = require('lodash');
const {errorHandler} = require('../../../utils/error-handler.util');
const ResponseBuilder = require('../../../utils/response-builder.util');
const ValidatorUtil = require('../../../utils/request-validator.util');
const {CmsEventEndPoint} = require('./const');
const {EventService} = require('./event.service');
const {ZoneServices} = require('./zone.service');
const {RowServices} = require('./row.service');
const {TicketServices} = require('./ticket.service');
const {TicketClassServices} = require('./ticket-class.service');
const TicketTypes = require('../../../common/types/ticket.type');
const {BadRequestError} = require('../../../common/exceptions');
const CollaborationType = require('../../../common/types/collaboration.type');
const TicketEventCategory = require('../../../common/types/ticketing-category.type');
// const {LambdaClient} = require('../../../common/lambda');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      case CmsEventEndPoint.GET_EVENT:
        data = await getEvents(event);
        break;
      case CmsEventEndPoint.CREATE_EVENT:
        data = await createEvent(event);
        break;
      case CmsEventEndPoint.UPDATE_EVENT:
        const updateFor = event.pathParameters.updateFor;
        if (updateFor === 'content') {
          data = await updateEventContent(event);
          break;
        }
        if (updateFor === 'collection-fields') {
          data = await updatePaymentInfoFields(event);
          break;
        }
        if (updateFor === 'general') {
          data = await updateGeneralInfo(event);
          break;
        }
        break;
      case CmsEventEndPoint.GET_EVENT_DETAIL:
        data = await getEventDetail(event);
        break;
      case CmsEventEndPoint.CREATE_TICKET:
        data = await createTicket(event);
        break;
      case CmsEventEndPoint.GET_TICKET_DETAIL:
        data = await getTicket(event);
        break;
      case CmsEventEndPoint.UPDATE_TICKET:
        data = await updateTicket(event);
        break;
      case CmsEventEndPoint.DELETE_TICKET:
        data = await deleteTicket(event);
        break;
      case CmsEventEndPoint.UPDATE_EVENT_STATUS:
        data = await updateEventStatus(event);
        break;
      case CmsEventEndPoint.GET_EVENT_CATEGORIES:
        data = Object.values(TicketEventCategory).map((category) => ({
          key: category,
          text: TicketEventCategory.getText(category),
        }));
        break;
      case CmsEventEndPoint.CREATE_ZONE:
        data = await createZone(event);
        break;
      case CmsEventEndPoint.GET_TICKETS:
        data = await getTickets(event);
        break;
      case CmsEventEndPoint.CREATE_ROW:
        data = await createRow(event);
        break;
      case CmsEventEndPoint.DELETE_ZONE:
        data = await deleteZone(event);
        break;
      case CmsEventEndPoint.CREATE_TICKET_CLASS:
        data = await createTicketClass(event);
        break;
      case CmsEventEndPoint.UPDATE_ROW:
        data = await updateRow(event);
        break;
      case CmsEventEndPoint.GET_ZONES:
        data = await getZones(event);
        break;
      case CmsEventEndPoint.GET_ROWS:
        data = await getRows(event);
        break;
      case CmsEventEndPoint.DELETE_ROW:
        data = await deleteRow(event);
        break;
      case CmsEventEndPoint.DELETE_TICKET_CLASS:
        data = await deleteTicketClass(event);
        break;
      case CmsEventEndPoint.GET_TICKET_CLASS:
        data = await getTicketClasses(event);
        break;
      case CmsEventEndPoint.GET_TICKETS_BY_FILTER:
        data = await getTicketsByFilter(event);
        break;
      case CmsEventEndPoint.SORT_ZONES:
        data = await sortZones(event);
        break;
      case CmsEventEndPoint.SORT_ROWS:
        data = await sortRows(event);
        break;
      case CmsEventEndPoint.UPDATE_TICKET_CLASS:
        data = await updateTicketClass(event);
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (error) {
    return errorHandler(error);
  }
};

async function getEvents(event) {
  const events = await EventService.getAssignedEvents();

  return events;
}

async function createEvent(event) {
  const requiredFields = [
    'tenantId',
    'eventName',
    'location',
    'address',
    'pathName',
    'collaboration',
    'ticketType',
    'eventCalendar',
  ];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const body = JSON.parse(event.body);
  if (!TicketTypes.isValid(body.ticketType)) {
    throw new BadRequestError({
      code: 510001,
      message: `Ticket type is invalid.[${Object.values(TicketTypes)}]`,
    });
  }
  if (!CollaborationType.isValid(body.collaboration)) {
    throw new BadRequestError({
      code: 510002,
      message: `Collaboration is invalid.[${Object.values(CollaborationType)}]`,
    });
  }
  if (body.categories && !TicketEventCategory.isValidAll(body.categories)) {
    throw new BadRequestError({
      code: 510004,
      message: `Category is invalid.[${Object.values(TicketEventCategory)}]`,
    });
  }

  const ticketEvent = await EventService.createEvent(body);

  // deploy
  // const functionName = `trecenter-api-${process.env.ENV}-cms-deployment`;
  // const payload = {
  //   domain: ticketEvent.domain,
  //   eventPath: ticketEvent.pathName,
  // };
  // LambdaClient.invoke(functionName, payload).then(() =>
  //   console.log('Event is deployed'),
  // );

  return ticketEvent;
}

async function updateGeneralInfo(event) {
  const eventId = event.pathParameters.eventId;
  const body = JSON.parse(event.body);
  if (body.ticketType && !TicketTypes.isValid(body.ticketType)) {
    throw new BadRequestError({
      code: 510001,
      message: `Ticket type is invalid.[${Object.values(TicketTypes)}]`,
    });
  }
  if (body.collaboration && !CollaborationType.isValid(body.collaboration)) {
    throw new BadRequestError({
      code: 510002,
      message: `Collaboration is invalid.[${Object.values(CollaborationType)}]`,
    });
  }
  if (body.categories && !TicketEventCategory.isValidAll(body.categories)) {
    throw new BadRequestError({
      code: 510004,
      message: `Category is invalid.[${Object.values(TicketEventCategory)}]`,
    });
  }

  await EventService.updateEvent(
    eventId,
    pick(body, [
      'eventName',
      'location',
      'address',
      'collaboration',
      'ticketType',
      'eventCalendar',
      'note',
      'categories',
      'concertId',
    ]),
  );

  return {successMessage: 'Cập nhật thông tin thành công'};
}

async function updateEventContent(event) {
  const eventId = event.pathParameters.eventId;
  const requiredFields = ['logo', 'hostedBy', 'thumbnail', 'venueMap', 'eventDesctiptions'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const body = JSON.parse(event.body);
  const eventDesctiptionFields = ['title', 'description'];
  for (const [index, item] of body.eventDesctiptions.entries()) {
    await ValidatorUtil.requireParams(JSON.stringify(item), eventDesctiptionFields, index);
  }
  const updateFields = [
    ...requiredFields,
    'visitors',
    'videos',
    'images',
    'background',
    'headingBackground',
  ];

  await EventService.updateEvent(eventId, pick(body, updateFields));

  return {successMessage: 'Cập nhật thông tin thành công'};
}

async function updatePaymentInfoFields(event) {
  const eventId = event.pathParameters.eventId;
  const body = JSON.parse(event.body) || [];

  const requiredFields = ['key', 'text', 'isRequired'];
  for (const [index, item] of body.entries()) {
    await ValidatorUtil.requireParams(JSON.stringify(item), requiredFields, index);
  }

  await EventService.updateEvent(eventId, {
    general: {
      paymentInfoFields: body.map((item) => ({
        ...pick(item, requiredFields),
        isDefault: ['name', 'phoneNumber', 'email', 'note'].includes(item.key),
      })),
    },
  });

  return {successMessage: 'Cập nhật thông tin thành công'};
}

async function getEventDetail(event) {
  const eventId = event.pathParameters.eventId;
  return await EventService.getEventDetail(eventId);
}

async function createTicket(event) {
  const eventId = event.pathParameters.eventId;
  const requiredFields = ['ticketName', 'seatType', 'calendarId'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const body = JSON.parse(event.body);
  await EventService.createTicket(eventId, body);

  return {successMessage: 'Tạo vé thành công'};
}

async function getTicket(event) {
  const eventId = event.pathParameters.eventId;
  const ticketId = event.pathParameters.ticketId;

  return await EventService.getTicketDetail(eventId, ticketId);
}

async function updateTicket(event) {
  const eventId = event.pathParameters.eventId;
  const ticketId = event.pathParameters.ticketId;

  const requiredFields = ['ticketName', 'seatType', 'ticketInventory'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const body = JSON.parse(event.body);
  await EventService.updateTicket(eventId, ticketId, body);

  return {successMessage: 'Cập nhật thông tin vé thành công'};
}

async function deleteTicket(event) {
  const eventId = event.pathParameters.eventId;
  const ticketId = event.pathParameters.ticketId;

  await EventService.deleteTicket(eventId, ticketId);

  return {successMessage: 'Xóa vé thành công'};
}

async function updateEventStatus(event) {
  const eventId = event.pathParameters.eventId;
  const requiredFields = ['status'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const {status} = JSON.parse(event.body);
  await EventService.updateEventStatus(eventId, status);

  return {successMessage: 'Cập nhật trạng thái thành công'};
}

async function createZone(event) {
  const {eventId, calendarId} = event.pathParameters;
  const requiredFields = ['name'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const result = await ZoneServices.createZone({
    ...JSON.parse(event.body),
    eventId,
    calendarId,
  });

  return {
    successMessage: 'Tạo zone thành công',
    ...result,
  };
}

async function createRow(event) {
  const {eventId, calendarId, zoneId} = event.pathParameters;
  const requiredFields = ['ticketClassId', 'rowName', 'tickets'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  await RowServices.createRow({
    ...JSON.parse(event.body),
    eventId,
    calendarId,
    zoneId,
  });
}

async function deleteZone(event) {
  const {eventId, calendarId, zoneId} = event.pathParameters;

  await ZoneServices.deleteZone({
    eventId,
    calendarId,
    zoneId,
  });
}

async function createTicketClass(event) {
  const {eventId, calendarId} = event.pathParameters;
  const requiredFields = ['name', 'color', 'seatType'];
  await ValidatorUtil.requireParams(event.body, requiredFields);
  await TicketClassServices.createTicketClass({
    ...JSON.parse(event.body),
    eventId,
    calendarId,
  });
}

async function updateRow(event) {
  const {eventId, calendarId, rowId, zoneId} = event.pathParameters;
  const requiredFields = [
    'name',
    'seatNumberingType',
    'seatNumberingFrom',
    'seatNumberingTo',
    'tickets',
  ];
  await ValidatorUtil.requireParams(event.body, requiredFields);
  await RowServices.updateRow({
    ...JSON.parse(event.body),
    eventId,
    calendarId,
    rowId,
    zoneId,
  });
}

async function getZones(event) {
  const {eventId, calendarId} = event.pathParameters;
  const filters = event.queryStringParameters || {};

  return await ZoneServices.getZones({eventId, calendarId, ...filters});
}

async function getRows(event) {
  const {eventId, calendarId, zoneId} = event.pathParameters;
  return await RowServices.getRows({eventId, calendarId, zoneId});
}

async function getTickets(event) {
  const {eventId, calendarId, zoneId, rowId} = event.pathParameters;
  return await TicketServices.getTickets({eventId, calendarId, zoneId, rowId});
}

async function deleteRow(params) {
  return await RowServices.deleteRow(params.pathParameters);
}

async function deleteTicketClass(params) {
  return await TicketClassServices.deleteTicketClasses(params.pathParameters);
}

async function getTicketClasses(params) {
  return await TicketClassServices.getTicketClasses(params.pathParameters);
}

async function getTicketsByFilter(params) {
  return await TicketServices.getTicketsByFilter(
    params.pathParameters,
    params.queryStringParameters,
  );
}

async function sortZones(params) {
  const {eventId, calendarId} = params.pathParameters;

  return await ZoneServices.sortZones({
    eventId,
    calendarId,
    ...JSON.parse(params.body),
  });
}
async function sortRows(params) {
  const {eventId, calendarId, zoneId} = params.pathParameters;

  return await RowServices.sortRows({
    eventId,
    calendarId,
    zoneId,
    ...JSON.parse(params.body),
  });
}

async function updateTicketClass(params) {
  return await TicketClassServices.updateTicketClass({
    ...params.pathParameters,
    ...JSON.parse(params.body),
  });
}
