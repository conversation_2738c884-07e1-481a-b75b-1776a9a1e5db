const {EventModel} = require('../../../models/EventModel');
const ticketZoneModel = require('../../../../drizzle/models/ticket-zone');
const seatmapModel = require('../../../../drizzle/models/seatmap');
const {ResourceNotFoundError} = require('../../../common/exceptions');

const getZones = async (data) => {
  const event = await EventModel.get({PK: data.eventId});
  if (!event) {
    throw new ResourceNotFoundError('Không tìm thấy sự kiện');
  }
  const calendarInfo = event.eventCalendar.find((item) => item.calendarId === data.calendarId);
  if (!calendarInfo) {
    throw new ResourceNotFoundError('<PERSON><PERSON>y diễn ra sự kiện không đúng');
  }
  return await ticketZoneModel.getZones(data);
};

const createZone = async (data) => {
  const event = await EventModel.get({PK: data.eventId});
  if (!event) {
    throw new ResourceNotFoundError('Không tìm thấy sự kiện');
  }

  const lastPositionItem = await ticketZoneModel.getLatestPosition(data);
  let position = 1;

  if (lastPositionItem.length > 0) {
    position = lastPositionItem[0].position + 1;
  }

  const res = await ticketZoneModel.createTicketZone({
    ...data,
    position,
  });

  // Temporary delete seatmap when update zones
  await seatmapModel.deleteSeatmap({
    calendarId: data.calendarId,
    eventId: data.eventId,
  });

  return res;
};

const deleteZone = async (data) => {
  const event = await EventModel.get({PK: data.eventId});
  if (!event) {
    throw new ResourceNotFoundError('Không tìm thấy sự kiện');
  }
  const calendarInfo = event.eventCalendar.find((item) => item.calendarId === data.calendarId);
  if (!calendarInfo) {
    throw new ResourceNotFoundError('Ngày diễn ra sự kiện không đúng');
  }
  await ticketZoneModel.deleteTicketZone(data);
  // Temporary delete seatmap when update zones
  await seatmapModel.deleteSeatmap({
    calendarId: data.calendarId,
    eventId: data.eventId,
  });
};

const sortZones = async (body) => {
  await ticketZoneModel.sortZones(body);
};

module.exports.ZoneServices = {
  getZones,
  createZone,
  deleteZone,
  sortZones,
};
