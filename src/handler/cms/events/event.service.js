const {orderBy} = require('lodash');
const {ResourceNotFoundError, BadRequestError} = require('../../../common/exceptions');
const {EventModel} = require('../../../models/EventModel');
const {v4} = require('../../../utils/uuid.util');
const {DatetimeUtil} = require('../../../utils/datetime.util');
const {TicketStorageModel} = require('../../../models/TicketStorageModel');
const {TenantModel} = require('../../../models/TenantModel');

const getAllEvents = async () => {
  return await EventModel.scan().all().exec();
};

const getAssignedEvents = async () => {
  // const user = await UserModel.get({PK: userId});
  //
  // const hasPerm = user.tenants?.some((id) => id === tenantId);
  //
  // if (!hasPerm) {
  //   return [];
  // }

  const events = await EventModel.scan()
    .attributes([
      'PK',
      'eventName',
      'location',
      'address',
      'status',
      'eventCalendar',
      'tickets',
      'categories',
    ])
    .all()
    .exec()
    .then((res) => res.toJSON())
    .then((events) =>
      events.map((item) => ({
        id: item.PK,
        eventName: item.eventName,
        location: item.location,
        address: item.address,
        status: item.status,
        ...DatetimeUtil.getTicketEventTime(item.eventCalendar),
        tickets: item.tickets?.map((ticket) => ({
          id: ticket.ticketId,
          name: ticket.ticketType,
          price: ticket.ticketPrice,
        })),
        eventCalendar: item.eventCalendar,
        categories: item.categories,
      })),
    );

  return events;
};

const fillCalendarId = (eventCalendar) => {
  return orderBy(eventCalendar, ['startTime'], ['asc']).map((item) => ({
    ...item,
    calendarId: item.calendarId || v4(),
  }));
};

const createEvent = async (data) => {
  const tenant = await TenantModel.get({tenantId: data.tenantId});
  if (!tenant) {
    throw new ResourceNotFoundError('Không tìm thấy dự án');
  }
  const event = await EventModel.query('pathName').eq(data.pathName).using('pathNameIndex').exec();

  if (event?.count) {
    throw new BadRequestError({
      code: 510003,
      message: 'Tên đường dẫn đã được sử dụng',
    });
  }
  data.general = {
    collaboration: data.collaboration,
    ticketType: data.ticketType,
  };
  delete data.collaboration;
  delete data.ticketType;

  data.eventCalendar = fillCalendarId(data.eventCalendar);

  const result = await EventModel.create({
    PK: v4(),
    ...data,
    domain: tenant.domain,
  });

  return {
    id: result.PK,
    ...result,
    PK: undefined,
  };
};

const updateEvent = async (eventId, data) => {
  const event = await EventModel.get({PK: eventId});
  if (!event) {
    throw new ResourceNotFoundError('Không tìm thấy sự kiện');
  }

  if (data.general) {
    await EventModel.update(
      {PK: eventId},
      {
        general: {
          ...event.general,
          ...data.general,
        },
      },
    );
    return;
  }
  data.general = {...event.general};
  if (data.collaboration) {
    data.general = {
      ...data.general,
      collaboration: data.collaboration,
    };
  }
  if (data.ticketType) {
    data.general = {
      ...data.general,
      ticketType: data.ticketType,
    };
  }
  delete data.pathName;
  delete data.collaboration;
  delete data.ticketType;

  if (data.eventCalendar) {
    data.eventCalendar = fillCalendarId(data.eventCalendar);
  }

  await EventModel.update({PK: eventId}, data);
};

const getEventDetail = async (eventId) => {
  const event = await EventModel.get({PK: eventId});
  if (!event) {
    throw new ResourceNotFoundError('Không tìm thấy sự kiện');
  }

  return {
    id: event.PK,
    ...event,
    tickets:
      event.tickets?.map((item) => ({
        ...item,
        ticketName: item.ticketType,
        ticketType: undefined,
      })) || [],
    collaboration: event.general?.collaboration,
    ticketType: event.general?.ticketType,
    paymentInfoFields: event.general?.paymentInfoFields,
    PK: undefined,
    general: undefined,
  };
};

const createTicket = async (eventId, data) => {
  const event = await EventModel.get({PK: eventId});
  if (!event) {
    throw new ResourceNotFoundError('Không tìm thấy sự kiện');
  }

  const isMatch = event.eventCalendar?.some((item) => item.calendarId === data.calendarId);
  if (!isMatch) {
    throw new BadRequestError({
      code: 510003,
      message: 'Ngày diễn ra của vé không khớp với số ngày diễn ra của sự kiện.',
    });
  }

  const tickets = event.tickets || [];
  const ticketId = v4();
  tickets.push({
    ticketId,
    ticketType: data.ticketName,
    ...data,
    maxTicketPerUser: data.maxTicketPerUser || -1,
  });

  event.tickets = tickets;
  await event.save();
};

const getTicketDetail = async (eventId, ticketId) => {
  const event = await EventModel.get({PK: eventId});
  if (!event) {
    throw new ResourceNotFoundError('Không tìm thấy sự kiện');
  }
  const ticketInfo = event.tickets?.find((item) => item.ticketId === ticketId);
  if (!ticketInfo) {
    throw new ResourceNotFoundError('Vé không tồn tại');
  }

  const storage = await TicketStorageModel.query('PK')
    .eq(eventId)
    .where('ticketId')
    .beginsWith(ticketId)
    .exec()
    .then((res) => res.toJSON());

  const getCalendar = (id) => {
    const calendar = event.eventCalendar.find((item) => item.calendarId === id);
    if (!calendar) {
      return {};
    }
    return {
      startDate: calendar.startDate,
      endDate: calendar.endDate,
    };
  };

  const ticketInventory = storage.map((item) => {
    const [, calendarId] = item.ticketId.split('#');
    return {
      calendarId: calendarId,
      ...getCalendar(calendarId),
      quantity: item.baseQuantity,
      show: !!item.show,
      stopSelling: !!item.stopSelling,
    };
  });

  return {
    eventId,
    ...ticketInfo,
    ticketName: ticketInfo.ticketType,
    ticketInventory: orderBy(ticketInventory, ['startDate'], ['asc']),
    ticketType: undefined,
  };
};

const updateTicket = async (eventId, ticketId, data) => {
  const event = await EventModel.get({PK: eventId});
  if (!event) {
    throw new ResourceNotFoundError('Không tìm thấy sự kiện');
  }
  const ticketInfo = event.tickets?.find((item) => item.ticketId === ticketId);
  if (!ticketInfo) {
    throw new ResourceNotFoundError('Vé không tồn tại');
  }

  ticketInfo.ticketType = data.ticketName;
  ticketInfo.ticketDescription = data.ticketDescription;
  ticketInfo.ticketPrice = data.ticketPrice || 0;
  ticketInfo.ticketOriginalPrice = data.ticketOriginalPrice || 0;
  ticketInfo.ticketPriceInUSD = data.ticketPriceInUSD || 0;
  ticketInfo.ticketOriginalPriceInUSD = data.ticketOriginalPriceInUSD || 0;
  ticketInfo.seatType = data.seatType;
  ticketInfo.maxTicketPerUser = data.maxTicketPerUser || -1;
  ticketInfo.seatColor = data.seatColor;

  await event.save();

  const promises = [];
  const defaultValue = {baseQuantity: 0, quantity: 0};
  for (const ticket of data.ticketInventory) {
    const key = {
      PK: eventId,
      ticketId: `${ticketId}#${ticket.calendarId}`,
    };
    const result = await TicketStorageModel.get(key);
    const {baseQuantity, quantity} = result || defaultValue;
    const ticketQuantity = ticket.quantity - baseQuantity + quantity;
    promises.push(
      TicketStorageModel.update(key, {
        baseQuantity: ticket.quantity,
        quantity: ticketQuantity < 0 ? 0 : ticketQuantity,
        show: !!ticket.show,
        stopSelling: !!ticket.stopSelling,
        maxTicketPerUser: data.maxTicketPerUser || -1,
      }),
    );
  }
  await Promise.all(promises);
};

const deleteTicket = async (eventId, ticketId) => {
  const event = await EventModel.get({PK: eventId});
  if (!event) {
    throw new ResourceNotFoundError('Không tìm thấy sự kiện');
  }
  const ticketInfo = event.tickets?.find((item) => item.ticketId === ticketId);
  if (!ticketInfo) {
    throw new ResourceNotFoundError('Vé không tồn tại');
  }

  event.tickets = event.tickets.filter((item) => item.ticketId !== ticketId);
  await event.save();

  const promises = [];
  for (const calendar of event.eventCalendar) {
    promises.push(
      TicketStorageModel.delete({
        PK: eventId,
        ticketId: `${ticketId}#${calendar.calendarId}`,
      }),
    );
  }
  await Promise.all(promises);
};

const updateEventStatus = async (eventId, status) => {
  const event = await EventModel.get({PK: eventId});
  if (!event) {
    throw new ResourceNotFoundError('Không tìm thấy sự kiện');
  }
  event.status = status.toUpperCase();
  await event.save();
};

module.exports.EventService = {
  getAllEvents,
  getAssignedEvents,
  createEvent,
  updateEvent,
  getEventDetail,
  createTicket,
  getTicketDetail,
  updateTicket,
  deleteTicket,
  updateEventStatus,
};
