const {EventModel} = require('../../../models/EventModel');
const ticketRowModel = require('../../../../drizzle/models/ticket-row');
const ticketModel = require('../../../../drizzle/models/ticket');
const seatmapModel = require('../../../../drizzle/models/seatmap');
const {v4} = require('../../../utils/uuid.util');
const {ResourceNotFoundError} = require('../../../common/exceptions');

const getRows = async (data) => {
  const event = await EventModel.get({PK: data.eventId});
  if (!event) {
    throw new ResourceNotFoundError('Không tìm thấy sự kiện');
  }
  const calendarInfo = event.eventCalendar.find((item) => item.calendarId === data.calendarId);
  if (!calendarInfo) {
    throw new ResourceNotFoundError('<PERSON><PERSON><PERSON> diễ<PERSON> ra sự kiện không đúng');
  }
  return await ticketRowModel.getRowsByZone(data);
};

const createRow = async (data) => {
  const event = await EventModel.get({PK: data.eventId});
  if (!event) {
    throw new ResourceNotFoundError('Không tìm thấy sự kiện');
  }
  const calendarInfo = event.eventCalendar.find((item) => item.calendarId === data.calendarId);
  if (!calendarInfo) {
    throw new ResourceNotFoundError('Ngày diễn ra sự kiện không đúng');
  }

  const extraFields = {};

  event.toJSON().general.paymentInfoFields.forEach((field) => {
    if (!field.isDefault) {
      extraFields[field.key] = null;
    }
  });

  const lastPositionItem = await ticketRowModel.getLatestPosition(data);
  let position = 1;

  if (lastPositionItem.length > 0) {
    position = lastPositionItem[0].position + 1;
  }

  const res = await ticketRowModel.createTicketRow({
    eventId: data.eventId,
    calendarId: data.calendarId,
    name: data.rowName,
    zoneId: data.zoneId,
    seatNumberingType: data.seatNumberingType,
    seatNumberingFrom: data.seatNumberingFrom,
    seatNumberingTo: data.seatNumberingTo,
    position,
  });

  const tickets = [];
  for (const ticket of data.tickets) {
    tickets.push({
      eventId: data.eventId,
      calendarId: data.calendarId,
      ticketClassId: data.ticketClassId,
      zoneId: data.zoneId,
      code: ticket.seatCode,
      rowId: res[0].insertedId,
      generateCode: v4(),
      customerExtraData: extraFields,
      position: ticket.position,
    });
  }
  await ticketModel.createTickets(tickets);
  // Temporary delete seatmap when update zones
  await seatmapModel.deleteSeatmap({
    calendarId: data.calendarId,
    eventId: data.eventId,
  });
};

const deleteRow = async (data) => {
  const event = await EventModel.get({PK: data.eventId});
  if (!event) {
    throw new ResourceNotFoundError('Không tìm thấy sự kiện');
  }
  const calendarInfo = event.eventCalendar.find((item) => item.calendarId === data.calendarId);
  if (!calendarInfo) {
    throw new ResourceNotFoundError('Ngày diễn ra sự kiện không đúng');
  }
  await ticketRowModel.deleteRow(data);
  // Temporary delete seatmap when update zones
  await seatmapModel.deleteSeatmap({
    calendarId: data.calendarId,
    eventId: data.eventId,
  });
};

const updateRow = async (data) => {
  const event = await EventModel.get({PK: data.eventId});
  if (!event) {
    throw new ResourceNotFoundError('Không tìm thấy sự kiện');
  }
  const calendarInfo = event.eventCalendar.find((item) => item.calendarId === data.calendarId);
  if (!calendarInfo) {
    throw new ResourceNotFoundError('Ngày diễn ra sự kiện không đúng');
  }

  const extraFields = {};

  event.toJSON().general.paymentInfoFields.forEach((field) => {
    if (!field.isDefault) {
      extraFields[field.key] = null;
    }
  });

  const tickets = [];
  for (const ticket of data.tickets) {
    tickets.push({
      eventId: data.eventId,
      calendarId: data.calendarId,
      ticketClassId: data.ticketClassId,
      zoneId: data.zoneId,
      code: ticket.seatCode,
      rowId: data.rowId,
      generateCode: v4(),
      customerExtraData: extraFields,
      position: ticket.position,
    });
  }
  const payload = {
    id: data.rowId,
    eventId: data.eventId,
    calendarId: data.calendarId,
    name: data.name,
    zoneId: data.zoneId,
    seatNumberingType: data.seatNumberingType,
    seatNumberingFrom: data.seatNumberingFrom,
    seatNumberingTo: data.seatNumberingTo,
    generateCode: v4(),
  };
  await ticketRowModel.updateRow(payload, tickets);
  // Temporary delete seatmap when update zones
  await seatmapModel.deleteSeatmap({
    calendarId: data.calendarId,
    eventId: data.eventId,
  });
};

const sortRows = async (data) => {
  await ticketRowModel.sortRows(data);
  // Temporary delete seatmap when update zones
  await seatmapModel.deleteSeatmap({
    calendarId: data.calendarId,
    eventId: data.eventId,
  });
};

module.exports.RowServices = {
  getRows,
  createRow,
  deleteRow,
  updateRow,
  sortRows,
};
