const CmsEventEndPoint = {
  GET_EVENT: 'GET /v1/cms/event',
  GET_EVENT_DETAIL: 'GET /v1/cms/event/{eventId}',
  UPDATE_EVENT_STATUS: 'POST /v1/cms/event/{eventId}/status',
  CREATE_EVENT: 'POST /v1/cms/event',
  UPDATE_EVENT: 'PUT /v1/cms/event/{eventId}/{updateFor}',
  CREATE_TICKET: 'POST /v1/cms/event/{eventId}/ticket',
  GET_TICKET_DETAIL: 'GET /v1/cms/event/{eventId}/ticket/{ticketId}',
  UPDATE_TICKET: 'PUT /v1/cms/event/{eventId}/ticket/{ticketId}',
  DELETE_TICKET: 'DELETE /v1/cms/event/{eventId}/ticket/{ticketId}',
  GET_TICKETS_BY_FILTER: 'GET /v2/cms/event/{eventId}/calendar/{calendarId}/tickets',
  GET_EVENT_CATEGORIES: 'GET /v1/cms/event/categories',
  CREATE_ZONE: 'POST /v2/cms/event/{eventId}/calendar/{calendarId}/zone',
  CREATE_TICKET_CLASS: 'POST /v2/cms/event/{eventId}/calendar/{calendarId}/ticket-class',
  DELETE_TICKET_CLASS:
    'DELETE /v2/cms/event/{eventId}/calendar/{calendarId}/ticket-class/{ticketClassId}',
  UPDATE_TICKET_CLASS:
    'PUT /v2/cms/event/{eventId}/calendar/{calendarId}/ticket-class/{ticketClassId}',
  GET_TICKET_CLASS: 'GET /v2/cms/event/{eventId}/calendar/{calendarId}/ticket-classes',
  CREATE_ROW: 'POST /v2/cms/event/{eventId}/calendar/{calendarId}/zone/{zoneId}/row',
  DELETE_ZONE: 'DELETE /v2/cms/event/{eventId}/calendar/{calendarId}/zone/{zoneId}',
  UPDATE_ROW: 'PUT /v2/cms/event/{eventId}/calendar/{calendarId}/zone/{zoneId}/row/{rowId}',
  GET_ZONES: 'GET /v2/cms/event/{eventId}/calendar/{calendarId}/zones',
  GET_ROWS: 'GET /v2/cms/event/{eventId}/calendar/{calendarId}/zone/{zoneId}/rows',
  GET_TICKETS:
    'GET /v2/cms/event/{eventId}/calendar/{calendarId}/zone/{zoneId}/row/{rowId}/tickets',
  DELETE_ROW: 'DELETE /v2/cms/event/{eventId}/calendar/{calendarId}/zone/{zoneId}/row/{rowId}',
  SORT_ZONES: 'POST /v2/cms/event/{eventId}/calendar/{calendarId}/sort-zones',
  SORT_ROWS: 'POST /v2/cms/event/{eventId}/calendar/{calendarId}/zone/{zoneId}/sort-rows',
};

module.exports = {
  CmsEventEndPoint,
};
