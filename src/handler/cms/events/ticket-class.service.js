const {EventModel} = require('../../../models/EventModel');
const ticketClassModel = require('../../../../drizzle/models/ticket-class');
const seatmapModel = require('../../../../drizzle/models/seatmap');
const {ResourceNotFoundError} = require('../../../common/exceptions');

const getTicketClasses = async (data) => {
  const event = await EventModel.get({PK: data.eventId});
  if (!event) {
    throw new ResourceNotFoundError('Không tìm thấy sự kiện');
  }
  const calendarInfo = event.eventCalendar.find((item) => item.calendarId === data.calendarId);
  if (!calendarInfo) {
    throw new ResourceNotFoundError('Ngày diễn ra sự kiện không đúng');
  }
  return await ticketClassModel.getTicketClasses(data);
};

const deleteTicketClasses = async (data) => {
  const event = await EventModel.get({PK: data.eventId});
  if (!event) {
    throw new ResourceNotFoundError('Không tìm thấy sự kiện');
  }
  const calendarInfo = event.eventCalendar.find((item) => item.calendarId === data.calendarId);
  if (!calendarInfo) {
    throw new ResourceNotFoundError('Ngày diễn ra sự kiện không đúng');
  }
  await ticketClassModel.deleteTicketClass(data);
  // Temporary delete seatmap when update zones
  await seatmapModel.deleteSeatmap({
    calendarId: data.calendarId,
    eventId: data.eventId,
  });
};

const createTicketClass = async (data) => {
  const event = await EventModel.get({PK: data.eventId});
  if (!event) {
    throw new ResourceNotFoundError('Không tìm thấy sự kiện');
  }
  const calendarInfo = event.eventCalendar.find((item) => item.calendarId === data.calendarId);
  if (!calendarInfo) {
    throw new ResourceNotFoundError('Ngày diễn ra sự kiện không đúng');
  }
  await ticketClassModel.createTicketClass(data);
};

const updateTicketClass = async (data) => {
  const event = await EventModel.get({PK: data.eventId});
  if (!event) {
    throw new ResourceNotFoundError('Không tìm thấy sự kiện');
  }
  const calendarInfo = event.eventCalendar.find((item) => item.calendarId === data.calendarId);
  if (!calendarInfo) {
    throw new ResourceNotFoundError('Ngày diễn ra sự kiện không đúng');
  }
  await ticketClassModel.updateTicketClass(data);
  // Temporary delete seatmap when update zones
  await seatmapModel.deleteSeatmap({
    calendarId: data.calendarId,
    eventId: data.eventId,
  });
};

module.exports.TicketClassServices = {
  getTicketClasses,
  deleteTicketClasses,
  createTicketClass,
  updateTicketClass,
};
