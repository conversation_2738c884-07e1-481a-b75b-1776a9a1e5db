const {orderBy, pick} = require('lodash');
const {ResourceNotFoundError, BadRequestError} = require('../../../common/exceptions');
const ErrorCode = require('../../../constants/error-code');
const {EventModel} = require('../../../models/EventModel');
const {PromotionModel} = require('../../../models/PromotionModel');
const {TicketStorageModel} = require('../../../models/TicketStorageModel');
const ticketClassModel = require('../../../../drizzle/models/ticket-class');

const createPromotion = async (data) => {
  const [event, promotion] = await Promise.all([
    EventModel.get({PK: data.eventId}),
    PromotionModel.get({eventId: data.eventId, code: data.code}),
  ]);
  if (!event) {
    throw new ResourceNotFoundError('<PERSON><PERSON> kiện không tồn tại');
  }
  if (!!promotion) {
    throw new BadRequestError({
      code: ErrorCode.Promotion.CODE_EXISTED,
      message: '<PERSON>ã khuyễn mại đã tồn tại',
    });
  }

  return await PromotionModel.create(data);
};

const getPromotions = async (eventId) => {
  const results = await PromotionModel.query('eventId')
    .eq(eventId)
    .attributes(['eventId', 'code', 'appliesTo', 'active', 'maxUsage', 'discount', 'createdAt'])
    .all()
    .exec()
    .then((res) => res.toJSON())
    .then((res) => orderBy(res, ['createdAt'], 'desc'));

  return results;
};

const getPromotion = async (eventId, code) => {
  const promotion = await PromotionModel.get({eventId, code});
  if (!promotion) {
    throw new ResourceNotFoundError('Mã khuyến mại không tồn tại');
  }

  return promotion;
};

const deletePromotion = async (eventId, code) => {
  await PromotionModel.delete({eventId, code});
};

const updatePromotion = async (data) => {
  return await PromotionModel.update(
    {eventId: data.eventId, code: data.code},
    pick(data, [
      'appliesTo',
      'active',
      'maxUsage',
      'discount',
      'tickets',
      'type',
      'minOrderAmount',
      'minOrderAmountInUSD',
    ]),
    {return: 'item'},
  );
};

const updateStatusPromotion = async (eventId, code, isActive) => {
  await PromotionModel.update({eventId, code}, {active: isActive});
};

const getTickets = async (eventId) => {
  const event = await EventModel.get({PK: eventId});
  if (!event) {
    throw new ResourceNotFoundError('Không tìm thấy sự kiện');
  }
  const ticketClass = await ticketClassModel.getTicketClassesByEventId(eventId);

  if (ticketClass) {
    return ticketClass.map((t) => ({
      ticketId: t.id,
      ticketName: t.name,
    }));
  } else {
    const storage = await TicketStorageModel.query('PK')
      .eq(eventId)
      .exec()
      .then((res) => res.toJSON());

    let {eventCalendar, tickets} = event;
    tickets = tickets.filter((t) => !!t.ticketPrice);
    for (const ticket of tickets) {
      const ticketCalendar = storage
        .filter((s) => s.ticketId.startsWith(ticket.ticketId))
        .map((s) => {
          const calendarId = s.ticketId.split('#')[1];
          const result = eventCalendar.find((c) => c.calendarId === calendarId);
          return {
            calendarId,
            date: s.show ? result?.startDate : null,
          };
        })
        .filter((s) => !!s.date);

      ticket.calendar = ticketCalendar;
    }

    return tickets.map((t) => ({
      ticketId: t.ticketId,
      ticketName: t.ticketType,
      calendar: t.calendar,
    }));
  }
};

module.exports.PromotionService = {
  createPromotion,
  getPromotions,
  getPromotion,
  deletePromotion,
  updatePromotion,
  updateStatusPromotion,
  getTickets,
};
