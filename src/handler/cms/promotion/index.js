const {errorHandler} = require('../../../utils/error-handler.util');
const ResponseBuilder = require('../../../utils/response-builder.util');
const ValidatorUtil = require('../../../utils/request-validator.util');
const {CmsPromotionEndPoint} = require('./const');
const {PromotionService} = require('./promotion.service');
const {BadRequestError} = require('../../../common/exceptions');
const ErrorCode = require('../../../constants/error-code');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;

    switch (routeKey) {
      case CmsPromotionEndPoint.CREATE_PROMOTION:
        data = await createPromotion(event);
        break;
      case CmsPromotionEndPoint.GET_PROMOTIONS:
        data = await getPromotions(event);
        break;
      case CmsPromotionEndPoint.GET_PROMOTION:
        data = await getPromotion(event);
        break;
      case CmsPromotionEndPoint.DELETE_PROMOTION:
        data = await deletePromotion(event);
        break;
      case CmsPromotionEndPoint.UPDATE_PROMOTION:
        data = await updatePromotion(event);
        break;
      case CmsPromotionEndPoint.ACTIVE_PROMOTION:
        data = await updateSatusPromotion(event);
        break;
      case CmsPromotionEndPoint.GET_LIST_TICKETS:
        data = await getTickets(event);
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (error) {
    return errorHandler(error);
  }
};

async function createPromotion(event) {
  const requiredFields = ['code', 'discount.percent', 'discount.percentInUSD', 'appliesTo', 'type']; // 'maxValue', 'maxUsage'
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const {eventId} = event.pathParameters;
  const body = JSON.parse(event.body);

  if (body.type === 'auto') {
    await ValidatorUtil.requireParams(event.body, ['minOrderAmount', 'minOrderAmountInUSD']);
  }

  const pattern = /^[A-Za-z\d]{5,}$/;
  if (!pattern.test(body.code)) {
    throw new BadRequestError({
      code: ErrorCode.Promotion.CODE_INVALID,
      message: 'Mã khuyến mại không hợp lệ',
    });
  }

  if (body.appliesTo === 'ticket') {
    await ValidatorUtil.requireParams(event.body, ['tickets']);
  }

  return await PromotionService.createPromotion({
    ...body,
    code: body.code.toUpperCase(),
    eventId,
  });
}

async function getPromotions(event) {
  const {eventId} = event.pathParameters;

  return await PromotionService.getPromotions(eventId);
}

async function getPromotion(event) {
  const {eventId, code} = event.pathParameters;

  return await PromotionService.getPromotion(eventId, code);
}

async function deletePromotion(event) {
  const {eventId, code} = event.pathParameters;
  await PromotionService.deletePromotion(eventId, code);

  return {successMessage: 'Đã xoá mã khuyến mại'};
}

async function updatePromotion(event) {
  const requiredFields = ['discount.percent', 'discount.percentInUSD', 'appliesTo', 'type'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const {eventId, code} = event.pathParameters;
  const body = JSON.parse(event.body);

  if (body.type === 'auto') {
    await ValidatorUtil.requireParams(event.body, ['minOrderAmount', 'minOrderAmountInUSD']);
  }

  if (body.appliesTo === 'ticket') {
    await ValidatorUtil.requireParams(event.body, ['tickets']);
  }

  return await PromotionService.updatePromotion({...body, eventId, code});
}

async function updateSatusPromotion(event) {
  const requiredFields = ['active'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const {eventId, code} = event.pathParameters;
  const body = JSON.parse(event.body);
  await PromotionService.updateStatusPromotion(eventId, code, body.active);

  return {successMessage: 'Thành công'};
}

async function getTickets(event) {
  const {eventId} = event.pathParameters;

  return await PromotionService.getTickets(eventId);
}
