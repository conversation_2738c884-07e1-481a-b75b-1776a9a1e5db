const {orderBy} = require('lodash');
const {ResourceNotFoundError} = require('../../../common/exceptions');
const {TenantModel} = require('../../../models/TenantModel');
const {v4} = require('../../../utils/uuid.util');
const {UserModel} = require('../../../models/UserModel');
const UserRoles = require('../../../common/types/user-roles');

const createTenant = async (data) => {
  const result = await TenantModel.create({
    ...data,
    tenantId: v4(),
  });

  if (!data.users) {
    return;
  }
  for (const user of data.users) {
    await UserModel.update({PK: user.userId}, {$ADD: {tenants: result.tenantId}});
  }
};

const updateTenant = async (tenantId, data) => {
  const tenant = await TenantModel.get({tenantId});
  if (!tenant) {
    throw new ResourceNotFoundError('<PERSON>hông tìm thấy dự án');
  }
  delete data.domain;
  await TenantModel.update({tenantId}, data);

  if (!data.users) {
    return;
  }
  const removeUsers =
    tenant.users?.filter((u) => !data.users.some((user) => user.userId === u.userId)) || [];
  for (const user of data.users) {
    const result = await UserModel.get({PK: user.userId});
    if (!result.tenants?.includes(tenantId)) {
      result.tenants = result.tenants ? [...result.tenants, tenantId] : [tenantId];
      await result.save();
    }
  }
  for (const user of removeUsers) {
    const result = await UserModel.get({PK: user.userId});
    if (result.tenants) {
      result.tenants = result.tenants.filter((t) => t !== tenantId);
      await result.save();
    }
  }
};

const getTenantDetail = async (tenantId, user) => {
  const tenant = await TenantModel.get({tenantId});
  if (!tenant) {
    throw new ResourceNotFoundError('Không tìm thấy dự án');
  }
  if (UserRoles.isSuperAdmin(user.userRole)) {
    return tenant;
  }

  return {
    ...tenant,
    permissions: tenant.users?.find((u) => u.userId === user.principalId),
    users: undefined,
  };
};

const getTenants = async (userId) => {
  const [tenants, user] = await Promise.all([
    TenantModel.scan()
      .attributes(['tenantId', 'name', 'domain', 'products', 'users', 'createdAt', 'updatedAt'])
      .exec()
      .then((res) => res.toJSON())
      .then((tenants) =>
        tenants.map((tenant) => ({
          ...tenant,
          products: tenant.products.map((p) => p.product),
        })),
      )
      .then((tenants) => orderBy(tenants, ['createdAt'], ['desc'])),
    UserModel.get({PK: userId}),
  ]);

  if (UserRoles.isSuperAdmin(user.role)) {
    return tenants.map((tenant) => ({...tenant, users: undefined}));
  }

  return tenants
    .filter((tenant) => tenant.users?.some((user) => user.userId === userId))
    .map((tenant) => ({...tenant, users: undefined}));
};

module.exports.DataService = {
  createTenant,
  updateTenant,
  getTenantDetail,
  getTenants,
};
