const ResponseBuilder = require('../../../utils/response-builder.util');
const {CmsTenantEndPoint} = require('./const');
const {errorHandler} = require('../../../utils/error-handler.util');
const ValidatorUtil = require('../../../utils/request-validator.util');
const {DataService} = require('./data.service');
const {VotingPlatformClient} = require('../../../common/lambda/voting-platform');

module.exports.handler = async (event) => {
  try {
    const routeKey = event.routeKey;

    let data;
    switch (routeKey) {
      case CmsTenantEndPoint.CREATE_TENANT:
        data = await createTenant(event);
        break;
      case CmsTenantEndPoint.GET_LIST_TENANT:
        data = await getTenants(event);
        break;
      case CmsTenantEndPoint.GET_TENANT:
        data = await getTenant(event);
        break;
      case CmsTenantEndPoint.UPDATE_TENANT:
        data = await updateTenant(event);
        break;
    }
    return ResponseBuilder.ok(data);
  } catch (error) {
    return errorHandler(error);
  }
};

const createTenant = async (event) => {
  const requiredFields = ['name', 'domain'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const body = JSON.parse(event.body);
  await DataService.createTenant(body);

  return {successMessage: 'Tạo dự án thành công!'};
};

const getTenants = async (event) => {
  const userId = event.requestContext.authorizer.lambda.principalId;
  return await DataService.getTenants(userId);
};

const getTenant = async (event) => {
  const userContext = event.requestContext.authorizer.lambda;
  const tenantId = event.pathParameters.tenantId;
  return await DataService.getTenantDetail(tenantId, userContext);
};

const updateTenant = async (event) => {
  const tenantId = event.pathParameters.tenantId;
  const requiredFields = ['name'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const body = JSON.parse(event.body);

  await DataService.updateTenant(tenantId, body);

  const votingProduct = body.products.find((item) => item.product === 'voting');
  if (!!votingProduct) {
    await VotingPlatformClient.invoke('invoke-update-payment-gateways', {
      tenantId: votingProduct.productId,
      paymentGateways: votingProduct.paymentGateway,
    });
  }

  return {successMessage: 'Cập nhật dự án thành công!'};
};
