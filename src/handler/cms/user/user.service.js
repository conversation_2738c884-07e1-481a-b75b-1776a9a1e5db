const {authenticator} = require('otplib');
const {BadRequestError, ResourceNotFoundError} = require('../../../common/exceptions');
const ErrorCode = require('../../../constants/error-code');
const {UserModel} = require('../../../models/UserModel');
const UserUtil = require('../../client/auth/util');
const UserRoles = require('../../../common/types/user-roles');

const createUser = async ({userId, password, ...rest}) => {
  try {
    const hashPassword = await UserUtil.hashPassword(password);
    await UserModel.create({
      PK: userId,
      password: hashPassword,
      ...rest,
    });
  } catch (error) {
    if (error.name === 'ConditionalCheckFailedException') {
      throw new BadRequestError({
        code: ErrorCode.EMAIL_EXIST,
        message: '<PERSON><PERSON><PERSON> kho<PERSON> đã được sử dụng',
      });
    }
    throw error;
  }
};

const generateOtp = async (userId) => {
  // check username exists
  const user = await UserModel.get({PK: userId});
  if (!user) {
    throw new ResourceNotFoundError('Tài khoản không tồn tại');
  }

  let secret = user.otpSecret;
  if (!secret) {
    secret = authenticator.generateSecret();
    // save new secret
    user.otpSecret = secret;
    await user.save();
  }
  let serviceName = 'Fan Engagement';
  if (process.env.ENV !== 'prod') {
    serviceName = `[${process.env.ENV}] ${serviceName}`;
  }
  const totp = authenticator.keyuri(userId, serviceName, secret);

  return {
    secret,
    qrCodeUrl: `https://quickchart.io/chart?cht=qr&chld=H%7C1&chs=200x200&chl=${encodeURIComponent(
      totp,
    )}`,
  };
};

const getUserDetail = async (userId) => {
  const user = await UserModel.get({PK: userId});
  if (!user) {
    throw new ResourceNotFoundError('Tài khoản không tồn tại');
  }

  return {
    userId: user.PK,
    ...user,
    PK: undefined,
    password: undefined,
  };
};

const getUsers = async () => {
  const query = UserModel.scan()
    .attributes([
      'PK',
      'email',
      'phone',
      'name',
      'gender',
      'status',
      'university',
      'major',
      'birthday',
      'icCard',
      'studentId',
      'schoolYear',
      'schoolCertificate',
      'createdAt',
      'updatedAt',
    ])
    .where('role')
    .eq(UserRoles.USER);

  return await query
    .all()
    .exec()
    .then((snapshot) => snapshot.toJSON())
    .then((users) => users.map((user) => ({...user, userId: user.PK, PK: undefined})));
};

const changePassword = async (userId, newPassword) => {
  const user = await UserModel.get({PK: userId});
  if (!user) {
    throw new ResourceNotFoundError('Tài khoản không tồn tại');
  }
  const hashPassword = await UserUtil.hashPassword(newPassword);
  user.password = hashPassword;
  await user.save();
};

const updateUser = async (userId, data) => {
  const user = await UserModel.get({PK: userId});
  if (!user) {
    throw new ResourceNotFoundError('Tài khoản không tồn tại');
  }
  delete data.role;
  delete data.userId;
  delete data.PK;

  if (data.password) {
    data.password = await UserUtil.hashPassword(data.password);
  } else {
    data.password = user.password;
  }

  await UserModel.update({PK: userId}, data);
};

const deleteUser = async (userId) => {
  const user = await UserModel.get({PK: userId});
  if (!user) {
    throw new ResourceNotFoundError('Tài khoản không tồn tại');
  }

  await user.delete();
};

module.exports.UserService = {
  createUser,
  generateOtp,
  getUserDetail,
  getUsers,
  changePassword,
  updateUser,
  deleteUser,
};
