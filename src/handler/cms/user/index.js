const {errorHandler} = require('../../../utils/error-handler.util');
const ResponseBuilder = require('../../../utils/response-builder.util');
const ValidatorUtil = require('../../../utils/request-validator.util');
const {CmsUserEndPoint} = require('./const');
const {UserService} = require('./user.service');
const UserRoles = require('../../../common/types/user-roles');
const {BadRequestError, PermissionDeniedError} = require('../../../common/exceptions');
const ErrorCode = require('../../../constants/error-code');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      case CmsUserEndPoint.CREATE_USER:
        data = await createUser(event);
        break;
      case CmsUserEndPoint.QR_GENERATOR:
        data = await qrGenerator(event);
        break;
      case CmsUserEndPoint.USER_ME:
        data = await getUserLogged(event);
        break;
      case CmsUserEndPoint.LIST_USER:
        data = await getUsers(event);
        break;
      case CmsUserEndPoint.CHANGE_PASSWORD:
        data = await changePassword(event);
        break;
      case CmsUserEndPoint.UPDATE_USER:
        data = await updateUser(event);
        break;
      case CmsUserEndPoint.DELETE_USER:
        data = await deleteUser(event);
        break;
      case CmsUserEndPoint.GET_USER_DETAIL:
        data = await getUserDetail(event);
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (error) {
    return errorHandler(error);
  }
};

async function createUser(event) {
  const requiredFields = ['userId', 'password', 'role'];
  await ValidatorUtil.requireParams(event.body, requiredFields);
  const body = JSON.parse(event.body);
  if (!UserRoles.isValidRole(body.role)) {
    throw new BadRequestError({
      code: ErrorCode.USER_ROLE_NOT_VALID,
      message: 'Role không đúng',
    });
  }
  const userRole = event.requestContext.authorizer.lambda.userRole;
  const permissionDeined = ![UserRoles.ADMIN, UserRoles.SUPER_ADMIN].includes(userRole);
  const actionDenied = UserRoles.isAdmin(userRole) && !UserRoles.isStaff(body.role);
  if (permissionDeined || actionDenied) {
    throw new PermissionDeniedError();
  }

  await UserService.createUser(body);

  return {successMessage: 'Tạo tài khoản thành công'};
}

async function qrGenerator(event) {
  const requiredFields = ['userId'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const body = JSON.parse(event.body);
  return await UserService.generateOtp(body.userId);
}

async function getUserLogged(event) {
  const userId = event.requestContext.authorizer.lambda.principalId;
  return await UserService.getUserDetail(userId);
}

async function getUsers(event) {
  const userRole = event.requestContext.authorizer.lambda.userRole;
  if (UserRoles.isSuperAdmin(userRole)) return await UserService.getUsers(userRole);
  return [];
}

async function changePassword(event) {
  const userId = event.pathParameters.userId;
  const requiredFields = ['newPassword'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const body = JSON.parse(event.body);
  await UserService.changePassword(userId, body.newPassword);

  return {successMessage: 'Đổi mật khẩu thành công!'};
}

async function updateUser(event) {
  const userRole = event.requestContext.authorizer.lambda.userRole;
  if (![UserRoles.SUPER_ADMIN, UserRoles.ADMIN].includes(userRole)) {
    throw new PermissionDeniedError();
  }
  const userId = event.pathParameters.userId;
  const body = JSON.parse(event.body);
  await UserService.updateUser(userId, body);

  return {successMessage: 'Cập nhật thành công!'};
}

async function deleteUser(event) {
  const userRole = event.requestContext.authorizer.lambda.userRole;
  if (![UserRoles.SUPER_ADMIN, UserRoles.ADMIN].includes(userRole)) {
    throw new PermissionDeniedError();
  }
  const userId = event.pathParameters.userId;
  await UserService.deleteUser(userId);

  return {successMessage: `Đã xóa ${userId}`};
}

async function getUserDetail(event) {
  const userRole = event.requestContext.authorizer.lambda.userRole;
  if (![UserRoles.SUPER_ADMIN, UserRoles.ADMIN].includes(userRole)) {
    throw new PermissionDeniedError();
  }
  const userId = event.pathParameters.userId;
  return await UserService.getUserDetail(userId);
}
