const ResponseBuilder = require('../../../utils/response-builder.util');
const {errorHandler} = require('../../../utils/error-handler.util');
const {CmsBlogEndPoint} = require('./const');
const ValidatorUtil = require('../../../utils/request-validator.util');
const {BlogService} = require('./blog.service');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      case CmsBlogEndPoint.GET_BLOGS:
        data = await getBlogs(event);
        break;
      case CmsBlogEndPoint.CREATE_BLOG:
        data = await createBlog(event);
        break;
      case CmsBlogEndPoint.UPDATE_BLOG:
        data = await updateBlog(event);
        break;
      case CmsBlogEndPoint.DELETE_BLOG:
        data = await deleteBlog(event);
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (error) {
    return errorHandler(error);
  }
};

const getBlogs = async (event) => {
  const type = event.queryStringParameters?.type;
  return BlogService.getBlogs(type.split(','));
};

const createBlog = async (event) => {
  const fields = ['title', 'description', 'banner', 'type'];
  await ValidatorUtil.requireParams(event.body, fields);
  const body = JSON.parse(event.body);
  return BlogService.createBlog(body);
};

const updateBlog = async (event) => {
  const fields = ['PK', 'SK'];
  await ValidatorUtil.requireParams(event.body, fields);
  const body = JSON.parse(event.body);
  return BlogService.updateBlog(body);
};

const deleteBlog = async (event) => {
  const fields = ['PK', 'SK'];
  await ValidatorUtil.requireParams(event.body, fields);
  const body = JSON.parse(event.body);
  return BlogService.deleteBlog(body);
};
