const BlogRepository = require('../../../repository/blog');
const {v4} = require('../../../utils/uuid.util');

const getBlogs = async (type) => {
  return BlogRepository.getBlogs(type);
};

const createBlog = async (body) => {
  const blogType = body.type.toUpperCase();
  const data = {
    title: body.title,
    description: body.description,
    banner: body.banner,
    button: body.button,
    type: blogType,
    sort: body.sort || 1,
  };
  switch (blogType) {
    case 'WORKSHOP':
    case 'CONCERT':
      data.PK = 'EVENT';
      data.SK = `EVENT_${v4()}`;
      break;
    case 'ALBUM':
      data.PK = blogType;
      data.SK = `${blogType}_${v4()}`;
      data.author = body.author;
      data.startTime = body.startTime;
      data.endTime = body.endTime;
      data.children = body.children;
      break;
    case 'PODCAST':
      data.PK = blogType;
      data.SK = `${blogType}_${v4()}`;
      data.author = body.author;
      data.file = body.file;
      break;
    default:
      data.PK = blogType;
      data.SK = `${blogType}_${v4()}`;
      break;
  }

  return BlogRepository.createBlog(data);
};

const updateBlog = async (data) => {
  return BlogRepository.updateBlog(data);
};

const deleteBlog = async (data) => {
  return BlogRepository.updateBlog({
    PK: data.PK,
    SK: data.SK,
    delFlg: true,
  });
};

module.exports.BlogService = {
  getBlogs,
  createBlog,
  updateBlog,
  deleteBlog,
};
