const ConfigRepository = require('../../../repository/config');
const {v4} = require('../../../utils/uuid.util');
const {CONFIG_MODEL_SKEY} = require('../../../common/types/config.type');

const getConfigs = async () => {
  return ConfigRepository.getConfigs();
};

const createConfig = async ({page, position, title, navigationUrl, sort, items}) => {
  let data = {
    PK: page,
    SK: v4(),
    title,
    position,
    navigationUrl,
    sort: sort || 1,
  };
  switch (position.split('_')[0]) {
    case CONFIG_MODEL_SKEY.BANNER:
    case CONFIG_MODEL_SKEY.ICON:
    case CONFIG_MODEL_SKEY.SECTION:
      data = {
        ...data,
        items,
      };
      break;
    case CONFIG_MODEL_SKEY.BUTTON:
    default:
      break;
  }

  return ConfigRepository.createConfig(data);
};

const updateConfig = async (data) => {
  return ConfigRepository.updateConfig(data);
};

const deleteConfig = async (data) => {
  return ConfigRepository.deleteConfig(data);
};

const sortConfig = async (configs) => {
  return ConfigRepository.batchUpdateConfigs(configs);
};

module.exports.ConfigService = {
  getConfigs,
  createConfig,
  updateConfig,
  deleteConfig,
  sortConfig,
};
