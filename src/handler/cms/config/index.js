const ResponseBuilder = require('../../../utils/response-builder.util');
const {errorHandler} = require('../../../utils/error-handler.util');
const {CmsConfigEndPoint} = require('./const');
const ValidatorUtil = require('../../../utils/request-validator.util');
const {ConfigService} = require('./config.service');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      case CmsConfigEndPoint.GET_CONFIGS:
        data = await getConfigs(event);
        break;
      case CmsConfigEndPoint.CREATE_CONFIG:
        data = await createConfig(event);
        break;
      case CmsConfigEndPoint.UPDATE_CONFIG:
        data = await updateConfig(event);
        break;
      case CmsConfigEndPoint.DELETE_CONFIG:
        data = await deleteConfig(event);
        break;
      case CmsConfigEndPoint.SORT_CONFIG:
        data = await sortConfig(event);
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (error) {
    return errorHandler(error);
  }
};

const getConfigs = async (event) => {
  return ConfigService.getConfigs();
};

const createConfig = async (event) => {
  const fields = ['page', 'position'];
  await ValidatorUtil.requireParams(event.body, fields);
  const body = JSON.parse(event.body);
  return ConfigService.createConfig(body);
};

const updateConfig = async (event) => {
  const fields = ['PK', 'SK'];
  await ValidatorUtil.requireParams(event.body, fields);
  const body = JSON.parse(event.body);
  return ConfigService.updateConfig(body);
};

const deleteConfig = async (event) => {
  const fields = ['PK', 'SK'];
  await ValidatorUtil.requireParams(event.body, fields);
  const body = JSON.parse(event.body);
  return ConfigService.deleteConfig(body);
};

const sortConfig = async (event) => {
  const fields = ['configs'];
  await ValidatorUtil.requireParams(event.body, fields);
  const body = JSON.parse(event.body);
  return ConfigService.sortConfig(body.configs);
};
