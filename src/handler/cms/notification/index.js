const {errorHandler} = require('../../../utils/error-handler.util');
const ResponseBuilder = require('../../../utils/response-builder.util');
const {NotificationEndpoint} = require('./const');
const {NotificationService} = require('./notification.service');
const ValidatorUtil = require('../../../utils/request-validator.util');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      case NotificationEndpoint.PUSH_NOTIFICATIONS:
        data = await pushNotification(event);
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (error) {
    return errorHandler(error);
  }
};

async function pushNotification(event) {
  const requiredFields = ['userIds', 'body'];
  await ValidatorUtil.requireParams(event.body, requiredFields);
  return NotificationService.pushNotification(JSON.parse(event.body));
}
