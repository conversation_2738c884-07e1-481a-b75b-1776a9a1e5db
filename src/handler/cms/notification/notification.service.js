const {NotificationModel} = require('../../../models/NotificationModel');
const {UserModel} = require('../../../models/UserModel');
const {v4} = require('../../../utils/uuid.util');
const {Expo} = require('expo-server-sdk');

const pushNotification = async (params) => {
  let notifications = [];
  const users = [];
  // lay thong tin user theo tung batch, moi batch 25 user
  for (let i = 0; i < params.userIds.length; i += 25) {
    const userIds = params.userIds.slice(i, i + 25);
    const usersBatch = await UserModel.batchGet(userIds);
    users.push(...usersBatch);
  }
  const messages = [];
  for (const user of users) {
    const message = {
      to: user.PK,
      body: params.body,
      title: params.title,
      data: params.data,
    };
    notifications.push({
      id: v4(16),
      userId: user.PK,
      sendAt: Date.now(),
      message,
    });
    const userMessages = user.notificationTokens
      ?.filter(({token}) => Expo.isExpoPushToken(token))
      .map(({token}) => ({
        ...message,
        to: token,
      }));
    messages.push(...userMessages);
    if (notifications.length === 25) {
      await NotificationModel.batchPut(notifications);
      notifications = [];
    }
  }
  if (!!notifications.length) {
    await NotificationModel.batchPut(notifications);
  }
  await pushNotificationToUser(messages);
  return {successMessage: 'Đã gửi thông báo thành công'};
};

const pushNotificationToUser = async (messages) => {
  const expo = new Expo();
  const chunks = expo.chunkPushNotifications(messages);
  const tickets = [];
  for (const chunk of chunks) {
    const ticketChunk = await expo.sendPushNotificationsAsync(chunk);
    tickets.push(...ticketChunk);
  }
  const receiptIds = tickets.filter((ticket) => ticket.status === 'ok').map((ticket) => ticket.id);
  const receiptIdChunks = expo.chunkPushNotificationReceiptIds(receiptIds);
  for (const chunk of receiptIdChunks) {
    try {
      const receipts = await expo.getPushNotificationReceiptsAsync(chunk);
      // The receipts specify whether Apple or Google successfully received the
      // notification and information about an error, if one occurred.
      // eslint-disable-next-line guard-for-in
      for (const receiptId in receipts) {
        const {status, message, details} = receipts[receiptId];
        if (status === 'ok') {
          continue;
        } else if (status === 'error') {
          console.error(`There was an error sending a notification: ${message}`);
          if (details && details.error) {
            // The error codes are listed in the Expo documentation:
            // https://docs.expo.io/push-notifications/sending-notifications/#individual-errors
            // You must handle the errors appropriately.
            console.error(`The error code is ${details.error}`);
          }
        }
      }
    } catch (error) {
      console.error(error);
    }
  }
};

module.exports.NotificationService = {
  pushNotification,
};
