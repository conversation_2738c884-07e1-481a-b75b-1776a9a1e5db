const ResponseBuilder = require('../../../utils/response-builder.util');
const {errorHandler} = require('../../../utils/error-handler.util');
const {CmsCourseEndPoint} = require('./const');
const ValidatorUtil = require('../../../utils/request-validator.util');
const {CourseService} = require('./course.service');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      case CmsCourseEndPoint.CREATE_LESSON:
        data = await createLesson(event);
        break;
      case CmsCourseEndPoint.GET_LESSON:
        data = await getLesson(event);
        break;
      case CmsCourseEndPoint.UPDATE_LESSON:
        data = await updateLesson(event);
        break;
      case CmsCourseEndPoint.DELETE_LESSON:
        data = await deleteLesson(event);
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (error) {
    return errorHandler(error);
  }
};

const createLesson = async (event) => {
  const courseId = event.pathParameters.courseId;
  const fields = ['title', 'type'];
  await ValidatorUtil.requireParams(event.body, fields);
  const body = JSON.parse(event.body);
  return CourseService.createLesson({courseId, ...body});
};

const getLesson = async (event) => {
  const courseId = event.pathParameters.courseId;
  const lessonId = event.pathParameters.lessonId;
  return CourseService.getLesson(courseId, lessonId);
};

const updateLesson = async (event) => {
  const courseId = event.pathParameters.courseId;
  const lessonId = event.pathParameters.lessonId;
  const body = JSON.parse(event.body);
  return CourseService.updateLesson({courseId, lessonId, ...body});
};

const deleteLesson = async (event) => {
  const courseId = event.pathParameters.courseId;
  const lessonId = event.pathParameters.lessonId;
  return CourseService.deleteLesson(courseId, lessonId);
};
