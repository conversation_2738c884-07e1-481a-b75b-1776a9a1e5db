const CourseRepository = require('../../../repository/course');
const {v4} = require('../../../utils/uuid.util');

const createLesson = async (data) => {
  const lessonType = data.type.toUpperCase();
  const lessonData = {
    PK: data.courseId,
    SK: v4(),
    title: data.title,
    type: lessonType,
    sort: data.sort || 1,
  };
  switch (lessonType) {
    case 'VIDEO':
      lessonData.videoUrl = data.videoUrl;
      break;
    case 'QUIZ':
      lessonData.questions = data.questions;
      break;
    default:
      break;
  }

  return CourseRepository.createLesson(lessonData);
};

const getLesson = async (courseId, lessonId) => {
  return CourseRepository.getLesson({
    PK: courseId,
    SK: lessonId,
  });
};

const updateLesson = async (data) => {
  return CourseRepository.updateLesson({
    ...data,
    PK: data.courseId,
    SK: data.lessonId,
  });
};

const deleteLesson = async (courseId, lessonId) => {
  return CourseRepository.deleteLesson({
    PK: courseId,
    SK: lessonId,
  });
};

module.exports.CourseService = {
  createLesson,
  getLesson,
  updateLesson,
  deleteLesson,
};
