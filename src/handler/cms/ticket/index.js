// const {LambdaClient} = require('../../../common/lambda');
const {errorHandler} = require('../../../utils/error-handler.util');
const ResponseBuilder = require('../../../utils/response-builder.util');
const ValidatorUtil = require('../../../utils/request-validator.util');
const {CmsTicketEndPoint} = require('./const');
const {TicketProcess} = require('./ticket-process');
const {TicketService} = require('./ticket.service');
const {SeatmapService} = require('./seatmap.service');
const {LambdaClient} = require('../../../common/lambda');
const {BadRequestError} = require('../../../common/exceptions');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;

  try {
    let data;
    switch (routeKey) {
      case CmsTicketEndPoint.SCAN_TICKET:
        data = await checkTicket(event);
        break;
      case CmsTicketEndPoint.CHECKIN_TICKET:
        data = await checkInTicket(event);
        break;
      case CmsTicketEndPoint.GET_TICKETS:
        data = await getTickets(event);
        break;
      case CmsTicketEndPoint.DELETE_TICKETS:
        data = await deleteTickets(event);
        break;
      case CmsTicketEndPoint.ACTIONS_TICKETS:
        data = await actionsTickets(event);
        break;
      case CmsTicketEndPoint.GET_ALL_TICKETS:
        data = await getAllTickets(event);
        break;
      case CmsTicketEndPoint.GET_SEATMAP:
        data = await getSeatmap(event);
        break;
      case CmsTicketEndPoint.CREATE_SEATMAP:
        data = await createSeatmap(event);
        break;
      case CmsTicketEndPoint.UPDATE_SEATMAP:
        data = await updateSeatmap(event);
        break;
      case CmsTicketEndPoint.RESEND_TICKET:
        data = await resendTickets(event);
        break;
      case CmsTicketEndPoint.UPDATE_TICKETS:
        data = await updateTickets(event);
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (error) {
    return errorHandler(error);
  }
};

async function checkTicket(event) {
  // const requiredFields = ['orderId', 'ticketId', 'productId'];
  // await ValidatorUtil.requireParams(event.body, requiredFields);

  const body = JSON.parse(event.body);
  const ticket = new TicketProcess(body.generateCode || body.refId);
  await ticket.process();

  return ticket.info;
}

async function checkInTicket(event) {
  // const requiredFields = ['orderId', 'ticketId', 'productId'];
  // await ValidatorUtil.requireParams(event.body, requiredFields);

  const body = JSON.parse(event.body);
  await TicketService.updateTicketStatus(body.generateCode || body.refId);

  LambdaClient.invoke(process.env.LAMBDA_BROADCAST, {
    topicName: 'CHECK_IN_TICKET',
    body,
  });

  return {successMessage: 'Check-in thành công'};
}

async function getTickets(event) {
  const userId = event.requestContext.authorizer.lambda.principalId;
  return await TicketService.getEventTickets(userId);
}

async function deleteTickets(event) {
  // TODO:
}

async function actionsTickets(event) {
  const userId = event.requestContext.authorizer.lambda.principalId;
  const body = JSON.parse(event.body);
  return await TicketService.actionsTickets(body, userId);
}

async function getAllTickets(params) {
  return await TicketService.getAllTickets(params.queryStringParameters);
}

async function getSeatmap(params) {
  return await SeatmapService.getSeatmap(params.queryStringParameters);
}

async function createSeatmap(params) {
  return await SeatmapService.createSeatmap(JSON.parse(params.body));
}

async function updateSeatmap(params) {
  return await SeatmapService.editSeatmap(JSON.parse(params.body));
}

async function resendTickets(event) {
  const eventId = event.pathParameters.eventId;
  const orderIds = JSON.parse(event.body);
  if (!orderIds?.length) {
    throw new BadRequestError({
      code: 3330001,
      message: 'Mã đơn hàng không được để trống',
    });
  }
  const uniqueIds = Array.from(new Set(orderIds));
  await TicketService.resendTickets(eventId, uniqueIds);
}

async function updateTickets(event) {
  const requiredFields = [
    'orderId',
    'eventId',
    'customerEmail',
    'customerName',
    'customerPhone',
    'customerExtraData',
  ];
  await ValidatorUtil.requireParams(event.body, requiredFields);
  const body = JSON.parse(event.body);
  return await TicketService.updateTickets(body);
}
