const {pick} = require('lodash');
const moment = require('moment');
const {BadRequestError, ResourceNotFoundError} = require('../../../common/exceptions');
const ErrorCode = require('../../../constants/error-code');
const {EventModel} = require('../../../models/EventModel');
const {EventTicketsModel} = require('../../../models/EventTIckets');
const {OrderModel} = require('../../../models/OrderModel');
const {UserModel} = require('../../../models/UserModel');
const ticketModel = require('../../../../drizzle/models/ticket');
const {P_EVENT} = require('../../../common/types/product-type');
const db = require('../../../../drizzle/db');
const schema = require('../../../../drizzle/schema');
const {inArray} = require('drizzle-orm');
const {v4} = require('../../../utils/uuid.util');
const OrderStatus = require('../../../common/types/order-status');
const {sendEmail} = require('../../../utils/email-util');
const CollaborationType = require('../../../common/types/collaboration.type');
const {getTicketClassesById} = require('../../../../drizzle/models/ticket-class');
const {TicketDao} = require('./ticket.dao');
const PaymentGatewayTypes = require('../../../common/types/payment-gateway.type');
const TicketStatus = require('../../../common/types/ticket-status.type');

const getOrder = async ({productId, orderId}) => {
  return await OrderModel.get({
    PK: productId,
    orderId,
  });
};

const updateTicketStatus = async (generateCode) => {
  const ticketValidError = new BadRequestError({
    code: ErrorCode.TICKET_INVALID,
    message: 'Vé không hợp lệ',
  });

  const [ticket] = await ticketModel.getTicketByGenerateCode(generateCode);
  if (!ticket) {
    throw ticketValidError;
  }

  if (ticket.checkin) {
    throw new BadRequestError({
      code: ErrorCode.TICKET_CHECKED_IN,
      message: 'Vé đã được check-in',
    });
  }
  await ticketModel.updateCheckinStatusByGenerateCode(generateCode);
  await EventTicketsModel.update(
    {
      eventId: ticket.eventId,
      refId: ticket.generateCode,
    },
    {
      checkedIn: true,
      checkinTime: new Date(),
    },
  );
};

const getEvent = async (productId) => {
  return await EventModel.get({PK: productId});
};

const getEventTickets = async (userId) => {
  const staff = await UserModel.get({PK: userId});
  const eventIds = staff.assignedEvents;
  if (!eventIds?.length) {
    throw new BadRequestError({
      code: ErrorCode.STAFF_NOT_ASSIGNED,
      message: 'Nhân viên chưa được phân vào sự kiện',
    });
  }

  const getTickets = async (eventId) => {
    const [event, tickets] = await Promise.all([
      EventModel.get({PK: eventId}),
      EventTicketsModel.query({eventId}).all().exec(),
    ]);

    const {paymentInfoFields = []} = event.general;
    const customFields = paymentInfoFields.filter((f) => !f.isDefault);

    return tickets.toJSON().map((t) => {
      return {
        ...t,
        seatCode: t.seatCode || 'N/A',
        ticketName: t.ticketType,
        account: t.paymentInfo.email || t.userId,
        phoneNumber: t.paymentInfo.phoneNumber,
        customValues: customFields.map((f) => ({
          key: f.key,
          text: f.text,
          value: t.paymentInfo[f.key] || '--',
        })),
        event: {
          eventName: event.eventName,
          location: event.location,
          startDate: event.startDate,
          endDate: event.endDate,
        },
        ticketType: undefined,
        paymentInfo: undefined,
      };
    });
  };

  const promises = eventIds.map((eventId) => getTickets(eventId));
  const tickets = await Promise.all(promises).then((res) => res.flat());

  return tickets;
};

const actionsTickets = async (data, userId) => {
  const ids = data.tickets.map((t) => t.id);
  if (data.action === TicketStatus.BLOCK) {
    return ticketModel.updateTicketsStatus({
      eventId: data.eventId,
      calendarId: data.calendarId,
      ids,
      status: TicketStatus.BLOCK,
      inStatus: TicketStatus.IDLE,
    });
  }
  if (data.action === 'unblock') {
    return ticketModel.updateTicketsStatus({
      eventId: data.eventId,
      calendarId: data.calendarId,
      ids,
      status: TicketStatus.IDLE,
      inStatus: TicketStatus.BLOCK,
    });
  }
  if (data.action === 'cancel') {
    // cancel order
    const tickets = await TicketDao.getTicketsByIds(ids);

    const orderIds = Array.from(new Set(tickets.map((t) => t.orderId).filter((t) => !!t)));
    const storeTicketFields = [
      'id',
      'calendarId',
      'rowId',
      'rowName',
      'zoneId',
      'zoneName',
      'ticketClassId',
      'ticketClassName',
      'ticketClassDesc',
      'code',
      'checkin',
      'position',
      'generateCode',
      'ticketClassPrototypeUrl',
      'finalPriceVn',
      'finalPriceUsd',
    ];

    for (const orderId of orderIds) {
      await OrderModel.update(
        {PK: data.eventId, orderId},
        {
          status: OrderStatus.CANCELED,
          cancelReason: 'Huỷ vé từ CMS',
          cancelBy: userId,
          cancelTickets: tickets.map((t) => pick(t, storeTicketFields)),
        },
      );
      const deleteEventTickets = await EventTicketsModel.query('eventId')
        .eq(data.eventId)
        .where('orderId')
        .eq(orderId)
        .exec()
        .then((eTickets) => eTickets.map((eTicket) => eTicket.delete()));
      await Promise.all(deleteEventTickets);
    }
    await TicketDao.cancelTickets({
      eventId: data.eventId,
      calendarId: data.calendarId,
      orderIds,
    });
  }

  if (data.action === 'checkin') {
    return ticketModel.updateTicketCheckinStatus({
      eventId: data.eventId,
      calendarId: data.calendarId,
      ids,
      checkin: true,
    });
  }
  if (data.action === 'not-checkin') {
    return ticketModel.updateTicketCheckinStatus({
      eventId: data.eventId,
      calendarId: data.calendarId,
      ids,
      checkin: false,
    });
  }
  if (data.action === 'sell-manually') {
    const event = await EventModel.get({PK: data.eventId});
    if (!event) {
      throw new ResourceNotFoundError('Không tìm th/Dk sự kiện');
    }
    const ticketClassIds = new Set();
    data.tickets.forEach((t) => {
      ticketClassIds.add(t.ticketClassId);
    });
    const extraData = {
      productId: data.eventId,
      productType: P_EVENT,
      domain: event.toJSON().domain,
    };
    let amountVn = 0;
    let amountUsd = 0;
    let originalAmountVn = 0;
    let originalAmountUsd = 0;

    const ticketsMail = [];
    const priceByTicketClass = {};
    const tickets = await TicketDao.getTicketsByIds(ids);
    const ticketClass = await db
      .select()
      .from(schema.ticketClass)
      .where(inArray(schema.ticketClass.id, [...ticketClassIds]));

    const ticketCommonFields = [
      'id',
      'calendarId',
      'name',
      'color',
      'originalPriceVn',
      'originalPriceUsd',
      'finalPriceVn',
      'quantity',
      'finalPriceUsd',
      'description',
      'seatType',
      'prototypeUrl',
    ];
    const ticketsOrder = ticketClass.map((t) => ({
      ...pick(t, ticketCommonFields),
      quantity: tickets.filter((item) => item.ticketClassId === t.id).length,
    }));

    ticketClass.forEach((v) => {
      priceByTicketClass[v.id] = {
        finalPriceVn: v.finalPriceVn,
        finalPriceUsd: v.finalPriceUsd,
        originalPriceVn: v.originalPriceVn,
        originalPriceUsd: v.originalPriceUsd,
      };
    });

    tickets.forEach((ticket) => {
      amountVn += priceByTicketClass[ticket.ticketClassId].finalPriceVn;
      amountUsd += priceByTicketClass[ticket.ticketClassId].finalPriceUsd;
      originalAmountVn += priceByTicketClass[ticket.ticketClassId].originalPriceVn;
      originalAmountUsd += priceByTicketClass[ticket.ticketClassId].originalPriceUsd;
      ticketsMail.push({
        ticketId: ticket.id,
        ticketType: ticket.ticketClassName,
        refId: ticket.generateCode,
        calendarId: data.calendarId,
        totalAmount: priceByTicketClass[ticket.ticketClassId].finalPriceVn,
        deliveryInfo: {
          seatCode: `${ticket.zoneName}-${ticket.rowName}-${ticket.code}`,
        },
        description: ticket.ticketClassDesc,
        prototypeUrl: ticket.ticketClassPrototypeUrl,
        finalPriceVn: ticket.finalPriceVn,
        finalPriceUsd: ticket.finalPriceUsd,
      });
    });

    const customFields = {};
    for (const [key, value] of Object.entries(data.customFields)) {
      customFields[key] = value;
    }
    const order = {
      PK: data.eventId,
      orderId: v4(),
      originalAmount: originalAmountVn,
      originalAmountUsd: originalAmountUsd,
      amount: amountVn,
      amountInUSD: amountUsd,
      currencyCode: 'VND',
      tickets: ticketsOrder,
      productType: P_EVENT,
      extraData,
      userId: userId,
      status: OrderStatus.SUCCESS,
      paymentInfo: {
        name: data.customerName,
        email: data.customerEmail,
        phoneNumber: data.customerPhone,
        ...customFields,
      },
      sellFromPlatform: 'cms',
      paymentGateway: PaymentGatewayTypes.MANUALLY,
      paymentTime: new Date(),
    };
    await OrderModel.create(order);
    await sendEventTicketInvoice({...order, flatTickets: ticketsMail}, event);

    await TicketDao.updateTicketStatusForSellManually({
      ticketIds: ids,
      status: TicketStatus.SOLD,
      customerName: data.customerName,
      customerEmail: data.customerEmail,
      customerPhone: data.customerPhone,
      customerExtraData: customFields,
      orderId: order.orderId,
    });

    // sync to table EventTickets
    const eventTickets = ticketsMail.map((ticket) => ({
      eventId: data.eventId,
      refId: ticket.refId,
      orderId: order.orderId,
      calendarId: ticket.calendarId,
      paymentGateway: order.paymentGateway,
      paymentInfo: order.paymentInfo,
      paymentTime: order.paymentTime,
      ticketDescription: ticket.description,
      ticketId: ticket.ticketId,
      ticketPrice: ticket.finalPriceVn,
      ticketPriceInUSD: ticket.finalPriceUsd,
      ticketType: ticket.ticketType,
      totalAmount: order.amount,
      userId: data.customerEmail,
      deliveryInfo: ticket.deliveryInfo,
      sellFromPlatform: 'cms',
    }));
    await EventTicketsModel.batchPut(eventTickets);
  }
};

const sendEventTicketInvoice = async (order, product) => {
  process.env.TZ = 'Asia/Ho_Chi_Minh';
  const userEmail = order.paymentInfo.email;
  const [calendarId] = order.flatTickets.map((item) => item.calendarId);
  const eventDate = product.eventCalendar?.find((item) => item.calendarId === calendarId);
  const payload = {
    orderId: order.orderId,
    type: 'Vé điện tử',
    customerName: order.paymentInfo.name,
    customerPhoneNumber: order.paymentInfo.phoneNumber,
    eventNote: product.note,
    userEmail: userEmail,
    thumbnailUrl: product.thumbnail,
    eventName: product.eventName,
    eventDate: eventDate ? moment(eventDate.startDate).format('DD/MM/YYYY') : '',
    paymentTime: moment(Date.now()).format('DD/MM/YYYY HH:mm:ss'),
    tickets: order.flatTickets.map((t) => ({
      ...t,
      totalAmount: t.totalAmount.toLocaleString(),
      quantity: 1,
      seatCode: t.deliveryInfo.seatCode,
      qrUrl: `https://quickchart.io/chart?cht=qr&chld=H%7C1&chs=200x200&chl=${encodeURIComponent(
        JSON.stringify({generateCode: t.refId}),
      )}`,
      ticketImage: t.prototypeUrl || product.thumbnail,
    })),
    totalAmount: order.amount.toLocaleString(),
  };

  let subject = '[Eventista] Eventista gửi bạn thông tin vé và chỗ ngồi';
  let template = 'event-ticket-ticket-info';
  if (!CollaborationType.isEventista(product.general?.collaboration)) {
    subject = '[Eventista] Xác nhận mua vé thành công tại Eventista';
    template = 'event-ticket-invoice';
    payload.tickets = order.tickets;
  }

  await sendEmail({
    to: userEmail,
    subject,
    context: payload,
    template,
  });
  console.log('Send ticket invoice successfully to [%s]', order.paymentInfo.email);
};

const getAllTickets = async (data) => {
  return await ticketModel.getAllTicketsByEventAndCalendar(data);
};

const resendTickets = async (eventId, orderIds) => {
  const eventInfo = await EventModel.get({PK: eventId});
  if (!eventInfo) {
    throw new ResourceNotFoundError('Sự kiện không tồn tại');
  }
  const sendMail = async (orderId) => {
    const order = await OrderModel.get({
      PK: eventId,
      orderId: orderId,
    });
    const tickets = await ticketModel.getTicketsByOrderId(orderId);
    const [ticketClass] = await getTicketClassesById(tickets[0].ticketClassId);
    order.flatTickets = tickets.map((ticket) => ({
      ticketId: ticket.id,
      ticketType: ticket.ticketClassName,
      refId: ticket.generateCode,
      calendarId: ticket.calendarId,
      totalAmount: ticketClass.finalPriceVn || 0,
      deliveryInfo: {
        seatCode: `${ticket.zoneName}-${ticket.rowName}-${ticket.code}`,
      },
      ticketImage: ticketClass.prototypeUrl || eventInfo.thumbnail,
      customQr: {generateCode: ticket.generateCode},
    }));

    await sendEventTicketInvoice(order, eventInfo);
  };
  let promomises = [];
  for (const orderId of orderIds) {
    promomises.push(sendMail(orderId));
    if (promomises.length >= 20) {
      await Promise.all(promomises);
      promomises = [];
    }
  }
  if (!!promomises.length) {
    await Promise.all(promomises);
  }
};

const updateTickets = async (data) => {
  const order = await OrderModel.get({PK: data.eventId, orderId: data.orderId});
  if (!order) {
    throw new ResourceNotFoundError('Đơn hàng không tồn tại');
  }
  order.paymentInfo = {
    ...order.paymentInfo,
    ...data.customerExtraData,
    email: data.customerEmail,
    name: data.customerName,
    phoneNumber: data.customerPhone,
  };
  await order.save();

  return await ticketModel.updateTickets(data);
};

module.exports.TicketService = {
  getOrder,
  updateTicketStatus,
  getEvent,
  getAllTickets,
  getEventTickets,
  actionsTickets,
  resendTickets,
  updateTickets,
};
