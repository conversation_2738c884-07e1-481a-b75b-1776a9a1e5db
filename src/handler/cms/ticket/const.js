const CmsTicketEndPoint = {
  SCAN_TICKET: 'POST /v1/cms/ticket/check-in',
  CHECKIN_TICKET: 'PUT /v1/cms/ticket/check-in',
  GET_TICKETS: 'GET /v1/cms/ticket',
  DELETE_TICKETS: 'DELETE /v2/cms/ticket',
  ACTIONS_TICKETS: 'POST /v2/cms/ticket/actions',
  GET_ALL_TICKETS: 'GET /v2/cms/tickets',
  GET_SEATMAP: 'GET /v2/cms/seatmap',
  CREATE_SEATMAP: 'POST /v2/cms/seatmap',
  UPDATE_SEATMAP: 'PUT /v2/cms/seatmap',
  RESEND_TICKET: 'POST /v2/cms/event/{eventId}/ticket/send',
  UPDATE_TICKETS: 'PUT /v2/cms/tickets',
};

const ticketStatus = {
  IDLE: 'idle',
  CHECKIN: 'checkin',
  BLOCK: 'block',
  SOLD: 'sold',
  HOLD: 'hold',
  CANCEL: 'cancel',
};

module.exports = {
  CmsTicketEndPoint,
  ticketStatus,
};
