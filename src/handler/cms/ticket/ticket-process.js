const {getTicketByGenerateCode} = require('../../../../drizzle/models/ticket');
const {BadRequestError} = require('../../../common/exceptions');
const ErrorCode = require('../../../constants/error-code');
const {DatetimeUtil} = require('../../../utils/datetime.util');
const {TicketService} = require('./ticket.service');

class TicketProcess {
  #requestTicket;
  #tickets;
  #ticket;
  #event;
  #order;
  #generateCode;

  constructor(generateCode) {
    this.#generateCode = generateCode;
  }

  async process() {
    await this.#checkTicket();
    await this.#getOrder();
    await this.#getEvent();
  }

  async #getOrder() {
    const order = await TicketService.getOrder(this.#requestTicket);
    if (!order) {
      throw new BadRequestError({
        code: ErrorCode.TICKET_INVALID,
        message: 'Vé không hợp lệ',
      });
    }
    this.#order = order;
  }

  async #getEvent() {
    this.#event = await TicketService.getEvent(this.#requestTicket.productId);
  }

  #checkTicket() {
    return new Promise(async (resolve, reject) => {
      const [ticket] = await getTicketByGenerateCode(this.#generateCode);
      if (!ticket) {
        reject(
          new BadRequestError({
            code: ErrorCode.TICKET_INVALID,
            message: 'Vé không hợp lệ',
          }),
        );
        return;
      }
      this.#requestTicket = {
        productId: ticket.eventId,
        orderId: ticket.orderId,
      };
      this.#ticket = ticket;
      resolve();
    });
  }

  get info() {
    const {paymentInfoFields = []} = this.#event.general;
    const customFields = paymentInfoFields.filter((f) => !f.isDefault);

    return {
      productId: this.#requestTicket.productId,
      orderId: this.#requestTicket.orderId,
      ticket: {
        name: this.#ticket.ticketName,
        ticketId: this.#ticket.id,
        refId: this.#generateCode,
        checkedIn: !!this.#ticket.checkin,
        checkinTime: this.#ticket.checkinTime,
        paymentTime: this.#order.paymentTime,
        seatCode: `${this.#ticket.zoneName}-${this.#ticket.rowName}-${this.#ticket.code}`,
        account: this.#order.paymentInfo?.email || this.#order.userId,
        phoneNumber: this.#order.paymentInfo?.phoneNumber || 'N/A',
        customValues: customFields.map((f) => ({
          key: f.key,
          text: f.text,
          value: this.#order.paymentInfo[f.key] || '--',
        })),
      },
      event: {
        eventName: this.#event.eventName,
        location: this.#event.location,
        ...DatetimeUtil.getTicketEventTime(this.#event.eventCalendar),
      },
    };
  }
}

module.exports = {TicketProcess};
