const {inArray, eq, and} = require('drizzle-orm');
const schema = require('../.././../../drizzle/schema');
const TicketStatus = require('../../../common/types/ticket-status.type');
const {withConnection} = require('../../../../drizzle/db');

const updateTicketStatusForSellManually = async (data) => {
  return withConnection(async (db) => {
    const result = await db
      .update(schema.tickets)
      .set({
        customerEmail: data.customerEmail,
        customerName: data.customerName,
        customerPhone: data.customerPhone,
        customerExtraData: data.customerExtraData,
        status: data.status,
        orderId: data.orderId,
      })
      .where(inArray(schema.tickets.id, data.ticketIds));
    return result;
  });
};

const getTicketsByIds = async (ids) => {
  return withConnection(async (db) => {
    const result = await db
      .select({
        ...schema.tickets,
        ticketClassName: schema.ticketClass.name,
        ticketClassDesc: schema.ticketClass.description,
        ticketClassPrototypeUrl: schema.ticketClass.prototypeUrl,
        rowName: schema.rows.name,
        zoneName: schema.zones.name,
        finalPriceVn: schema.ticketClass.finalPriceVn,
        finalPriceUsd: schema.ticketClass.finalPriceUsd,
      })
      .from(schema.tickets)
      .leftJoin(schema.rows, eq(schema.tickets.rowId, schema.rows.id))
      .leftJoin(schema.zones, eq(schema.tickets.zoneId, schema.zones.id))
      .leftJoin(schema.ticketClass, eq(schema.tickets.ticketClassId, schema.ticketClass.id))
      .where(inArray(schema.tickets.id, ids));
    return result;
  });
};

const cancelTickets = async (data) => {
  return withConnection(async (db) => {
    await db
      .update(schema.tickets)
      .set({
        status: TicketStatus.IDLE,
        customerEmail: null,
        customerExtraData: null,
        customerName: null,
        customerPhone: null,
        customerNote: null,
        orderId: null,
        checkin: false,
        soldAt: null,
        paymentGateway: null,
        checkinAt: null,
        checkinBy: null,
      })
      .where(
        and(
          eq(schema.tickets.eventId, data.eventId),
          inArray(schema.tickets.orderId, data.orderIds),
        ),
      );
  });
};

module.exports.TicketDao = {
  updateTicketStatusForSellManually,
  getTicketsByIds,
  cancelTickets,
};
