const SeatmapModel = require('../../../../drizzle/models/seatmap');

const createSeatmap = async (data) => {
  const result = await SeatmapModel.createSeatmap(data);
  return result;
};

const getSeatmap = async (data) => {
  const result = await SeatmapModel.getSeatmap(data);
  return result;
};

const editSeatmap = async (data) => {
  const result = await SeatmapModel.editSeatmap(data);
  return result;
};

module.exports.SeatmapService = {
  createSeatmap,
  getSeatmap,
  editSeatmap,
};
