const moment = require('moment');
const {orderBy} = require('lodash');
const ProductTypes = require('../../../common/types/product-type');
const OrderStatus = require('../../../common/types/order-status');
const {EventModel} = require('../../../models/EventModel');
const {EventTicketsModel} = require('../../../models/EventTIckets');
const {FanpassModel} = require('../../../models/FanpassModel');
const {OrderModel} = require('../../../models/OrderModel');
const {UserFanpassModel} = require('../../../models/UserFanpassModel');
const {jsonToBase64, base64ToJson} = require('../../../utils/object.util');
const {UserModel} = require('../../../models/UserModel');
const DeliveryStatus = require('../../../common/types/delivery-status');
const {ResourceNotFoundError} = require('../../../common/exceptions');
const {sendEmail} = require('../../../utils/email-util');
const {OrderUtil} = require('../../../utils/order.util');
const PaymentGatewayTypes = require('../../../common/types/payment-gateway.type');
const {TicketDao} = require('./ticket.dao');

const calculateTicketPriceWithPromotion = (
  {ticketId, ticketPrice, ticketPriceInUSD, paymentGateway},
  promotion,
) => {
  if (!promotion || (promotion.appliesTo === 'ticket' && !promotion.tickets.includes(ticketId))) {
    return {
      amount:
        PaymentGatewayTypes.getCurrencyCode(paymentGateway) === 'VND'
          ? ticketPrice
          : ticketPriceInUSD,
      ticketPriceAfterDiscount: 0,
      ticketPriceInUSDAfterDiscount: 0,
    };
  }
  const {amount, amountInUSD} = OrderUtil.calcTicketPriceWithPromotion(
    {ticketPrice, quantity: 1, ticketPriceInUSD},
    {...promotion, percentInUSD: promotion.discountPercentInUSD},
  );

  return {
    ticketPriceAfterDiscount: amount,
    ticketPriceInUSDAfterDiscount: amountInUSD,
  };
};

const getTicketReports = async (params) => {
  process.env.TZ = 'Asia/Ho_Chi_Minh';
  const {
    productId,
    ticketId,
    checkedIn,
    startDate,
    endDate,
    limit,
    lastKey,
    paymentGateway,
    calendarId,
  } = params;

  const ticketExe = EventTicketsModel.query({eventId: productId});
  if (ticketId) {
    ticketExe.where('ticketId').in(ticketId.split(','));
  }
  if (checkedIn) {
    ticketExe.where('checkedIn').eq(/^true$/i.test(checkedIn));
  }
  if (paymentGateway) {
    ticketExe.where('paymentGateway').in(paymentGateway.split(','));
  }
  if (startDate && endDate) {
    ticketExe.where('paymentTime').between(+startDate, +endDate);
  }
  if (calendarId) {
    ticketExe.where('calendarId').in(calendarId.split(','));
  }

  // todo:
  if (lastKey) {
    ticketExe.startAt(base64ToJson(lastKey));
  }
  if (+limit === -1) {
    ticketExe.all();
  } else {
    ticketExe.limit(parseInt(limit) || 10);
  }

  const tickets = await ticketExe.exec();
  const ticketInventories = await TicketDao.getTicketsByEventId(productId);

  const product = await EventModel.get({PK: productId}, {attributes: ['eventName']});

  const getTicketClassId = (id) => {
    const t = ticketInventories.find((i) => i.id === id);
    return t?.ticketClassId;
  };

  const orderTickets = tickets.toJSON().map((ticket) => ({
    ticketId: ticket.ticketId,
    refId: ticket.refId,
    productId: ticket.eventId,
    orderId: ticket.orderId,
    invoiceNumber: 'N/A',
    paymentTime: ticket.paymentTime,
    ticketPrice: ticket.ticketPrice,
    ticketPriceInUSD: ticket.ticketPriceInUSD,
    ...calculateTicketPriceWithPromotion(
      {...ticket, ticketId: getTicketClassId(ticket.ticketId)},
      ticket.promotionApplied,
    ),
    totalAmount: ticket.totalAmount,
    amount: ticket.ticketPrice,
    ticketName: ticket.ticketType,
    checkedIn: ticket.checkedIn,
    paymentGateway: ticket.paymentGateway,
    paymentStatus: 'Giao dịch thành công',
    productName: product.eventName,
    userId: ticket.userId,
    paymentInfo: ticket.paymentInfo,
    deliveryInfo: ticket.deliveryInfo,
    promotionApplied: ticket.promotionApplied,
    currencyCode: PaymentGatewayTypes.getCurrencyCode(ticket.paymentGateway),
    date: moment(ticket.paymentTime).format('DD-MM-YYYY'),
  }));

  const calculateRevenue = (value) => {
    const orders = orderTickets.filter((item) => item.currencyCode === value);
    const calculator = (ticket) => {
      if (!!ticket.promotionApplied) {
        return value === 'VND'
          ? ticket.ticketPriceAfterDiscount || 0
          : ticket.ticketPriceInUSDAfterDiscount || 0;
      }

      return value === 'VND' ? ticket.ticketPrice : ticket.ticketPriceInUSD;
    };

    return orders.reduce((a, c) => a + calculator(c), 0);
  };

  // revenue by currency
  const currencyCodes = Array.from(new Set(orderTickets.map((item) => item.currencyCode)));
  const revenueByCurrencyCode = currencyCodes.map((code) => ({
    currencyCode: code,
    amount: calculateRevenue(code),
  }));

  // tickets sold by date
  const ticketList = [
    ...new Map(
      orderTickets.map((item) => [
        item.ticketId,
        {ticketId: item.ticketId, ticketName: item.ticketName},
      ]),
    ).values(),
  ];
  const dateList = Array.from(new Set(orderTickets.map((item) => item.date)));
  const calculateTicketSold = (ticketId) =>
    dateList.map((date) => ({
      date,
      ticketCount: orderTickets.filter((item) => item.date === date && item.ticketId === ticketId)
        .length,
    }));

  const ticketSoldByDate = ticketList.map(({ticketId, ticketName}) => ({
    ticketId,
    ticketName,
    data: calculateTicketSold(ticketId),
  }));

  // transaction info
  const totalOrders = Array.from(new Set(orderTickets.map((item) => item.orderId)));
  const transaction = {
    totalTickets: orderTickets.length,
    totalOrders: totalOrders.length,
  };

  // analytics
  const revenueByDate = currencyCodes.map((code) => ({
    currencyCode: code,
    data: dateList.map((date) => ({
      date,
      revenue: orderTickets
        .filter((item) => item.date === date && item.currencyCode === code)
        .reduce((a, c) => a + c.ticketPrice, 0),
    })),
  }));

  return {
    lastKey: !!tickets.count ? jsonToBase64(tickets.lastKey) : null,
    revenueByCurrencyCode,
    transaction,
    ticketSoldByDate,
    revenueByDate,
    data: orderTickets,
  };
};

const getFanpassReports = async (params) => {
  const {productId, paymentGateway, startDate, endDate, limit, lastKey} = params;

  const fanpassExe = UserFanpassModel.query({fanpassId: productId});

  if (paymentGateway) {
    fanpassExe.where('paymentGateway').in(paymentGateway.split(','));
  }
  if (startDate && endDate) {
    fanpassExe.where('paymentTime').between(+startDate, +endDate);
  }
  if (lastKey) {
    fanpassExe.startAt(base64ToJson(lastKey));
  }
  if (+limit === -1) {
    fanpassExe.all();
  } else {
    fanpassExe.limit(parseInt(limit) || 10);
  }

  const userFanpass = await fanpassExe.exec();

  const product = await FanpassModel.get({id: productId}, {attributes: ['fanpassName']});

  return {
    lastKey: !!userFanpass.count ? jsonToBase64(userFanpass.lastKey) : null,
    data: userFanpass.toJSON().map((fanpass) => {
      return {
        refId: fanpass.refId,
        productId: fanpass.PK,
        orderId: fanpass.orderId,
        invoiceNumber: 'N/A',
        paymentTime: fanpass.paymentTime,
        amount: fanpass.ticketPrice - (fanpass.ticketPrice * (fanpass.discountPercent || 0)) / 100,
        amountOriginal: fanpass.ticketPrice,
        discount: fanpass.discountPercent || 'N/A',
        fanpassName: product.fanpassName,
        userId: fanpass.userId,
        paymentInfo: fanpass.paymentInfo,
        paymentGateway: fanpass.paymentGateway,
        userFanpasStatus: 'Giao dịch thành công', // TODO: q&a lai anh Toan
      };
    }),
  };
};

const getReport = async (productType, params) => {
  if (!params.productId) {
    return {
      lastKey: null,
      data: [],
    };
  }
  if (ProductTypes.isEvent(productType)) {
    return await getTicketReports(params);
  }

  return await getFanpassReports(params);
};

const getRevenueGrowthByDay = async (assignedProducts) => {
  process.env.TZ = 'Asia/Ho_Chi_Minh';
  const now = moment();
  const yesterday = moment().subtract(1, 'day');

  const getOrders = (date) =>
    OrderModel.query('status')
      .eq(OrderStatus.SUCCESS)
      .where('PK')
      .in(assignedProducts)
      .where('paymentTime')
      .between(date.startOf('day').toDate().getTime(), date.endOf('day').toDate().getTime())
      .all()
      .exec();

  const nowOrders = await getOrders(now);
  const revenueByNow = nowOrders.toJSON().reduce((a, c) => a + c.amount, 0);

  const yesterdayOrders = await getOrders(yesterday);
  const revenueByYesterday = yesterdayOrders.toJSON().reduce((a, c) => a + c.amount, 0);

  return {
    growth: revenueByNow - revenueByYesterday,
    revenueByNow: revenueByNow,
  };
};

const getAnlytiscData = async (userId, params) => {
  const {productId, startDate, endDate} = params;
  const user = await UserModel.get({PK: userId});
  if (!user) {
    return [];
  }

  const assignedEvents = user.assignedEvents || [];
  const assignedFanpass = user.assignedFanpass || [];
  const assignedProducts = assignedEvents.concat(assignedFanpass);
  if (!assignedProducts.length) {
    return {
      revenue: 0,
      totalTransactions: 0,
      aov: 0,
      products: [],
    };
  }

  const orderExe = OrderModel.query('status').eq(OrderStatus.SUCCESS).using('statusIndex');

  let productIds = [];
  if (!user.accessAllEvents) {
    if (productId) {
      productIds = productId.split(',');
      productIds = productIds.filter((id) => assignedProducts.some((pId) => pId === id));
    } else {
      productIds = assignedProducts;
    }
    orderExe.where('PK').in(productIds);
  }

  if (startDate && endDate) {
    orderExe.where('paymentTime').between(+startDate, +endDate);
  }
  let orders = await orderExe.all().exec();
  orders = orders.toJSON();

  let products = [];
  if (productIds.length) {
    products = await Promise.all([
      EventModel.scan().where('PK').in(productIds).attributes(['PK', 'eventName']).exec(),
      FanpassModel.scan().where('id').in(productIds).attributes(['id', 'fanpassName']).exec(),
    ]).then(([res1, res2]) =>
      [
        ...res1.toJSON().map((item) => ({
          id: item.PK,
          productName: item.eventName,
          type: ProductTypes.P_EVENT,
        })),
        ...res2.toJSON().map((item) => ({
          id: item.id,
          productName: item.fanpassName,
          type: ProductTypes.P_FANPASS,
        })),
      ].flat(),
    );
  }
  const revenue = orders.reduce((a, c) => a + c.amount, 0);
  const growth = await getRevenueGrowthByDay(assignedProducts);

  return {
    revenue,
    totalTransactions: orders.length,
    aov: +(revenue / (orders.length || 1)).toFixed(2),
    ...growth,
    products: products.map((product) => ({
      ...product,
      orders: orders
        .filter((order) => order.productType === product.type)
        .map((order) => ({
          orderId: order.orderId,
          amount: order.amount,
          paymentTime: order.paymentTime,
        })),
    })),
  };
};

const getOrders = async (productType, params) => {
  const {productId, deliveryStatus, startDate, endDate} = params;
  if (!productId) {
    return [];
  }

  const event = await EventModel.get({PK: productId});
  if (!event) {
    throw new ResourceNotFoundError('Sự kiện không tồn tại');
  }

  const orderExe = OrderModel.query('PK')
    .eq(productId)
    .where('productType')
    .eq(productType)
    .where('status')
    .eq(OrderStatus.SUCCESS);

  if (startDate && endDate) {
    orderExe.where('paymentTime').between(+startDate, +endDate);
  }
  if (deliveryStatus) {
    if (DeliveryStatus.PENDING === deliveryStatus) {
      orderExe.where('deliveryStatus').not().exists();
    } else {
      orderExe.where('deliveryStatus').eq(deliveryStatus);
    }
  }

  let orders = await orderExe.all().exec();
  orders = orderBy(orders.toJSON(), ['paymentTime', 'desc']);

  return orders.map((order) => ({
    orderId: order.orderId,
    transId: order.transId,
    status: order.status,
    paymentInfo: order.paymentInfo,
    paymentTime: order.paymentTime,
    userId: order.userId,
    amount: order.amount,
    ticketCount: order.tickets.reduce((a, c) => a + c.quantity, 0),
    deliveryStatus: order.deliveryStatus || DeliveryStatus.PENDING,
    eventName: event.eventName,
    createdAt: order.createdAt,
  }));
};

const getTicketsByOrder = async (productId, orderId) => {
  const tickets = await EventTicketsModel.query('eventId')
    .eq(productId)
    .where('orderId')
    .eq(orderId)
    .exec();

  return tickets.toJSON().map((ticket) => ({
    refId: ticket.refId,
    ticketId: ticket.ticketId,
    ticketType: ticket.ticketType,
  }));
};

const deliveryTicket = async (orderId, data) => {
  process.env.TZ = 'Asia/Ho_Chi_Minh';
  const order = await OrderModel.get({
    PK: data.productId,
    orderId,
  });
  if (!order) {
    throw new ResourceNotFoundError('Đơn hàng không tồn tại');
  }
  order.deliveryStatus = DeliveryStatus.DELIVERED;
  await order.save();

  // update delivery info for ticket
  const promises = [];
  for (const info of data.ticketInfo) {
    const refId = info.refId;
    delete info.refId;
    promises.push(
      EventTicketsModel.update(
        {
          eventId: data.productId,
          refId,
        },
        {deliveryInfo: {...info}},
      ),
    );
  }
  await Promise.all(promises);

  const [product, tickets] = await Promise.all([
    EventModel.get({PK: data.productId}),
    EventTicketsModel.query('eventId').eq(data.productId).where('orderId').eq(order.orderId).exec(),
  ]);

  const userEmail = order.paymentInfo.email;
  const payload = {
    orderId: order.orderId,
    type: 'Vé điện tử',
    customerName: order.paymentInfo.name,
    customerPhoneNumber: order.paymentInfo.phoneNumber,
    eventNote: product.note,
    thumbnailUrl: product.thumbnail,
    paymentTime: moment(order.paymentTime).format('DD/MM/YYYY HH:mm:ss'),
    tickets: tickets.toJSON().map((t) => ({
      ...t,
      totalAmount: (t.totalAmount || t.ticketPrice)?.toLocaleString(),
      seatCode: t.deliveryInfo.seatCode,
      qrUrl: !!t.deliveryInfo.qrUrl
        ? t.deliveryInfo.qrUrl
        : `https://quickchart.io/chart?cht=qr&chld=H%7C1&chs=200x200&chl=${encodeURIComponent(
            !!t.deliveryInfo.qrText
              ? t.deliveryInfo.qrText
              : JSON.stringify({
                  refId: t.refId,
                  ticketId: t.ticketId,
                  orderId: order.orderId,
                  productId: order.PK,
                }),
          )}`,
    })),
    totalAmount: order.amount.toLocaleString(),
  };
  await sendEmail({
    to: userEmail,
    subject: '[Eventista] Eventista gửi bạn thông tin vé và chỗ ngồi',
    context: payload,
    template: 'event-ticket-ticket-info',
  });
};

module.exports.ReportService = {
  getReport,
  getAnlytiscData,
  getOrders,
  getTicketsByOrder,
  deliveryTicket,
};
