const {errorHandler} = require('../../../utils/error-handler.util');
const ResponseBuilder = require('../../../utils/response-builder.util');
const {CmsReportEndPoint} = require('./const');
const {ReportService} = require('./report.service');
const ValidatorUtil = require('../../../utils/request-validator.util');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      case CmsReportEndPoint.ANALYTICS:
        data = await getAnlytiscData(event);
        break;
      case CmsReportEndPoint.REPORT:
        data = await getReports(event);
        break;
      case CmsReportEndPoint.ORDERS:
        data = await getOrders(event);
        break;
      case CmsReportEndPoint.GET_TICKETS:
        data = await getTicketsByOrder(event);
        break;
      case CmsReportEndPoint.DELIVERY:
        data = await deliveryTicket(event);
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (error) {
    return errorHandler(error);
  }
};

async function getAnlytiscData(event) {
  const userId = event.requestContext.authorizer.lambda.principalId;
  const params = event.queryStringParameters || {};

  return await ReportService.getAnlytiscData(userId, params);
}

async function getReports(event) {
  const params = event.queryStringParameters || {};
  const productType = event.pathParameters.productType;

  return await ReportService.getReport(productType, params);
}

async function getOrders(event) {
  const params = event.queryStringParameters || {};
  const productType = event.pathParameters.productType;

  return await ReportService.getOrders(productType, params);
}

async function getTicketsByOrder(event) {
  const productId = event.pathParameters.productId;
  const orderId = event.pathParameters.orderId;

  return await ReportService.getTicketsByOrder(productId, orderId);
}

async function deliveryTicket(event) {
  const orderId = event.pathParameters.orderId;

  const orderFields = ['productId', 'ticketInfo'];
  await ValidatorUtil.requireParams(event.body, orderFields);

  return await ReportService.deliveryTicket(orderId, JSON.parse(event.body));
}
