const db = require('../../../../drizzle/db');
const schema = require('../../../../drizzle/schema');
const {eq, or, ilike, and, count, asc, sql, between} = require('drizzle-orm');

const getTicketsByFilter = async (queryParams) => {
  const whereClauses = [
    eq(schema.tickets.eventId, queryParams.eventId),
    eq(schema.tickets.calendarId, queryParams.calendarId),
  ];
  let limit = 100;
  let offset = 0;
  if ('ticketClassId' in queryParams) {
    whereClauses.push(eq(schema.tickets.ticketClassId, queryParams.ticketClassId));
  }
  if ('status' in queryParams) {
    whereClauses.push(eq(schema.tickets.status, queryParams.status));
  }
  if ('startDate' in queryParams && 'endDate' in queryParams) {
    whereClauses.push(between(schema.tickets.soldAt, queryParams.startDate, queryParams.endDate));
  }
  if ('limit' in queryParams) {
    limit = queryParams.limit;
  }
  if ('page' in queryParams && queryParams.page > 0) {
    offset = (queryParams.page - 1) * limit;
  }
  if ('search' in queryParams) {
    whereClauses.push(
      or(
        ilike(schema.tickets.customerEmail, `%${queryParams.search}%`),
        ilike(schema.tickets.customerPhone, `%${queryParams.search}%`),
        ilike(schema.tickets.orderId, `%${queryParams.search}%`),
      ),
    );
  }

  const getBaseQuery = (qb) => {
    return qb
      .from(schema.tickets)
      .leftJoin(schema.rows, eq(schema.tickets.rowId, schema.rows.id))
      .leftJoin(schema.zones, eq(schema.tickets.zoneId, schema.zones.id))
      .leftJoin(schema.ticketClass, eq(schema.tickets.ticketClassId, schema.ticketClass.id))
      .where(and(...whereClauses));
  };
  const queryFilterSelect = db.select({
    ...schema.tickets,
    rowName: schema.rows.name,
    ticketClassName: schema.ticketClass.name,
    zoneName: schema.zones.name,
  });

  const queryCountSelect = db.select({count: count(schema.tickets.id)});

  const [result, resultCount] = await Promise.all([
    getBaseQuery(queryFilterSelect)
      .orderBy(asc(schema.zones.position), asc(schema.rows.position), asc(schema.tickets.position))
      .limit(sql`${limit}`)
      .offset(sql`${offset}`),
    getBaseQuery(queryCountSelect),
  ]);

  return {items: result, total: resultCount[0].count};
};

const getTicketsByEventId = async (eventId) => {
  return db
    .select({...schema.tickets})
    .from(schema.tickets)
    .where(and(eq(schema.tickets.eventId, eventId), eq(schema.tickets.status, 'sold')));
};

module.exports.TicketDao = {
  getTicketsByFilter,
  getTicketsByEventId,
};
