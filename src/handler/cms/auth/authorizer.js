const jwt = require('jsonwebtoken'); // used to create, sign, and verify tokens
const UserRoles = require('../../../common/types/user-roles');

// Policy helper function
const generatePolicy = (context, effect, resource) => {
  const authResponse = {};
  authResponse.principalId = context?.email || null;
  if (effect && resource) {
    const policyDocument = {};
    policyDocument.Version = '2012-10-17';
    policyDocument.Statement = [];
    const statementOne = {};
    statementOne.Action = 'execute-api:Invoke';
    statementOne.Effect = effect;
    statementOne.Resource = resource;
    policyDocument.Statement[0] = statementOne;
    authResponse.policyDocument = policyDocument;
  }
  authResponse.context = {
    principalId: context?.email,
    userRole: context?.role,
  };

  return authResponse;
};

function verifyRequest(event, cb, checkPerm) {
  try {
    // check header or url parameters or post parameters for token
    const token = event.identitySource[0];

    if (!token) {
      return cb(null, generatePolicy(null, 'Deny', event.routeArn));
    }

    const match = token.match(/^Bearer (.*)$/);
    if (!match || match.length < 2) {
      return cb(null, generatePolicy(null, 'Deny', event.routeArn));
    }

    // verifies secret and checks exp
    jwt.verify(match[1], process.env.ADMIN_SECRET, (err, decoded) => {
      if (err || !checkPerm(decoded)) {
        return cb(null, generatePolicy(null, 'Deny', event.routeArn));
      }

      // if everything is good, save to request for use in other routes
      return cb(null, generatePolicy(decoded, 'Allow', event.routeArn));
    });
  } catch (err) {
    console.log('cms::authorizer - verifyRequest error: ', err.message);
    return cb(null, generatePolicy(null, 'Deny', event.routeArn));
  }
}

module.exports.admin = (event, _, callback) => {
  return verifyRequest(
    event,
    callback,
    (decoded) => UserRoles.isAdmin(decoded.role) || UserRoles.isSuperAdmin(decoded.role),
  );
};

module.exports.superAdmin = (event, _, callback) => {
  return verifyRequest(event, callback, (decoded) => UserRoles.isSuperAdmin(decoded.role));
};

module.exports.staff = (event, _, callback) => {
  return verifyRequest(
    event,
    callback,
    (decoded) => UserRoles.isStaff(decoded.role) || UserRoles.isSuperAdmin(decoded.role),
  );
};

module.exports.cmsAccess = (event, _, callback) => {
  return verifyRequest(event, callback, (decoded) => !UserRoles.isUser(decoded.role));
};

module.exports.report = (event, _, callback) => {
  return verifyRequest(
    event,
    callback,
    (decoded) => UserRoles.isReport(decoded.role) || UserRoles.isSuperAdmin(decoded.role),
  );
};
