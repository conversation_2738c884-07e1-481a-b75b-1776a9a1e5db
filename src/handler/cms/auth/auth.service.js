// const {authenticator} = require('otplib');
const {BadRequestError, PermissionDeniedError} = require('../../../common/exceptions');
const UserRoles = require('../../../common/types/user-roles');
const ErrorCode = require('../../../constants/error-code');
const UserUtil = require('../../client/auth/util');
const {UserModel} = require('../../../models/UserModel');

const InvalidCredentialsError = new BadRequestError({
  code: ErrorCode.INVALID_CREDENTIALS,
  message: 'Thông tin đăng nhập không chính xác',
});

const verifyUserLogin = async ({email, password, otp}) => {
  const user = await UserModel.get({
    PK: email,
    delFlg: false,
  });
  const isValidPassword = await UserUtil.comparePassword(password, user?.password);
  if (!user || !isValidPassword) {
    throw InvalidCredentialsError;
  }
  // const otpIsValid = authenticator.verify({token: otp, secret: user.otpSecret});
  // if (!otpIsValid) {
  //   throw InvalidCredentialsError;
  // }
  if (UserRoles.isUser(user.role)) {
    throw new PermissionDeniedError();
  }

  const token = UserUtil.signToken(email, user.role);

  return {
    token,
    user: {
      userId: user.PK,
      email: user.email,
      name: user.name,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    },
  };
};

module.exports.AuthService = {
  verifyUserLogin,
};
