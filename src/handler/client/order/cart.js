const {CartEndPoint} = require('./const');
const {errorHandler} = require('../../../utils/error-handler.util');
const ResponseBuilder = require('../../../utils/response-builder.util');
const ValidatorUtil = require('../../../utils/request-validator.util');
const {OrderService} = require('./services/order.service');
const {GhtkService} = require('./services/ghtk.service');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      case CartEndPoint.ADD_PRODUCT:
        data = await addProduct(event);
        break;
      case CartEndPoint.REMOVE_PRODUCT:
        data = await removeProduct(event);
        break;
      case CartEndPoint.GET_CART:
        data = await getCart(event);
        break;
      case CartEndPoint.CALCULATE_TRANSPORT_FEE:
        data = await calculateTransportFee(event);
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (error) {
    return errorHandler(error);
  }
};

async function addProduct(event) {
  const userEmail = event.requestContext.authorizer.lambda.principalId;
  const merchantId = event.pathParameters.merchantId;

  const orderFields = ['id', 'quantity', 'type'];
  await ValidatorUtil.requireParams(event.body, orderFields);

  const item = JSON.parse(event.body);
  await OrderService.addItemToCart(merchantId, userEmail, item);

  return {successMessage: 'Đã thêm sản phẩm vào giỏ hàng'};
}

async function removeProduct(event) {
  const userEmail = event.requestContext.authorizer.lambda.principalId;
  const merchantId = event.pathParameters.merchantId;
  const productId = event.pathParameters.productId;

  await OrderService.removeProductFromCart(merchantId, userEmail, productId);
}

async function getCart(event) {
  const userEmail = event.requestContext.authorizer.lambda.principalId;
  const merchantId = event.pathParameters.merchantId;

  return await OrderService.getCart(userEmail, merchantId);
}

async function calculateTransportFee(event) {
  const merchantId = event.pathParameters.merchantId;
  const orderFields = ['province', 'district', 'address', 'value'];
  await ValidatorUtil.requireParams(event.body, orderFields);

  const ghtkService = new GhtkService(merchantId);

  return await ghtkService.calculateTransportFee(JSON.parse(event.body));
}
