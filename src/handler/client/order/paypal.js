const {errorHandler} = require('../../../utils/error-handler.util');
const ResponseBuilder = require('../../../utils/response-builder.util');
const ValidatorUtil = require('../../../utils/request-validator.util');
const {sendMessageToQueue} = require('../../../common/sqs');
const {PaypalService, PaypalActions} = require('./services/paypal.service');
const PaymentGatewayTypes = require('../../../common/types/payment-gateway.type');

const {OrderService} = require('./services/order.service');
const {PaypalEndpoint} = require('./const');
const OrderStatus = require('../../../common/types/order-status');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      case PaypalEndpoint.PAYPAL_CREATE_ORDER:
        console.log('PAYPAL_CREATE_ORDER');
        data = await createOrder(event);
        break;
      case PaypalEndpoint.PAYPAL_GET_CLIENT_TOKEN:
        console.log('PAYPAL_GET_CLIENT_TOKEN');
        const paypalService = new PaypalService(PaypalActions.GENERATE_CLIENT_TOKEN_ACTION);
        data = await paypalService.process();
        break;
      case PaypalEndpoint.PAYPAL_COMPLETE_ORDER:
        console.log('PAYPAL_COMPLETE_ORDER');
        data = await completeOrder(event);
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (error) {
    return errorHandler(error);
  }
};

async function createOrder(event) {
  const requiredFields = ['productId', 'orderId', 'productType', 'paymentInfo'];
  await ValidatorUtil.requireParams(event.body, requiredFields);
  const body = JSON.parse(event.body);

  const order = await OrderService.updateOrderStatus({
    ...body,
    paymentGateway: PaymentGatewayTypes.PAYPAL,
  });
  const payload = {
    productId: body.productId,
    orderId: body.orderId,
    amount: order.amount,
  };
  const paypalService = new PaypalService(PaypalActions.CREATE_ORDER_ACTION, payload);
  const result = await paypalService.process();

  return {
    ...result,
    orderId: order.orderId,
    productId: body.productId,
  };
}

async function completeOrder(event) {
  const requiredFields = ['paypalOrderId', 'orderId', 'productId', 'productType'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const body = JSON.parse(event.body);
  const {orderId, productId, productType} = body;

  const paypalService = new PaypalService(PaypalActions.COMPLETE_ORDER_ACTION, body);
  const result = await paypalService.process();

  if (result.details?.length && result.details[0].issue) {
    console.log('Failed to payment!!!');
    await sendMessageToQueue(
      {
        productId,
        orderId,
        message: result.details[0].description,
        transId: '',
        paymentStatus: OrderStatus.FAILED,
      },
      orderId,
    );

    const extraData = {
      productId,
      productType,
    };

    return {
      orderId,
      extraData: btoa(JSON.stringify(extraData)),
    };
  }

  const [purchaseUnit] = result.purchase_units;
  const ref = JSON.parse(purchaseUnit.reference_id);
  const extraData = {
    productId: ref.productId,
    productType: ref.productType,
  };

  return {
    ...result,
    orderId: orderId,
    extraData: btoa(JSON.stringify(extraData)),
  };
}
