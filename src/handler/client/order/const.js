const OrderEndPoint = {
  CREATE_ORDER: 'POST /v1/order/create',
  ADD_TO_CART: 'POST /v1/order/add-to-cart',
  GET_CART: 'GET /v1/order/event/{eventId}/cart',
  CANCEL_ORDER: 'POST /v1/order/event/{eventId}/cancel',
  CART_CONFIRMATION: 'POST /v1/order/event/{eventId}/cart/confirmation',
  CHECK_ORDER: 'POST /v1/order/check',
  CART_REVIEW: 'POST /v1/order/review',
  GET_CART_REVIEW: 'GET /v1/order/review',
};

const CartEndPoint = {
  GET_CART: 'GET /v1/{merchantId}/cart',
  ADD_PRODUCT: 'POST /v1/{merchantId}/cart',
  REMOVE_PRODUCT: 'DELETE /v1/{merchantId}/cart/{productId}',
  CALCULATE_TRANSPORT_FEE: 'POST /v1/{merchantId}/shipment/fee',
};

const PaypalEndpoint = {
  PAYPAL_CREATE_ORDER: 'POST /v1/paypal/create-order',
  PAYPAL_GET_CLIENT_TOKEN: 'GET /v1/paypal/client-token',
  PAYPAL_COMPLETE_ORDER: 'POST /v1/paypal/complete-order',
};

const OrderEndPointV2 = {
  GET_REMAINING_TIME: 'GET /v2/order/event/{pathName}/remaining-time',
};

module.exports = {
  OrderEndPoint,
  CartEndPoint,
  PaypalEndpoint,
  OrderEndPointV2,
};
