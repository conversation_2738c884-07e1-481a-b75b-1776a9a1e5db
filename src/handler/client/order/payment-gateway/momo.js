const crypto = require('crypto');
const {BadRequestError} = require('../../../../common/exceptions');

class MomoSerive {
  #config;
  #orderInfo;
  #requestBody;

  constructor(config, orderInfo) {
    this.#config = config;
    this.#orderInfo = orderInfo;
    this.#requestBody = this.#buildRequestBody();
  }

  createPaymentUrl() {
    const config = this.#config;

    return new Promise((resolve, reject) => {
      // Create the HTTPS objects
      const https = require('https');
      const options = {
        hostname: config.hostname,
        port: 443,
        path: '/v2/gateway/api/create',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(this.#requestBody),
        },
      };

      // Send the request and get the response
      const req = https.request(options, (res) => {
        res.setEncoding('utf8');
        res.on('data', (body) => {
          const res = JSON.parse(body);
          if (res.resultCode === 0) {
            resolve(JSON.parse(body).payUrl);
            return;
          }
          reject(
            new BadRequestError({
              code: 600001,
              message: 'Tạo URL thanh toán không thành công.',
              params: {
                momoError: res,
              },
            }),
          );
        });
        res.on('end', () => {});
      });

      req.on('error', (e) => {
        reject(
          new BadRequestError({
            code: 600001,
            message: 'Tạo URL thanh toán không thành công.',
            params: {
              momoError: {
                message: e.message,
              },
            },
          }),
        );
      });
      // write data to request body
      req.write(this.#requestBody);
      req.end();
    });
  }

  #buildRequestBody() {
    const {ipnUrl, storeId, partnerCode, accessKey, secretKey, requestType} = this.#config;
    const data = this.#orderInfo;

    const requestId = Date.now();
    const extraData = JSON.stringify(data.extraData);
    const orderId = data.orderId;
    const orderInfo = data.orderInfo;

    const domain = data.extraData.domain || 'faniesta.com';
    const redirectUrl = `https://${domain}/ket-qua-thanh-toan`;
    // const redirectUrl = this.#config.redirectUrl;

    const rawSignature =
      'accessKey=' +
      accessKey +
      '&amount=' +
      data.amount +
      '&extraData=' +
      btoa(extraData) +
      '&ipnUrl=' +
      ipnUrl +
      '&orderId=' +
      orderId +
      '&orderInfo=' +
      orderInfo +
      '&partnerCode=' +
      partnerCode +
      '&redirectUrl=' +
      redirectUrl +
      '&requestId=' +
      requestId +
      '&requestType=' +
      requestType;

    // puts raw signature
    // signature
    const signature = crypto.createHmac('sha256', secretKey).update(rawSignature).digest('hex');

    // json object send to MoMo endpoint
    return JSON.stringify({
      partnerCode: partnerCode,
      accessKey: accessKey,
      requestId: requestId,
      amount: data.amount,
      orderId: orderId,
      orderInfo: orderInfo,
      redirectUrl: redirectUrl,
      ipnUrl: ipnUrl,
      extraData: btoa(extraData),
      requestType: requestType,
      signature: signature,
      lang: 'vi',
      storeId: storeId,
    });
  }
}

module.exports = MomoSerive;
