const Paymentwall = require('paymentwall');

class PaymentwallService {
  #config;
  #orderInfo;

  constructor(config, orderInfo) {
    this.#config = config;
    this.#orderInfo = orderInfo;
  }

  createPaymentUrl() {
    const {publicKey, privateKey, widgetCode} = this.#config;
    const data = this.#orderInfo;

    const executor = (res, rej) => {
      try {
        // eslint-disable-next-line new-cap
        Paymentwall.Configure(Paymentwall.Base.API_GOODS, publicKey, privateKey);

        // random uid
        const userId = data.orderId;
        const webDomain = `https://${data.extraData?.domain}`;
        const product = new Paymentwall.Product(
          data.orderId, // ag_external_id
          data.amount, // amount
          'USD', // currencycode
          `"$${data.amount}" for "Ticket" at "${webDomain}"`, // ag_name
        );
        const extraData = JSON.stringify(data.extraData);

        const widget = new Paymentwall.Widget(
          userId, // uid
          widgetCode, // widget
          [product],
          {
            sign_version: 3,
            success_url: `${webDomain}/ket-qua-thanh-toan?orderId=${data.orderId}&extraData=${btoa(
              extraData,
            )}`,
            'history[registration_date]': Math.floor(Date.now() / 1000),
            ps: data.paymentShortCode, // Replace it with specific payment system short code for single payment methods
            // order_info: data.orderInfo,
            extra_data: extraData,
            merchant_order_id: data.orderId,
            country_code: data.countryCode,
            platform: 'fan-engagement',
          },
        );
        const paymentUrl = widget.getUrl();

        res(paymentUrl);
      } catch (error) {
        rej(error);
      }
    };

    return new Promise(executor);
  }
}

module.exports = PaymentwallService;
