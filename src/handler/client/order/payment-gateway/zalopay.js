const CryptoJS = require('crypto-js');
const moment = require('moment');
const axios = require('axios');
const {BadRequestError} = require('../../../../common/exceptions');
const PaymentGatewayTypes = require('../../../../common/types/payment-gateway.type');

class ZaloPaySerive {
  #config;
  #orderInfo;
  #requestBody;

  constructor(config, orderInfo) {
    this.#config = config;
    this.#orderInfo = orderInfo;
    this.#requestBody = this.#buildRequestBody();
  }

  createPaymentUrl() {
    const endpoint = `https://${this.#config.hostname}/v2/create`;
    return new Promise((resolve, reject) => {
      axios
        .post(endpoint, null, {params: this.#requestBody})
        .then((res) => {
          if (res.data.return_code === 1) {
            resolve(res.data.order_url);
            return;
          }
          reject(
            new BadRequestError({
              code: 600001,
              message: 'Tạo URL thanh toán không thành công.',
              params: {
                zalopayError: {
                  resultCode: res.data.sub_return_code,
                  message: res.data.sub_return_message,
                },
              },
            }),
          );
        })
        .catch((err) => {
          reject(
            new BadRequestError({
              code: 600001,
              message: 'Tạo URL thanh toán không thành công.',
              params: {
                zalopayError: {
                  message: err.message,
                },
              },
            }),
          );
        });
    });
  }

  #buildRequestBody() {
    const {callbackUrl, appId, macKey} = this.#config;
    const {orderId, amount, extraData, tickets, paymentGateway} = this.#orderInfo;

    const domain = extraData.domain || 'faniesta.com';
    const redirectUrl = `https://${domain}/ket-qua-thanh-toan`;
    const transId = `${moment().format('YYMMDD')}_${orderId}`;
    const ticketNames = tickets.map((ticket) => ticket.name).join(', ');
    const description = `Eventista - Thanh toán vé sự kiện: ${ticketNames}, Mã giao dịch: ${orderId}`;
    let preferredPaymentMethod = '';
    switch (paymentGateway) {
      case PaymentGatewayTypes.ZALOPAY_VIETQR:
        preferredPaymentMethod = 'vietqr';
        break;
      case PaymentGatewayTypes.ZALOPAY_CC:
        preferredPaymentMethod = 'international_card';
        break;
      case PaymentGatewayTypes.ZALOPAY:
      default:
        preferredPaymentMethod = 'zalopay_wallet';
        break;
    }

    const config = {
      app_id: appId,
      app_user: 'Eventista',
      app_time: Date.now(),
      amount: amount,
      app_trans_id: transId,
      bank_code: '',
      embed_data: JSON.stringify({
        preferred_payment_method: [preferredPaymentMethod],
        redirecturl:
          redirectUrl + `?orderId=${orderId}&extraData=${btoa(JSON.stringify(extraData))}`,
      }),
      item: JSON.stringify([extraData]),
      callback_url: callbackUrl,
      description,
    };

    const rawSignature = [
      config.app_id,
      config.app_trans_id,
      config.app_user,
      config.amount,
      config.app_time,
      config.embed_data,
      config.item,
    ].join('|');
    // eslint-disable-next-line new-cap
    const mac = CryptoJS.HmacSHA256(rawSignature, macKey).toString();

    // json object send to ZaloPay endpoint
    return {
      ...config,
      mac,
    };
  }
}

module.exports = ZaloPaySerive;
