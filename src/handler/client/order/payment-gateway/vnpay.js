const moment = require('moment');
const querystring = require('qs');
const crypto = require('crypto');

const DATETIME_FORMAT = 'YYYYMMDDHHmmss';

class VNPayService {
  #config;
  #order;
  #requestParams;

  constructor(config, orderInfo) {
    this.#config = config;
    this.#order = orderInfo;
  }

  #sortObject(obj) {
    const sorted = {};
    const str = [];
    let key;
    for (key in obj) {
      if (obj.hasOwnProperty(key)) {
        str.push(encodeURIComponent(key));
      }
    }
    str.sort();
    for (key = 0; key < str.length; key++) {
      sorted[str[key]] = encodeURIComponent(obj[str[key]]).replace(/%20/g, '+');
    }

    return sorted;
  }

  #hashParams(vnpParams) {
    const signData = querystring.stringify(vnpParams, {encode: false});
    const hmac = crypto.createHmac('sha512', this.#config.secretKey);
    const signed = hmac.update(Buffer.from(signData, 'utf-8')).digest('hex');

    return signed;
  }

  async createPaymentUrl() {
    await this.#buildRequestParams();
    return `${this.#config.vnpUrl}?${querystring.stringify(this.#requestParams, {encode: false})}`;
  }

  #buildRequestParams() {
    process.env.TZ = 'Asia/Ho_Chi_Minh';

    const now = moment();
    const createDate = now.format(DATETIME_FORMAT);
    const expireDate = now.add(10, 'minute').format(DATETIME_FORMAT);
    const domain = this.#order.extraData?.domain || 'faniesta.com';
    const redirectUrl = `https://${domain}/ket-qua-thanh-toan`;

    return new Promise((resolve, reject) => {
      try {
        let vnpParams = {};

        vnpParams['vnp_Version'] = '2.1.0';
        vnpParams['vnp_Command'] = 'pay';
        vnpParams['vnp_TmnCode'] = this.#config.tmnCode;
        vnpParams['vnp_Locale'] = 'vn';
        vnpParams['vnp_CurrCode'] = 'VND';
        vnpParams['vnp_TxnRef'] = this.#order.orderId;
        vnpParams['vnp_OrderInfo'] = JSON.stringify(this.#order.extraData);
        vnpParams['vnp_OrderType'] = 'billpayment';
        vnpParams['vnp_Amount'] = this.#order.amount * 100;
        vnpParams['vnp_ReturnUrl'] = redirectUrl;
        vnpParams['vnp_IpAddr'] = this.#order.ipAddress;
        vnpParams['vnp_CreateDate'] = createDate;
        vnpParams['vnp_ExpireDate'] = expireDate;
        if (this.#config.bankCode) {
          vnpParams['vnp_BankCode'] = this.#config.bankCode; // 'VNPAYQR';
        }

        vnpParams = this.#sortObject(vnpParams);
        vnpParams['vnp_SecureHash'] = this.#hashParams(vnpParams);

        this.#requestParams = vnpParams;
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }
}

module.exports = VNPayService;
