const {getSecretValue} = require('../../../../common/secret-manager');
const PaymentGatewayTypes = require('../../../../common/types/payment-gateway.type');
const {PaymentMethodService} = require('../services/payment-method.service');

const SECRET_NAMES = {
  [PaymentGatewayTypes.MOMO]: process.env.MOMO_SERVICE,
  [PaymentGatewayTypes.ZALOPAY]: process.env.ZALOPAY_SERVICE,
  [PaymentGatewayTypes.ZALOPAY_VIETQR]: process.env.ZALOPAY_SERVICE,
  [PaymentGatewayTypes.ZALOPAY_CC]: process.env.ZALOPAY_SERVICE,
  [PaymentGatewayTypes.VNPAY]: process.env.VNPAY_SERVICE,
  [PaymentGatewayTypes.INTCARD]: process.env.VNPAY_SERVICE,
  [PaymentGatewayTypes.PAYMENTWALL]: process.env.PAYMENTWALL_SERVICE,
};

class PaymentGateway {
  #gatewayConfig;
  #redirectUrl;

  constructor(gateway, orderInfo) {
    if (!Object.values(PaymentGatewayTypes).includes(gateway)) {
      throw new Error('Payment gateway is not valid');
    }
    this.#redirectUrl = process.env.PAYMENT_RESULT_URL;
    if (!this.#redirectUrl) {
      throw new Error('PAYMENT_RESULT_URL is not present in environment');
    }
    this.gateway = gateway;
    this.orderInfo = orderInfo;
  }

  async #getConfig() {
    const [secretConfig, dbConfig] = await Promise.all([
      getSecretValue(SECRET_NAMES[this.gateway]),
      PaymentMethodService.getPaymentMethodByType(this.gateway),
    ]);
    this.#gatewayConfig = {
      ...secretConfig,
      ...dbConfig,
      redirectUrl: this.#redirectUrl,
    };
  }

  async buildPaymentUrl() {
    await this.#getConfig();
    let gateway = this.gateway;
    if (PaymentGatewayTypes.isVNPay(gateway)) {
      gateway = PaymentGatewayTypes.VNPAY;
    }
    if (PaymentGatewayTypes.isZalopay(gateway)) {
      gateway = PaymentGatewayTypes.ZALOPAY;
    }
    const GatewayService = require(`./${gateway}`);
    const gatewayService = new GatewayService(this.#gatewayConfig, this.orderInfo);
    const paymentUrl = await gatewayService.createPaymentUrl();

    return paymentUrl;
  }
}

module.exports = {PaymentGateway};
