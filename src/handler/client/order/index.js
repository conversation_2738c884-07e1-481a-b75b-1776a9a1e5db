const moment = require('moment');
const {errorHandler} = require('../../../utils/error-handler.util');
const ResponseBuilder = require('../../../utils/response-builder.util');
const ValidatorUtil = require('../../../utils/request-validator.util');
const {OrderEndPoint, OrderEndPointV2} = require('./const');
const {PaymentGateway} = require('./payment-gateway/payment-gateway');
const {OrderService} = require('./services/order.service');
const {OrderStatus} = require('../../payment/const');
const {BadRequestError} = require('../../../common/exceptions');
const {sendMessageToQueue, countMesssageInQueue} = require('../../../common/sqs');
const {ErrorCode} = require('../../../constants/error-code');
const {EventistaOrderProcess} = require('./processor/eventista-order-process');
const PaymentGatewayTypes = require('../../../common/types/payment-gateway.type');
const ProductTypes = require('../../../common/types/product-type');
const {TicketProcess} = require('./processor/ticket-process');
const {cancelTicketsOrder} = require('../../../../drizzle/models/ticket');
const {getUserByEmail} = require('../../../repository/user');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      case OrderEndPoint.ADD_TO_CART:
        data = await addToCart(event);
        break;
      case OrderEndPoint.CREATE_ORDER:
        data = await createPaymentUrl(event);
        break;
      case OrderEndPoint.GET_CART:
        data = await getCart(event);
        break;
      case OrderEndPoint.CANCEL_ORDER:
        data = await cancelOrder(event);
        break;
      case OrderEndPoint.CART_CONFIRMATION:
        data = await cartConfirmation(event);
        break;
      case OrderEndPoint.CHECK_ORDER:
        data = await checkOrder(event);
        break;
      case OrderEndPoint.CART_REVIEW:
        data = await reviewOrder(event);
        break;
      case OrderEndPoint.GET_CART_REVIEW:
        data = await getReviewOrder(event);
        break;
      case OrderEndPointV2.GET_REMAINING_TIME:
        data = await getRemainingTime();
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (error) {
    return errorHandler(error);
  }
};

async function createPaymentUrl(event) {
  const body = JSON.parse(event.body);
  const orderFields = ['productId', 'orderId', 'paymentGateway', 'paymentInfo'];
  await ValidatorUtil.requireParams(event.body, orderFields);

  const order = await OrderService.updateOrderStatus(body);

  if (
    body.paymentGateway !== PaymentGatewayTypes.FREE &&
    body.paymentGateway !== PaymentGatewayTypes.COD
  ) {
    const orderInfo = {
      orderId: order.orderId,
      orderInfo: 'Thanh toan ve su kien',
      tickets: order.tickets,
      amount: order.amount,
      paymentGateway: order.paymentGateway,
      extraData: order.extraData,
      countryCode: body.countryCode,
      paymentShortCode: body.paymentShortCode,
    };
    const paymentService = new PaymentGateway(order.paymentGateway, orderInfo);
    const paymentUrl = await paymentService.buildPaymentUrl();

    return {paymentUrl};
  }

  // kiem tra cong thanh toan, so tien cua hoa don
  // neu la free thi amount phai = 0
  if (order.amount > 0 && body.paymentGateway === PaymentGatewayTypes.FREE) {
    throw new BadRequestError({
      code: ErrorCode.ORDER_INVALID_GATEWAY,
      message: 'Cổng thanh toán không phù hợp với số tiền',
    });
  }

  await sendMessageToQueue(
    {
      productId: order.PK,
      orderId: order.orderId,
      message: order.message,
      transId: `trecenter-${order.orderId}`,
      paymentStatus: OrderStatus.SUCCESS,
    },
    order.PK,
  );

  return {
    orderId: order.orderId,
    extraData: JSON.stringify(order.extraData),
  };
}

async function checkOrder(event) {
  const requiredFields = ['extraData', 'orderId'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const body = JSON.parse(event.body);
  let extraData = {};
  try {
    extraData = JSON.parse(body.extraData);
  } catch (_) {
    extraData = JSON.parse(atob(body.extraData));
  }

  const {productId, productType} = extraData;
  const orderInfo = await OrderService.getOrderInfo(productId, body.orderId);
  const productInfo = await OrderService.getProductInfo(productId, productType);

  if (!OrderStatus.isProcessed(orderInfo.status)) {
    throw new BadRequestError({
      code: ErrorCode.ORDER_PROCESSING,
      message: `Đơn hàng chưa được xử lý\nTrường hợp bạn đã bị trừ tiền, bạn sẽ được hoàn tiền trong vòng 1-3 ngày làm việc.`,
      item: productInfo,
    });
  }

  if (OrderStatus.isFailed(orderInfo.status)) {
    throw new BadRequestError({
      code: ErrorCode.ORDER_FAILED,
      message: `${orderInfo.message}\nTrường hợp bạn đã bị trừ tiền, bạn sẽ được hoàn tiền trong vòng 1-3 ngày làm việc.`,
      item: productInfo,
    });
  }

  return {
    successMessage: 'Đơn hàng đã thanh toán',
    item: productInfo,
    order: orderInfo,
  };
}

/**
 * Đơn hàng chia làm 2 loại:
 * 1 - Dành cho các sản phẩm của Eventista (vé + fanpass)
 *   product = ticket
 * 2 - Dành cho các sản phẩm thuộc Merchant (sản phẩm thuộc 1 nhãn hàng cụ thể)
 *   product = merchant product
 *
 * @param {*} event lambda payload
 * @return {Object} thông tin đơn hàng
 */
async function reviewOrder(event) {
  const userEmail = event.requestContext.authorizer?.lambda?.principalId;
  const orderFields = ['productId', 'tickets', 'productType'];
  await ValidatorUtil.requireParams(event.body, orderFields);

  // check thong tin ticket
  const body = JSON.parse(event.body);
  const ticketFields = ['ticketId', 'quantity'];
  if (ProductTypes.isEvent(body.productType)) {
    ticketFields.push('calendarId');
  }
  for (const ticket of body.tickets) {
    await ValidatorUtil.validateRequireFields(ticket, ticketFields);
  }

  const order = new EventistaOrderProcess({...body, userEmail});
  await order.process();

  return {
    productId: body.productId,
    orderId: order.orderId,
  };
}

async function getReviewOrder(event) {
  const userEmail = event.requestContext.authorizer.lambda.principalId;
  const order = await OrderService.getOrderInReview(userEmail);
  const product = await OrderService.getProductInfo(order.PK, order.productType);

  return {
    orderId: order.orderId,
    event: {
      ...product,
      tickets: undefined,
      type: order.productType,
    },
    originalAmount: order.originalAmount,
    amount: order.amount,
    tickets: order.tickets,
    expiredIn: moment(order.updatedAt).add(170, 'seconds').toDate().getTime(),
  };
}

async function addToCart(event) {
  const userEmail = event.requestContext.authorizer.lambda.principalId;
  const user = await getUserByEmail(userEmail);
  if (!user.verified) {
    throw new BadRequestError({
      code: ErrorCode.EMAIL_NOT_CONFIRMED,
      message: 'Tài khoản chưa được xác thực',
    });
  }
  // check thong tin ticket
  const body = JSON.parse(event.body);
  const orderTicketClassFields = ['productId', 'ticket.id', 'ticket.quantity', 'ticket.calendarId'];
  const orderTicketsFields = ['productId', 'seatmapTickets'];

  const [isValidTicketClass, isValidTickets] = await Promise.allSettled([
    ValidatorUtil.requireParams(event.body, orderTicketClassFields),
    ValidatorUtil.requireParams(event.body, orderTicketsFields),
  ]);

  if (isValidTicketClass.status === 'rejected' && isValidTickets.status === 'rejected') {
    throw body.eventCollaborator === 'seatmap' ? isValidTickets.reason : isValidTicketClass.reason;
  }

  const order = new TicketProcess({...body, userEmail});
  await order.process();

  return {
    productId: body.productId,
    orderId: order.orderId,
  };
}

async function getCart(event) {
  const userEmail = event.requestContext.authorizer?.lambda?.principalId;
  const eventId = event.pathParameters.eventId;

  const cart = await OrderService.getEventCart(userEmail, eventId);

  return cart;
}

async function cancelOrder(params) {
  const eventId = params.pathParameters.eventId;
  const event = await OrderService.getEventDetail(eventId);
  const {orderId} = JSON.parse(params.body);

  await OrderService.cancelOrder({
    cancelReason: 'Khách hàng bấm hủy order',
    eventId: event.PK,
    orderId,
  });
  await cancelTicketsOrder({eventId: event.PK, orderId});
}

async function cartConfirmation(params) {
  const orderFields = ['orderId', 'cartConfirmed'];
  await ValidatorUtil.requireParams(params.body, orderFields);
  const eventId = params.pathParameters.eventId;
  const event = await OrderService.getEventDetail(eventId);
  const body = JSON.parse(params.body);
  await OrderService.cartConfirm({...body, eventId: event.PK});
}

async function getRemainingTime() {
  const count = await countMesssageInQueue();
  return count + 3;
}
