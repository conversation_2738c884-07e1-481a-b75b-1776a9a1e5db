const {getSecretValue} = require('../../../../common/secret-manager');

const PaypalActions = {
  CREATE_ORDER_ACTION: 'CREATE_ORDER',
  COMPLETE_ORDER_ACTION: 'COMPLETE_ORDER_ACTION',
  GENERATE_CLIENT_TOKEN_ACTION: 'GENERATE_CLIENT_TOKEN',
  GET_ORDER_DETAILS: 'GET_ORDER_DETAILS',
  VALIDATE_IPN: 'VALIDATE_IPN',
};
const INTENT = 'CAPTURE';

class PaypalService {
  // config vars
  #clientId;
  #secretKey;
  #endpointUrl;
  #currencyCode;
  // common vars
  #action;
  #accessToken;
  // CREATE_ORDER vars
  data;

  constructor(action, data) {
    if (!action) {
      throw new Error('Action is required in Paypal service');
    }
    if (!action === PaypalActions.CREATE_ORDER_ACTION && !order) {
      throw new Error('The create order action is require machine order info');
    }
    this.data = data;
    this.#action = action;
  }

  async #loadConfig() {
    console.time('PaypalService::loadConfig');
    const config = await getSecretValue(process.env.PAYPAL_SERVICE);
    this.#clientId = config.clientId;
    this.#secretKey = config.secretKey;
    this.#endpointUrl = config.endpointUrl;
    this.#currencyCode = config.currencyCode;
    console.timeEnd('PaypalService::loadConfig');
  }

  #genAccessToken() {
    console.time('PaypalService::genAccessToken');
    const auth = `${this.#clientId}:${this.#secretKey}`;
    const payload = 'grant_type=client_credentials';

    return new Promise((resolve, reject) => {
      fetch(`${this.#endpointUrl}/v1/oauth2/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: `Basic ${Buffer.from(auth).toString('base64')}`,
        },
        body: payload,
      })
        .then((res) => res.json())
        .then((json) => {
          this.#accessToken = json.access_token;
          console.timeEnd('PaypalService::genAccessToken');
          resolve();
        })
        .catch((error) => reject(error));
    });
  }

  // https://developer.paypal.com/docs/api/orders/v2/#orders_create
  async #createOrder() {
    const {orderId, productId, amount, productType} = this.data;
    const orderData = {
      intent: INTENT,
      purchase_units: [
        {
          reference_id: JSON.stringify({
            orderId,
            productId,
            productType,
          }),
          custom_id: `${productType}|${orderId}|${productId}`,
          amount: {
            currency_code: this.#currencyCode,
            value: amount,
            breakdown: {
              item_total: {
                currency_code: this.#currencyCode,
                value: amount,
              },
            },
          },
          items: [
            {
              name: this.data.productId,
              quantity: 1,
              sku: this.data.productId,
              category: 'DIGITAL_GOODS',
              unit_amount: {
                currency_code: this.#currencyCode,
                value: amount,
              },
            },
          ],
        },
      ],
    };

    return new Promise((resolve, reject) => {
      fetch(`${this.#endpointUrl}/v2/checkout/orders`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.#accessToken}`,
        },
        body: JSON.stringify(orderData),
      })
        .then((res) => res.json())
        .then((json) => {
          resolve(json);
        })
        .catch((error) => reject(error));
    });
  }

  #completeOrder() {
    console.info('PaypalService::completeOrder - resource ID:', this.data.paypalOrderId);
    console.time('PaypalService::completeOrder');
    return new Promise((resolve, reject) => {
      fetch(
        `${this.#endpointUrl}/v2/checkout/orders/${
          this.data.paypalOrderId
        }/${INTENT.toLowerCase()}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${this.#accessToken}`,
          },
        },
      )
        .then((res) => res.json())
        .then((json) => {
          console.info('PaypalService::completeOrder - response:', json);
          console.timeEnd('PaypalService::completeOrder');
          resolve(json);
        })
        .catch((error) => {
          console.error('PaypalService::completeOrder - error:', json);
          reject(error);
        });
    });
  }

  #generateClientToken() {
    return new Promise((resolve, reject) => {
      fetch(`${this.#endpointUrl}/v1/identity/generate-token`, {
        method: 'post',
        headers: {
          Authorization: `Bearer ${this.#accessToken}`,
          'Content-Type': 'application/json',
        },
        // body: null,
      })
        .then((response) => response.json())
        .then((data) => {
          console.debug('PaypalService::generateClientToken - response: ', data);
          resolve({clientToken: data.client_token});
        })
        .catch((error) => reject(error));
    });
  }

  async process() {
    console.debug('PaypalService::proccess - action: ', this.#action);
    // load config
    await this.#loadConfig();
    // get paypal access token
    await this.#genAccessToken();

    let data;
    switch (this.#action) {
      case PaypalActions.CREATE_ORDER_ACTION:
        data = await this.#createOrder();
        break;
      case PaypalActions.COMPLETE_ORDER_ACTION:
        data = await this.#completeOrder();
        break;
      case PaypalActions.GENERATE_CLIENT_TOKEN_ACTION:
        data = await this.#generateClientToken();
        break;
    }

    return data;
  }
}

module.exports = {
  PaypalService,
  PaypalActions,
};
