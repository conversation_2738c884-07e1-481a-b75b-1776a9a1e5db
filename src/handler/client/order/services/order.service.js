const moment = require('moment');
const {orderBy} = require('lodash');
const {ResourceNotFoundError, BadRequestError} = require('../../../../common/exceptions');
const ErrorCode = require('../../../../constants/error-code');
const {EventModel} = require('../../../../models/EventModel');
const {FanpassModel} = require('../../../../models/FanpassModel');
const {OrderModel} = require('../../../../models/OrderModel');
const {TicketStorageModel} = require('../../../../models/TicketStorageModel');
const {getOrderTickets} = require('../../user/user.service');
const ProductTypes = require('../../../../common/types/product-type');
const OrderStatus = require('../../../../common/types/order-status');
const {CartModel} = require('../../../../models/CartModel');
const {v4} = require('../../../../utils/uuid.util');
const {MerchantProductModel} = require('../../../../models/MerchantProductModel');
const {MerchantModel} = require('../../../../models/MerchantModel');
const {PromotionModel} = require('../../../../models/PromotionModel');
const {OrderUtil} = require('../../../../utils/order.util');
const PaymentGatewayTypes = require('../../../../common/types/payment-gateway.type');
const {resolveHoldTicketByOrder} = require('../../../../../drizzle/models/ticket');

const getEventInfo = async (eventId) => {
  const eventInfo = await EventModel.get({PK: eventId});
  if (!eventInfo) {
    throw new ResourceNotFoundError('Sự kiện không tồn tại');
  }
  const storage = await TicketStorageModel.query('PK')
    .eq(eventId)
    .exec()
    .then((res) => res.toJSON())
    .then((res) => res.filter((item) => !!item.show));

  const tickets = storage.map((item) => {
    const [ticketId, calendarId] = item.ticketId.split('#');
    delete item.ticketId;
    const temp = eventInfo.tickets.find((s) => s.ticketId === ticketId);

    return {
      ...temp,
      ...item,
      calendarId,
    };
  });
  eventInfo.tickets = tickets;

  return eventInfo;
};

const createOrder = async (data) => {
  return await OrderModel.create(data);
};

const getOrderInfo = async (productId, orderId) => {
  const order = await OrderModel.get({
    PK: productId,
    orderId,
  });
  if (!order) {
    throw new ResourceNotFoundError('Đơn hàng không tồn tại');
  }

  return order;
};

const getProductInfo = async (productId, productType) => {
  if (ProductTypes.P_EVENT === productType) {
    return await EventModel.get({PK: productId});
  } else {
    return await FanpassModel.get({id: productId});
  }
};

const getFanpassInfo = async (productId) => {
  const fanpassInfo = await FanpassModel.get({id: productId});
  if (!fanpassInfo) {
    throw new ResourceNotFoundError('Fanpass không tồn tại');
  }

  return fanpassInfo;
};

const reviewOrder = async (data, eventInfo) => {
  const getTicketStorage = async () =>
    await TicketStorageModel.query({PK: data.productId})
      .exec()
      .then((res) => res.toJSON());

  let [ticketPurchased, ticketStorage] = await Promise.all([
    getOrderTickets(data.userId),
    getTicketStorage(),
  ]);

  ticketPurchased = ticketPurchased.find((item) => item.PK === data.productId);
  const totalTicketPurchased = ticketPurchased?.tickets?.length || 0;

  // kiem tra so luong ve user da mua trong su kien
  const maxEventTicket = eventInfo.maxTicketPerUser;
  const isLimitQuantityPerEvent = maxEventTicket !== -1;
  const totalOrderTickets = data.tickets.reduce((a, c) => a + c.quantity, 0);
  const totalTickets = totalOrderTickets + totalTicketPurchased;
  if (isLimitQuantityPerEvent && totalTickets > maxEventTicket) {
    throw new BadRequestError({
      code: ErrorCode.ORDER_MAX_TICKET_PER_EVENT,
      message: 'Bạn đã mua đủ số lượng vé cho phép trong sự kiện',
    });
  }

  if (!ticketStorage?.length) {
    throw new BadRequestError({
      code: ErrorCode.TICKET_SOLD_OUT,
      message: 'Vé đã bán hết',
    });
  }

  for (const ticket of data.tickets) {
    const purchased = ticketPurchased?.tickets?.filter((item) => item.ticketId === ticket.ticketId);
    const storage = ticketStorage.find(
      (item) => item.ticketId === `${ticket.ticketId}#${ticket.calendarId}`,
    );
    if (storage.stopSelling) {
      throw new BadRequestError({
        code: ErrorCode.ORDER_TICKET_STOP_SELLING,
        message: 'Vé bạn chọn đã dừng bán',
      });
    }
    // kiem tra so luong ticket da mua
    const isLimitQuantity = storage.maxTicketPerUser !== -1;
    if (isLimitQuantity && purchased?.length >= storage.maxTicketPerUser) {
      throw new BadRequestError({
        code: ErrorCode.ORDER_REVIEW_MAX_TICKET,
        message: 'Bạn đã mua đủ số lượng vé cho phép',
      });
    }
    // kiem tra so luong ticket da mua
    const quantity = ticket.quantity + (purchased?.length || 0);
    if (isLimitQuantity && quantity > storage.maxTicketPerUser) {
      throw new BadRequestError({
        code: ErrorCode.ORDER_REVIEW_MAX_TICKET,
        message: 'Bạn đã mua quá số lượng vé cho phép',
      });
    }
    // check so luong ve user mua voi so luong ve con lai
    if (ticket.quantity > storage.quantity) {
      throw new BadRequestError({
        code: ErrorCode.TICKET_QUANTITY_INVALID,
        message: `Không đủ số lượng [${ticket.ticketType}]\nSố lượng còn lại: ${storage.quantity}`,
      });
    }
  }
};

const updateOrderStatus = async (data) => {
  const order = await OrderModel.get({
    PK: data.productId,
    orderId: data.orderId,
  });
  if (!order) {
    throw new ResourceNotFoundError('Đơn hàng không tồn tại. Hãy chọn lại vé.');
  }
  if (OrderStatus.isProcessed(order.status)) {
    throw new BadRequestError({
      code: ErrorCode.ORDER_PROCESSED,
      message: 'Đơn hàng của bạn đã được xử lý',
    });
  } else if (OrderStatus.isExpired(order.status)) {
    throw new BadRequestError({
      code: ErrorCode.ORDER_TIMEOUT,
      message: 'Đơn hàng của bạn đã hết hạn',
    });
  }

  // nếu cổng thanh toán là paymentwall / paypal -> update lại order amount theo giá trị USD
  if (PaymentGatewayTypes.isGlobal(data.paymentGateway)) {
    order.originalAmount = order.amountInUSD;
    order.amount = order.amountInUSD;
    order.currencyCode = 'USD';
    delete order.amountInUSD;
  }

  order.status = OrderStatus.OPEN;
  order.paymentGateway = data.paymentGateway;
  order.paymentInfo = data.paymentInfo;

  if (!data.promotionCode && order.promotionUsed?.type === 'auto') {
    data.promotionCode = order.promotionUsed.code;
    const isNotApply =
      order.currencyCode === 'USD'
        ? order.amount < order.promotionUsed.minOrderAmountInUSD
        : order.amount < order.promotionUsed.minOrderAmount;
    if (isNotApply) {
      data.promotionCode = null;
    }
  }

  if (!!data.promotionCode) {
    const promotion = await getPromotion(data.productId, data.promotionCode);
    const {code, discount, appliesTo, tickets} = promotion;

    const {amount, amountInUSD} = OrderUtil.calcAllTicketPriceWithPromotion(
      order.tickets,
      promotion,
    );

    order.amount = PaymentGatewayTypes.isGlobal(data.paymentGateway) ? amountInUSD : amount;

    order.promotionUsed = {
      code: code.toUpperCase(),
      discountPercent: discount.percent,
      discountPercentInUSD: discount.percentInUSD,
      maxValue: discount.maxValue,
      maxValueInUSD: discount.maxValueInUSD,
      appliesTo,
      tickets,
    };
  }

  await order.save();

  return order;
};

const getPromotion = async (eventId, code) => {
  const promotion = await PromotionModel.get({
    eventId,
    code: code.toUpperCase(),
  });
  if (!promotion || !promotion.active) {
    throw new BadRequestError({
      code: 201100,
      message: 'Rất tiếc, mã khuyến mại không hợp lệ',
    });
  }

  return promotion;
};

const getOrderInReview = async (userId) => {
  let existedOrders = await OrderModel.query('userId')
    .eq(userId)
    .using('userIdIndex')
    .where('status')
    .eq('REVIEW')
    .exec();
  if (!existedOrders.count) {
    throw new ResourceNotFoundError('Bạn không có đơn hàng nào');
  }
  existedOrders = orderBy(existedOrders.toJSON(), ['createdAt'], ['desc']);

  return existedOrders.shift();
};

const addItemToCart = async (merchantId, email, item) => {
  const key = {userId: email, merchantId};
  const existCart = await CartModel.get(key);

  const quantityError = new BadRequestError({
    code: 700077,
    message: 'Số lượng của sản phẩm không đúng',
  });

  if (!existCart) {
    if (item.quantity < 1) {
      throw quantityError;
    }
    await CartModel.create({
      ...key,
      items: [item],
      orderId: v4(),
    });
    return;
  }

  const existItem = existCart.items.find((e) => e.id === item.id);
  if (existItem) {
    let quantity = existItem.quantity;
    let isEmpty = false;
    quantity += item.quantity;
    if (quantity <= 0) {
      const items = existCart.items.filter((e) => e.id !== item.id);
      isEmpty = items.length === 0;
      existCart.items = items;
    }
    if (isEmpty) {
      await existCart.delete();
    } else {
      existItem.quantity = quantity;
      await existCart.save();
    }
  } else {
    if (item.quantity < 1) {
      throw quantityError;
    }
    await CartModel.update(key, {$ADD: {items: [item]}});
  }
};

const removeProductFromCart = async (merchantId, email, productId) => {
  const key = {userId: email, merchantId};
  const existCart = await CartModel.get(key);
  if (!existCart) {
    return;
  }

  existCart.items = existCart.items.filter((e) => e.id !== productId);
  if (!existCart.items.length) {
    await existCart.delete();
  } else {
    await existCart.save();
  }
};

const getCart = async (email, merchantId) => {
  const key = {userId: email, merchantId};
  const existCart = await CartModel.get(key);
  if (!existCart) {
    return;
  }
  const itemIds = existCart.items.map((e) => e.id);
  const products = await MerchantProductModel.scan('id')
    .in(itemIds)
    .attributes([
      'id',
      'name',
      'brandName',
      'note',
      'type',
      'price',
      'thumbnail',
      'tags',
      'promotion',
    ])
    .exec();

  return {
    ...existCart.toJSON(),
    items: existCart.items.map((e) => {
      const product = products.toJSON().find((p) => p.id === e.id);
      return {
        ...e,
        ...product,
      };
    }),
  };
};

const getMerchantInfo = async (merchantId) => {
  const result = await MerchantModel.get({id: merchantId});
  if (!result) {
    throw new ResourceNotFoundError('Không tìm thấy thông tin Merchant');
  }

  return result.toJSON();
};

const getEventDetail = async (eventId) => {
  const eventInfo = await EventModel.get({
    PK: eventId,
  });

  if (!eventInfo) {
    throw new ResourceNotFoundError('Sự kiện không tồn tại');
  }

  return eventInfo;
};

const getEventCart = async (userId, eventId) => {
  const product = await getEventDetail(eventId);
  const existedOrders = await OrderModel.query('PK')
    .eq(product.PK)
    .where('status')
    .not()
    .eq(OrderStatus.FAILED)
    .where('userId')
    .eq(userId)
    .using('userIdIndex')
    .exec();
  if (!existedOrders.count) {
    throw new ResourceNotFoundError('Bạn không có đơn hàng nào');
  }
  const cart = orderBy(existedOrders.toJSON(), ['createdAt'], ['desc']).shift();
  if (cart.status === OrderStatus.PENDING) {
    throw new BadRequestError({
      code: ErrorCode.CART_PENDING,
      message: 'Đơn hàng của bạn đang được xử lý',
      orderId: cart.orderId,
      event: {
        ...product,
        tickets: undefined,
        type: cart.productType,
      },
    });
  }
  if (cart.status === OrderStatus.CANCELED) {
    await OrderModel.update({PK: cart.PK, orderId: cart.orderId}, {status: OrderStatus.FAILED});
    throw new BadRequestError({
      code: ErrorCode.ORDER_FAILED,
      message: cart.cancelReason || 'Đơn hàng đã hết hạn',
      orderId: cart.orderId,
      event: {
        ...product,
        tickets: undefined,
        type: cart.productType,
      },
    });
  }
  if (cart.status === OrderStatus.CONFIRM) {
    throw new BadRequestError({
      code: ErrorCode.CART_CONFIRMATION,
      message: 'Xác nhận mua vé không liên tiếp.',
      orderId: cart.orderId,
      event: {
        ...product,
        tickets: undefined,
        type: cart.productType,
      },
    });
  }
  if (cart.status !== OrderStatus.REVIEW) {
    throw new ResourceNotFoundError('Bạn không có đơn hàng nào');
  }

  let amountAfterDiscount;
  let amountInUSDAfterDiscount;

  const promotion = await getAutoPromotion(cart);

  if (promotion) {
    const {amount, amountInUSD} = OrderUtil.calcAllTicketPriceWithPromotion(
      cart.tickets,
      promotion,
    );

    if (cart.amount >= promotion.minOrderAmount) {
      amountAfterDiscount = amount;
    }
    if (cart.amountInUSD >= promotion.minOrderAmountInUSD) {
      amountInUSDAfterDiscount = amountInUSD;
    }
    // save promotion used to cart
    await OrderModel.update(
      {PK: cart.PK, orderId: cart.orderId},
      {
        promotionUsed: {
          code: promotion.code.toUpperCase(),
          type: promotion.type,
          discountPercent: promotion.discount.percent,
          discountPercentInUSD: promotion.discount.percentInUSD,
          maxValue: promotion.discount.maxValue,
          maxValueInUSD: promotion.discount.maxValueInUSD,
          appliesTo: promotion.appliesTo,
          tickets: promotion.tickets,
          minOrderAmount: promotion.minOrderAmount,
          minOrderAmountInUSD: promotion.minOrderAmountInUSD,
        },
      },
    );
  }

  return {
    orderId: cart.orderId,
    event: {
      ...product,
      tickets: undefined,
      type: cart.productType,
    },
    tickets: cart.tickets,
    expiredIn: moment(cart.updatedAt).add(600, 'seconds').toDate().getTime(),
    amountAfterDiscount,
    amountInUSDAfterDiscount,
    promotionCode: promotion?.code,
  };
};

const cancelOrder = async ({eventId, orderId, cancelReason}) => {
  const order = await OrderModel.get({
    PK: eventId,
    orderId,
  });
  if (!order) {
    throw new ResourceNotFoundError('Đơn hàng không tìm thấy');
  }
  if (OrderStatus.isProcessed(order.status)) {
    throw new BadRequestError({
      code: ErrorCode.ORDER_PROCESSED,
      message: 'Đơn hàng đã được thanh toán',
    });
  }

  order.status = OrderStatus.CANCELED;
  order.cancelReason = cancelReason;
  await order.save();
};

const cartConfirm = async ({eventId, orderId, cartConfirmed}) => {
  const [order] = await OrderModel.query('PK').eq(eventId).where('orderId').eq(orderId).exec();
  if (!order) {
    throw new ResourceNotFoundError('Đơn hàng không tìm thấy');
  }
  if (order.status !== OrderStatus.CONFIRM) {
    throw new BadRequestError({
      code: ErrorCode.ORDER_PROCESSED,
      message: 'Đơn hàng đã được xử lý',
    });
  }

  const errMessage = 'Đã quá thời gian xác nhận.';
  if (moment().diff(moment(order.toJSON().confirmAt), 'seconds') >= 20) {
    throw new BadRequestError({
      code: ErrorCode.CART_CONFIRMATION_EXPIRED,
      message: errMessage,
    });
  }
  if (!cartConfirmed) {
    order.status = OrderStatus.CANCELED;
    order.cancelReason = errMessage;
    await order.save();
    await resolveHoldTicketByOrder(orderId);
    return;
  }
  order.status = OrderStatus.REVIEW;
  await order.save();
};

const validateTicketQuantity = async (data, eventInfo) => {
  const orders = await OrderModel.query('PK')
    .eq(data.productId)
    .where('userId')
    .eq(data.userEmail)
    .where('status')
    .eq(OrderStatus.SUCCESS)
    .exec()
    .then((res) => res.toJSON())
    .then((orders) => orders.map((order) => order.tickets).flat());

  if (!orders.length) {
    return;
  }

  const totalTicketPurchased = orders.reduce((a, c) => a + c.quantity, 0);

  // kiem tra so luong ve user da mua trong su kien
  const maxEventTicket = eventInfo.maxTicketPerUser;
  const isLimitQuantityPerEvent = maxEventTicket !== -1;
  if (isLimitQuantityPerEvent && totalTicketPurchased >= maxEventTicket) {
    throw new BadRequestError({
      code: ErrorCode.ORDER_MAX_TICKET_PER_EVENT,
      message: 'Bạn đã mua đủ số lượng vé cho phép trong sự kiện',
    });
  }

  // kiem tra so luong ve mua theo calendar
  const calendar = eventInfo.eventCalendar?.find(
    (item) => item.calendarId === data.ticket.calendarId,
  );
  const maxTicketByCalendar = calendar?.maxTicketPerUser || -1;
  const totalTicketPurchasedByCalendar = orders
    .filter((ticket) => ticket.calendarId === data.ticket.calendarId)
    .reduce((a, c) => a + c.quantity, 0);
  if (
    maxTicketByCalendar !== -1 &&
    (totalTicketPurchasedByCalendar >= maxTicketByCalendar ||
      data.ticket.quantity + totalTicketPurchasedByCalendar > maxTicketByCalendar)
  ) {
    throw new BadRequestError({
      code: ErrorCode.ORDER_MAX_TICKET_PER_CALENDAR,
      message: 'Bạn đã mua đủ số lượng vé cho phép trong ngày',
    });
  }
};

const getAutoPromotion = async (order) => {
  const promotions = await PromotionModel.query('eventId')
    .eq(order.PK)
    .where('active')
    .eq(true)
    .where('type')
    .eq('auto')
    .exec()
    .then((res) => res.toJSON());

  const promotionMatched = promotions?.filter(
    (item) => order.amount >= item.minOrderAmount || order.amountInUSD >= item.minOrderAmountInUSD,
  );

  if (!promotionMatched?.length) {
    return;
  }
  if (promotionMatched.appliesTo === 'ticket') {
    const ticketApplied = tickets.filter((item) =>
      promotionMatched.some((item) => item.tickets.some((id) => id === item.id)),
    );
    if (!ticketApplied?.length) {
      return;
    }
  }

  return orderBy(promotionMatched, ['minOrderAmount'], ['desc']).shift();
};

module.exports.OrderService = {
  getEventInfo,
  createOrder,
  getOrderInfo,
  getProductInfo,
  getFanpassInfo,
  reviewOrder,
  updateOrderStatus,
  getOrderInReview,
  addItemToCart,
  removeProductFromCart,
  getCart,
  getMerchantInfo,
  getEventCart,
  getEventDetail,
  cancelOrder,
  cartConfirm,
  validateTicketQuantity,
};
