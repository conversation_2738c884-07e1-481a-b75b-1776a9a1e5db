const https = require('https');
const {getSecretValue} = require('../../../../common/secret-manager');
const {BadRequestError} = require('../../../../common/exceptions');
const {OrderService} = require('./order.service');
const {S3Client, PutObjectCommand} = require('@aws-sdk/client-s3');

const SECRET_NAME = 'GHTKService';

const client = new S3Client();
const s3Bucket = process.env.S3_UPLOAD_BUCKET;

class GhtkService {
  #tokenKey;
  #apiUrl;
  #merchantId;

  constructor(merchantId) {
    this.#merchantId = merchantId;
  }

  async #getConfig() {
    const config = await getSecretValue(SECRET_NAME);
    this.#tokenKey = config.tokenKey;
    this.#apiUrl = config.apiUrl;
  }

  async #getPickAddress() {
    const merchant = await OrderService.getMerchantInfo(this.#merchantId);

    return {
      pick_province: merchant.shipmentPickAddress.province,
      pick_district: merchant.shipmentPickAddress.district,
      pick_address: merchant.shipmentPickAddress.address,
      pick_name: merchant.shipmentPickAddress.name,
      pick_tel: merchant.shipmentPickAddress.phoneNumber,
    };
  }

  async calculateTransportFee(address) {
    await this.#getConfig();
    const pickAddress = await this.#getPickAddress();
    const urlSearchParams = new URLSearchParams({
      ...address,
      pick_district: pickAddress.pick_province,
      pick_province: pickAddress.pick_district,
      pick_address: pickAddress.pick_address,
      weight: 500,
      transport: 'road',
      deliver_option: 'none',
    });
    const queryString = urlSearchParams.toString();
    const url = `${this.#apiUrl}/services/shipment/fee?${queryString}`;
    const options = {
      headers: {
        Token: this.#tokenKey,
      },
    };

    return new Promise((resolve, reject) => {
      const requestError = {
        code: 800001,
        message: 'Tính phí vận chuyển thất bại.',
      };
      const req = https.get(url, options, (res) => {
        res.setEncoding('utf8');
        res.on('data', (body) => {
          const res = JSON.parse(body);
          if (res.success) {
            resolve(JSON.parse(body).fee);
            return;
          }
          reject(
            new BadRequestError({
              ...requestError,
              params: {
                ghtkError: res,
              },
            }),
          );
        });
      });

      req.on('error', (e) => {
        reject(
          new BadRequestError({
            ...requestError,
            params: {
              ghtkError: {
                message: e.message,
              },
            },
          }),
        );
      });
      req.end();
    });
  }

  async postOrder(order) {
    console.info('GhtkService::postOrder - order: ', order);
    const {orderId, products, shipmentInfo, amount, transportFee} = order;
    await this.#getConfig();
    const pickAddress = await this.#getPickAddress();

    const payload = JSON.stringify({
      products: products.map((item) => ({
        name: item.name,
        weight: 0.1,
        quantity: item.quantity,
        product_code: '',
      })),
      order: {
        id: orderId,
        ...pickAddress,
        tel: shipmentInfo.phoneNumber,
        name: shipmentInfo.name,
        address: shipmentInfo.address,
        province: shipmentInfo.province,
        district: shipmentInfo.district,
        ward: shipmentInfo.ward,
        hamlet: 'Khác',
        is_freeship: '0',
        pick_money: amount - transportFee,
        value: amount - transportFee,
        transport: 'road',
        pick_option: 'cod',
      },
    });
    console.info('GhtkService::postOrder - payload: ', payload);

    const options = {
      hostname: this.#apiUrl.replace('https://', ''),
      port: 443,
      path: '/services/shipment/order/?ver=1.5',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(payload),
        Token: this.#tokenKey,
      },
    };

    return new Promise((resolve, reject) => {
      const req = https.request(options, (res) => {
        res.setEncoding('utf8');
        res.on('data', async (body) => {
          const res = JSON.parse(body);
          console.info('GhtkService::postOrder - response: ', res);
          if (res.success) {
            const order = JSON.parse(body).order;
            const labelUrl = await this.#uploadLabelToS3(order.label);
            console.info('GhtkService::postOrder - labelUrl: ', labelUrl);
            order.labelUrl = labelUrl;

            resolve(order);
            return;
          }
          resolve(res);
        });
      });

      req.on('error', (error) => {
        console.error('GhtkService::postOrder - error: ', error.message);
        reject(error);
      });

      // write data to request body
      req.write(payload);
      req.end();
    });
  }

  async #uploadLabelToS3(label) {
    const labelUrl = `https://services.giaohangtietkiem.vn/services/label/${label}?original=landscape&page_size=A5`;
    const fileKey = `uploads/merchandise/labels/${label}.pdf`;
    return new Promise((resolve, reject) => {
      https.get(
        labelUrl,
        {
          headers: {Token: this.#tokenKey},
        },
        async (res) => {
          const data = [];
          res.on('data', (chunk) => {
            data.push(chunk);
          });
          res.on('end', async () => {
            try {
              const buffer = Buffer.concat(data);
              const params = {
                Bucket: s3Bucket,
                Key: fileKey,
                ContentType: 'application/pdf',
                Body: buffer,
              };
              const command = new PutObjectCommand(params);
              await client.send(command);
              resolve(`${process.env.MEDIA_DOMAIN}/${fileKey}`);
            } catch (error) {
              console.log('GhtkService::uploadLabelToS3 - end:error: ', error.message);
              resolve();
            }
          });
          res.on('error', (error) => {
            console.error('GhtkService::uploadLabelToS3 - error: ', error.message);
            resolve();
          });
        },
      );
    });
  }
}

module.exports = {GhtkService};
