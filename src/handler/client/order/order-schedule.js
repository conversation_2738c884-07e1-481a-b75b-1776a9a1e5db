const moment = require('moment');
const {EventModel} = require('../../../models/EventModel');
const {TicketStorageModel} = require('../../../models/TicketStorageModel');
const {OrderModel} = require('../../../models/OrderModel');
const OrderStatus = require('../../../common/types/order-status');
const EventStatus = require('../../../common/types/event-status');
const ProductTypes = require('../../../common/types/product-type');

module.exports.resolveHoldingTicket = async () => {
  // get active event
  let activeEvents = await EventModel.query('status')
    .eq(EventStatus.OPEN)
    .using('statusIndex')
    .exec();
  activeEvents = activeEvents.toJSON();

  for (const event of activeEvents) {
    await resolveByEvent(event.PK);
  }
};

async function resolveByEvent(eventId) {
  let eventTickets = await TicketStorageModel.query({PK: eventId}).exec();
  eventTickets = eventTickets.toJSON();
  let holderExpired = [];
  const isExpired = (ticket, userId, beginTime) => {
    const diff = moment().diff(moment(beginTime), 'seconds');
    console.log(
      'Event: %s | User: %s | ticket: %s :: diff: %s',
      eventId,
      userId,
      ticket.ticketId,
      diff,
    );
    // holding in 13 minutes
    return diff >= 780;
  };
  for (const eTicket of eventTickets) {
    let holders =
      eTicket.holders?.filter((item) => isExpired(eTicket, item.userId, item.beginTime)) || [];
    holders = holders.map((item) => resolveTicket(eventId, eTicket.ticketId, item));
    holderExpired = [...holderExpired, ...holders];
  }
  if (!!holderExpired.length) {
    await Promise.all(holderExpired);
    console.log('Holding ticket is resolved for event %s', eventId);
  }
}

async function resolveTicket(productId, ticketId, holder) {
  const resolveTicketExe = TicketStorageModel.update(
    {
      PK: productId,
      ticketId,
    },
    {
      $ADD: {
        quantity: holder.ticketQuantity,
      },
      $DELETE: {
        holders: [
          {
            userId: holder.userId,
            orderId: holder.orderId,
          },
        ],
      },
    },
  );
  const resolveOrderExe = OrderModel.update(
    {PK: productId, orderId: holder.orderId},
    {status: OrderStatus.EXPIRED},
  );

  await Promise.all([resolveTicketExe, resolveOrderExe]);
}

module.exports.updateOrderExpired = async () => {
  const expiredTime = moment().subtract(14, 'minutes').toDate().getTime();
  const orders = await OrderModel.query('productType')
    .eq(ProductTypes.P_FANPASS)
    .where('status')
    .eq(OrderStatus.REVIEW)
    .where('createdAt')
    .le(expiredTime)
    .all()
    .exec();

  console.log('updateOrderExpired - total order expired: ', orders.count);
  if (!orders.count) {
    return;
  }

  let promises = [];
  for (const order of orders) {
    order.status = OrderStatus.EXPIRED;
    promises.push(order.save());
    if (promises.length >= 25) {
      await Promise.all(promises);
      promises = [];
    }
  }
  if (promises.length) {
    await Promise.all(promises);
  }
};
