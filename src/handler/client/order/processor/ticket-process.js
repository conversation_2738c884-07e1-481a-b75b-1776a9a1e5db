const {pick} = require('lodash');
const {BadRequestError} = require('../../../../common/exceptions');
const EventStatus = require('../../../../common/types/event-status');
const ProductTypes = require('../../../../common/types/product-type');
const {ErrorCode} = require('../../../../constants/error-code');
const {OrderModel} = require('../../../../models/OrderModel');
const {OrderUtil} = require('../../../../utils/order.util');
const {v4} = require('../../../../utils/uuid.util');
const {OrderService} = require('../services/order.service');
const TicketClassModel = require('../../../../../drizzle/models/ticket-class');
const TicketModel = require('../../../../../drizzle/models/ticket');
const {sendMessageToTicketQueue} = require('../../../../common/sqs');
const {resolveHoldTicketByOrder} = require('../../../../../drizzle/models/ticket');
const {ticketStatus} = require('../../../cms/ticket/const');
const OrderStatus = require('../../../../common/types/order-status');
const CollaborationType = require('../../../../common/types/collaboration.type');

class TicketProcess {
  #product;
  #order;
  #tickets;
  #totalAmount;
  #totalAmountInUSD;
  #queueId;
  #ticketsSeatmap;

  constructor(orderInfo) {
    this.orderInfo = orderInfo;
    if (orderInfo.ticket?.id) {
      this.#queueId = `${orderInfo.productId}_${orderInfo.ticket.id}`;
      if (orderInfo.ticket?.zoneId) {
        this.#queueId += `_${orderInfo.ticket.zoneId}`;
      }
    }
  }

  async #getTicketsSeatmap() {
    const tickets = await TicketModel.getTicketsByIds({
      ids: this.orderInfo.seatmapTickets.map((v) => v.id),
    });
    this.#ticketsSeatmap = tickets;
  }

  async #getTicketClasses() {
    const tickets = await TicketClassModel.getTicketClassesByEvent(this.orderInfo.productId);
    this.#product = {
      ...this.#product,
      tickets,
    };
  }

  async #getProductInfo() {
    const productId = this.orderInfo.productId;
    this.#product = await OrderService.getEventInfo(productId);
    const {status} = this.#product;
    if (!EventStatus.isOpen(status)) {
      throw new BadRequestError({
        code: ErrorCode.TICKET_NOT_OPEN,
        message: EventStatus.isClose(status) ? 'Sự kiện đã đóng bán vé' : 'Sự kiện chưa mở bán vé',
      });
    }
    await this.#getTicketClasses();
  }

  async #validateOrder() {
    const ticket = this.orderInfo.ticket;
    const isExist = this.#product.tickets.some((t) => t.id === ticket.id);
    if (!isExist) {
      throw new BadRequestError({
        code: ErrorCode.TICKET_NOT_EXIST,
        message: 'Ticket không tồn tại',
      });
    }
  }

  async #calcTicketPrice() {
    return new Promise((resolve, reject) => {
      try {
        const tickets = [this.orderInfo.ticket].map((t) => {
          const ticket = this.#product.tickets.find(
            (eTicket) => eTicket.id === t.id && eTicket.calendarId === t.calendarId,
          );

          return {...ticket, quantity: t.quantity};
        });

        this.#tickets = tickets.map((t) => ({
          ...t,
          totalAmount: OrderUtil.calcTicketPrice(t, 'finalPriceVn'),
          totalAmountInUSD: OrderUtil.calcTicketPrice(t, 'finalPriceUsd'),
        }));

        this.#totalAmount = this.#tickets.reduce((total, ticket) => total + ticket.totalAmount, 0);
        this.#totalAmountInUSD = this.#tickets.reduce(
          (total, ticket) => total + ticket.totalAmountInUSD,
          0,
        );
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  async #createOrder() {
    // clean all review order
    await this.#clearDuplicationOrder();
    const orderId = v4();
    if (this.orderInfo.paymentGateway === 'free' && this.#totalAmount > 0) {
      throw new BadRequestError({
        code: ErrorCode.ORDER_INVALID_GATEWAY,
        message: 'Cổng thanh toán không phù hợp với số tiền',
      });
    }
    const extraData = {
      productId: this.orderInfo.productId,
      productType: ProductTypes.P_EVENT,
      domain: this.#product?.domain,
      version: 'v2',
    };

    const order = {
      PK: this.orderInfo.productId,
      orderId,
      originalAmount: this.#totalAmount,
      amount: this.#totalAmount,
      amountInUSD: this.#totalAmountInUSD,
      currencyCode: 'VND',
      productType: ProductTypes.P_EVENT,
      extraData,
      userId: this.orderInfo.userEmail,
      userRefId: this.orderInfo.refId,
    };
    const ticketClassFields = [
      'id',
      'calendarId',
      'name',
      'color',
      'originalPriceVn',
      'originalPriceUsd',
      'finalPriceVn',
      'quantity',
      'finalPriceUsd',
      'description',
      'seatType',
      'prototypeUrl',
    ];

    if (this.#tickets?.length) {
      order['tickets'] = this.#tickets.map((ticket) => pick(ticket, ticketClassFields));
    }

    if (this.#product.general.collaboration === 'seatmap') {
      order.status = OrderStatus.REVIEW;
    }

    this.#order = await OrderService.createOrder(order);
  }

  async #validateTicketQuantity() {
    await OrderService.validateTicketQuantity(this.orderInfo, this.#product);
  }

  async #resolveHoldingTicket(orderId) {
    await resolveHoldTicketByOrder(orderId);
  }

  async #clearDuplicationOrder() {
    const existedOrders = await OrderModel.query('PK')
      .eq(this.orderInfo.productId)
      .where('userId')
      .eq(this.orderInfo.userEmail)
      .using('userIdIndex')
      .where('productType')
      .eq(ProductTypes.P_EVENT)
      .where('status')
      .in(['PENDING', 'REVIEW'])
      .exec();

    for (const order of existedOrders.toJSON()) {
      await OrderModel.delete({
        PK: order.PK,
        orderId: order.orderId,
      });
      await this.#resolveHoldingTicket(order.orderId);
    }
  }

  async #sentToTicketQueue() {
    await sendMessageToTicketQueue(
      {
        productId: this.orderInfo.productId,
        orderId: this.orderId,
        ticketClassId: this.orderInfo.ticket.id,
        zoneId: this.orderInfo.ticket.zoneId,
      },
      this.#queueId,
    );
  }

  async #validateTicketSeatmap() {
    const notAvailableTickets = [];
    this.#ticketsSeatmap.forEach((ticket) => {
      if (ticket.status !== ticketStatus.IDLE) {
        notAvailableTickets.push(ticket.id);
      }
    });
    return notAvailableTickets;
  }

  async #processTicketClass() {
    await this.#validateOrder();
    await this.#calcTicketPrice();
    await this.#validateTicketQuantity();
    if (CollaborationType.isSeatmapZone(this.#product.general.collaboration)) {
      // TODO:
    }
    await this.#createOrder();
    await this.#sentToTicketQueue();
  }

  async #validateQuantitySeatmap() {
    const orders = await OrderModel.query('PK')
      .eq(this.orderInfo.productId)
      .where('userId')
      .eq(this.orderInfo.userEmail)
      .where('status')
      .eq(OrderStatus.SUCCESS)
      .exec()
      .then((res) => res.toJSON())
      .then((orders) => orders.map((order) => order.tickets).flat());

    const totalTicketPurchased = orders.reduce((a, c) => a + c.quantity, 0);

    // kiem tra so luong ve user da mua trong su kien
    const maxEventTicket = this.#product.maxTicketPerUser;
    const isLimitQuantityPerEvent = maxEventTicket !== -1;

    if (
      isLimitQuantityPerEvent &&
      (totalTicketPurchased >= maxEventTicket ||
        this.orderInfo.seatmapTickets.length > maxEventTicket)
    ) {
      return true;
    }
    return false;
  }

  async #processSeatmap() {
    await this.#getTicketsSeatmap();
    let validateTicketSeatmap = await this.#validateTicketSeatmap();
    if (validateTicketSeatmap.length > 0) {
      throw new BadRequestError({
        code: ErrorCode.TICKET_NOT_AVAILABLE,
        message: 'Vé không còn trống',
        tickets: validateTicketSeatmap,
      });
    }
    const result = await this.#validateQuantitySeatmap();

    if (result) {
      throw new BadRequestError({
        code: ErrorCode.ORDER_MAX_TICKET_PER_EVENT,
        message: 'Bạn đã mua đủ số lượng vé cho phép trong sự kiện',
      });
    }
    const ticketsClass = new Map();
    this.#ticketsSeatmap.forEach((v) => {
      if (!ticketsClass.has(v.ticketClassId)) {
        ticketsClass.set(v.ticketClassId, 1);
      } else {
        ticketsClass.set(v.ticketClassId, ticketsClass.get(v.ticketClassId) + 1);
      }
    });
    this.#tickets = Array.from(ticketsClass).map((t) => {
      const ticketClass = this.#product.tickets.find((v) => v.id === t[0]);
      ticketClass.quantity = t[1];
      return {
        ...ticketClass,
        totalAmount: OrderUtil.calcTicketPrice(ticketClass, 'finalPriceVn'),
        totalAmountInUSD: OrderUtil.calcTicketPrice(ticketClass, 'finalPriceUsd'),
      };
    });
    this.#totalAmount = 0;
    this.#totalAmountInUSD = 0;
    this.#ticketsSeatmap.forEach((ticket) => {
      this.#totalAmount += ticket.finalPriceVn;
      this.#totalAmountInUSD += ticket.finalPriceUsd;
    });

    // Temporary validate ticket seatmap again, will remove after do the lock ticket solution
    validateTicketSeatmap = await this.#validateTicketSeatmap();
    if (validateTicketSeatmap.length > 0) {
      throw new BadRequestError({
        code: ErrorCode.TICKET_NOT_AVAILABLE,
        message: 'Vé không còn trống',
        tickets: validateTicketSeatmap,
      });
    }
    await this.#createOrder();
    await TicketModel.updateTickets({
      eventId: this.orderInfo.productId,
      calendarId: this.#ticketsSeatmap[0].calendarId,
      ids: this.#ticketsSeatmap.map((v) => v.id),
      status: ticketStatus.HOLD,
      orderIdUpdate: this.#order.orderId,
    });
  }

  async process() {
    await this.#getProductInfo();
    const collaboration = this.#product.general.collaboration;
    if (CollaborationType.isSeatmap(collaboration)) {
      await this.#processSeatmap();
    } else {
      await this.#processTicketClass();
    }
  }

  get orderId() {
    return this.#order.orderId;
  }

  get extraData() {
    return this.#order.extraData;
  }

  get amount() {
    return this.#order.amount;
  }
}

module.exports = {TicketProcess};
