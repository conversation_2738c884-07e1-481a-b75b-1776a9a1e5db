const {pick} = require('lodash');
const {BadRequestError} = require('../../../../common/exceptions');
const EventStatus = require('../../../../common/types/event-status');
const ProductTypes = require('../../../../common/types/product-type');
const {ErrorCode} = require('../../../../constants/error-code');
const {OrderModel} = require('../../../../models/OrderModel');
const {TicketStorageModel} = require('../../../../models/TicketStorageModel');
const {OrderUtil} = require('../../../../utils/order.util');
const {v4} = require('../../../../utils/uuid.util');
const {OrderService} = require('../services/order.service');

class EventistaOrderProcess {
  #product;
  #order;
  #tickets;
  #totalAmount;
  #totalAmountInUSD;

  constructor(orderInfo) {
    if (!ProductTypes.includes(orderInfo.productType)) {
      throw new BadRequestError({
        code: ErrorCode.PRODUCT_TYPE_INVALID,
        message: 'Product type is not specified',
      });
    }

    this.productType = orderInfo.productType;
    this.orderInfo = orderInfo;
  }

  async #getProductInfo() {
    const productId = this.orderInfo.productId;
    if (ProductTypes.isEvent(this.productType)) {
      this.#product = await OrderService.getEventInfo(productId);
      // kiem tra trang thai mo ban ve cua su kien
      const {status} = this.#product;
      if (!EventStatus.isOpen(status)) {
        const message = EventStatus.isClose(status)
          ? 'Sự kiện đã đóng bán vé'
          : 'Sự kiện chưa mở bán vé';
        throw new BadRequestError({
          code: ErrorCode.TICKET_NOT_OPEN,
          message,
        });
      }
    } else {
      const fanpass = await OrderService.getFanpassInfo(productId);
      this.#product = {
        ...fanpass,
        tickets: fanpass.ticket.values,
      };
    }
  }

  /**
   * kiem tra ticket co phai cua su kien hay ko?
   */
  async #validateOrder() {
    const ticketCalendar = this.orderInfo.tickets.map((t) => t.calendarId);
    if (Array.from(new Set(ticketCalendar)).length > 1) {
      throw new BadRequestError({
        code: ErrorCode.ORDER_TICKET_CALENDAR_TOO_MUCH,
        message: 'Bạn chỉ có thể mua vé cùng ngày trên một đơn hàng',
      });
    }
    const orderTickets = this.orderInfo.tickets.map((t) => `${t.ticketId}#${t.calendarId}`);
    const productTickets = this.#product.tickets.map((t) => `${t.ticketId}#${t.calendarId}`);

    const ticketValid = orderTickets.every((el) => productTickets.includes(el));
    if (!ticketValid) {
      throw new BadRequestError({
        code: ErrorCode.TICKET_NOT_EXIST,
        message: 'Ticket không tồn tại',
      });
    }
  }

  async #calcTicketPrice() {
    return new Promise((resolve, reject) => {
      try {
        const tickets = this.orderInfo.tickets.map((t) => {
          const ticket = this.#product.tickets.find(
            (eTicket) => eTicket.ticketId === t.ticketId && eTicket.calendarId === t.calendarId,
          );

          return {...ticket, quantity: t.quantity};
        });

        this.#tickets = tickets.map((t) => ({
          ...t,
          totalAmount: OrderUtil.calcTicketPrice(t),
          totalAmountInUSD: OrderUtil.calcTicketPrice(t, 'ticketPriceInUSD'),
        }));

        this.#totalAmount = this.#tickets.reduce((total, ticket) => total + ticket.totalAmount, 0);
        this.#totalAmountInUSD = this.#tickets.reduce(
          (total, ticket) => total + ticket.totalAmountInUSD,
          0,
        );
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  async #createOrder() {
    // clean all review order
    await this.#clearDupplicationOrder();
    const orderId = v4();
    if (this.orderInfo.paymentGateway === 'free' && this.#totalAmount > 0) {
      throw new BadRequestError({
        code: ErrorCode.ORDER_INVALID_GATEWAY,
        message: 'Cổng thanh toán không phù hợp với số tiền',
      });
    }
    const extraData = {
      productId: this.orderInfo.productId,
      productType: this.productType,
      domain: this.#product?.domain,
    };

    const order = {
      PK: this.orderInfo.productId,
      orderId,
      originalAmount: this.#totalAmount,
      amount: this.#totalAmount,
      amountInUSD: this.#totalAmountInUSD,
      currencyCode: 'VND',
      tickets: this.#tickets.map((ticket) =>
        pick(ticket, [
          'ticketId',
          'calendarId',
          'seatType',
          'ticketDescription',
          'ticketPrice',
          'ticketPriceInUSD',
          'ticketType',
          'quantity',
          'totalAmount',
          'totalAmountInUSD',
          'duration',
        ]),
      ),
      productType: this.productType,
      extraData,
      userId: this.orderInfo.userEmail,
      userRefId: this.orderInfo.refId,
    };
    if (ProductTypes.isFanpass(this.productType)) {
      order.status = 'REVIEW';
    }
    this.#order = await OrderService.createOrder(order);
  }

  async #holdingTicket() {
    const holders = [];
    const updateTicket = async (ticket) => {
      await TicketStorageModel.update(
        {
          PK: this.orderInfo.productId,
          ticketId: `${ticket.ticketId}#${ticket.calendarId}`,
        },
        {
          $ADD: {
            quantity: ticket.quantity * -1,
            holders: [
              {
                userId: this.orderInfo.userEmail,
                orderId: this.#order.orderId,
                ticketQuantity: ticket.quantity,
                beginTime: new Date(),
              },
            ],
          },
        },
      );
    };
    for (const ticket of this.#tickets) {
      holders.push(updateTicket(ticket));
    }
    await Promise.all(holders);
  }

  async #validateTicketQuantity() {
    if (!ProductTypes.isEvent(this.productType)) {
      return;
    }
    await OrderService.reviewOrder(
      {
        userId: this.orderInfo.userEmail,
        productId: this.orderInfo.productId,
        tickets: this.#tickets,
      },
      this.#product,
    );
  }

  async #clearDupplicationOrder() {
    if (!ProductTypes.isEvent(this.productType)) {
      return;
    }
    const existedOrders = await OrderModel.query('PK')
      .eq(this.orderInfo.productId)
      .where('userId')
      .eq(this.orderInfo.userEmail)
      .using('userIdIndex')
      .where('productType')
      .eq(ProductTypes.P_EVENT)
      .where('status')
      .eq('REVIEW')
      .exec();

    for (const order of existedOrders.toJSON()) {
      await OrderModel.delete({
        PK: order.PK,
        orderId: order.orderId,
      });
      await this.#resolveHoldingTicket(order);
    }
  }

  async #resolveHoldingTicket(order) {
    try {
      const holders = [];
      const updateTicket = async (ticket) => {
        await TicketStorageModel.update(
          {
            PK: this.orderInfo.productId,
            ticketId: `${ticket.ticketId}#${ticket.calendarId}`,
          },
          {
            $ADD: {
              quantity: ticket.quantity,
            },
            $DELETE: {
              holders: [
                {
                  userId: order.userId,
                  orderId: order.orderId,
                },
              ],
            },
          },
        );
      };

      for (const ticket of order.tickets) {
        holders.push(updateTicket(ticket));
      }
      await Promise.all(holders);
      console.log('Holding ticket is resolved');
    } catch (error) {
      console.error('Resolve ticket holding: ', error.message);
    }
  }

  async process() {
    await this.#getProductInfo();
    await this.#validateOrder();
    await this.#calcTicketPrice();
    await this.#validateTicketQuantity();
    await this.#createOrder();
    if (ProductTypes.isEvent(this.productType)) {
      await this.#holdingTicket();
    }
  }

  get orderId() {
    return this.#order.orderId;
  }

  get extraData() {
    return this.#order.extraData;
  }

  get amount() {
    return this.#order.amount;
  }
}

module.exports = {EventistaOrderProcess};
