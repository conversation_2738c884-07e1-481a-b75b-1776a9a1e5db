const {BadRequestError} = require('../../../../common/exceptions');
const ProductTypes = require('../../../../common/types/product-type');
const {OrderUtil} = require('../../../../utils/order.util');
const {OrderStatus} = require('../../../payment/const');
const {GhtkService} = require('../services/ghtk.service');
const {OrderService} = require('../services/order.service');

class MerchantOrderProcess {
  #orderRequest;
  #cart;
  #products;
  #order;
  #totalAmount;
  #transportFee;

  constructor(cartInfo) {
    this.#orderRequest = cartInfo;
  }

  async #getCartInfo() {
    const {userEmail, merchantId} = this.#orderRequest;
    const cart = await OrderService.getCart(userEmail, merchantId);

    if (!cart) {
      throw new BadRequestError({
        code: 800002,
        message: 'Thông tin giỏ hàng không tồn tại',
      });
    }
    // this.#merchant = merchant;
    this.#cart = cart;
  }

  /**
   * kiem tra sản phẩm co phai cua merchant hay ko?
   */
  async #validateCart() {
    const orderItems = this.#orderRequest.items.map((t) => t.id);
    const cartItems = this.#cart.items.map((t) => t.id);

    const itemValid = orderItems.every((el) => cartItems.includes(el));
    if (!itemValid) {
      throw new BadRequestError({
        code: 800003,
        message: 'Không tìm thấy thông tin sản phẩm trong giỏ hàng',
      });
    }
  }

  async #calcCartAmount() {
    return new Promise(async (resolve, reject) => {
      try {
        const products = this.#cart.items;

        this.#products = products.map((t) => ({
          ...t,
          totalAmount: OrderUtil.calcMerchantProductPrice(t),
        }));

        // console.log('==> #calcCartAmount::#products - ', this.#products);

        const totalAmount = this.#products.reduce((total, item) => total + item.totalAmount, 0);
        await this.#calcTransportFee(totalAmount);

        this.#totalAmount = totalAmount + this.#transportFee;
        console.log('==> #calcCartAmount::#totalAmount - ', this.#totalAmount);
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  async #calcTransportFee(cartAmount) {
    const {merchantId, shipmentInfo} = this.#orderRequest;

    const ghtkService = new GhtkService(merchantId);
    const {fee} = await ghtkService.calculateTransportFee({
      province: shipmentInfo.province,
      district: shipmentInfo.district,
      address: shipmentInfo.address,
      value: cartAmount,
    });

    console.log('==> #calcTransportFee::fee -', fee);

    this.#transportFee = fee;
  }

  async #createOrder() {
    const {merchantId, shipmentInfo} = this.#orderRequest;
    const orderId = this.#cart.orderId;
    const extraData = {
      productId: merchantId,
      productType: ProductTypes.P_MERCHANDISE, // TODO
    };

    const order = {
      PK: merchantId,
      orderId,
      amount: this.#totalAmount,
      products: this.#products,
      productType: ProductTypes.P_MERCHANDISE, // TODO
      extraData,
      userId: this.#orderRequest.userEmail,
      transportFee: this.#transportFee,
      shipmentInfo,
      status: OrderStatus.OPEN,
      paymentGateway: this.#orderRequest.paymentGateway,
    };
    this.#order = await OrderService.createOrder(order);
  }

  async #holdingTicket() {
    // TODO
  }

  async #validateItemQuantity() {
    // TODO:
  }

  #resolveHoldingItems(order) {
    // TODO:
  }

  async process() {
    await this.#getCartInfo();
    await this.#validateCart();
    await this.#calcCartAmount();
    // await this.#validateTicketQuantity();
    await this.#createOrder();
  }

  get orderId() {
    return this.#order.orderId;
  }

  get extraData() {
    return this.#order.extraData;
  }

  get amount() {
    return this.#order.amount;
  }
}

module.exports = {MerchantOrderProcess};
