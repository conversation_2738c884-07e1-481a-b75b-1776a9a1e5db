const ResponseBuilder = require('../../../utils/response-builder.util');
const {errorHandler} = require('../../../utils/error-handler.util');
const {PodcastEndPoint} = require('./const');
const {CONFIG_MODEL_KEY} = require('../../../common/types/config.type');
const {getContentByConfig} = require('../../../utils/config.util');
const {BlogModel} = require('../../../models/BlogModel');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      case PodcastEndPoint.PAGE:
        data = await getContentByConfig(CONFIG_MODEL_KEY.PODCAST_PAGE);
        break;
      case PodcastEndPoint.GET_PODCAST_DETAIL:
        data = await getPodcastDetail(event);
        break;
      case PodcastEndPoint.GET_ALBUM_DETAIL:
        data = await getAlbumDetail(event);
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (err) {
    return errorHandler(err);
  }
};

const getPodcastDetail = async (event) => {
  const podcastId = event.pathParameters.podcastId;
  return await BlogModel.get({
    PK: 'PODCAST',
    SK: podcastId,
  });
};

const getAlbumDetail = async (event) => {
  const albumId = event.pathParameters.albumId;
  const album = await BlogModel.get({
    PK: 'ALBUM',
    SK: albumId,
  });

  if (album.children) {
    const children = await BlogModel.batchGet(
      album.children.map((i) => ({PK: i.id.split('_')[0], SK: i.id})),
    );
    album.children = children.toJSON();
  }

  return album;
};
