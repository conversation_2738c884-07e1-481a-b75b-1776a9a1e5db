const ResponseBuilder = require('../../../utils/response-builder.util');
const {errorHandler} = require('../../../utils/error-handler.util');
const {ConcertEndPoint} = require('./const');
const {CONFIG_MODEL_KEY} = require('../../../common/types/config.type');
const {getContentByConfig} = require('../../../utils/config.util');
const {EventModel} = require('../../../models/EventModel');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      case ConcertEndPoint.PAGE:
        data = await getContentByConfig(CONFIG_MODEL_KEY.CONCERT_PAGE);
        break;
      case ConcertEndPoint.RECAP:
        data = await getRecap();
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (err) {
    return errorHandler(err);
  }
};

const getRecap = async () => {
  const query = await EventModel.query('status').eq('CLOSE').using('statusIndex').all().exec();

  const concerts = query.toJSON().filter((item) => item.categories?.includes('concert'));

  return concerts;
};
