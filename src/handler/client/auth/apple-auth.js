const {BadRequestError} = require('../../../common/exceptions');
const {SOCIAL_AUTH_FAILED} = require('../../../constants/error-code');
const jwt = require('jsonwebtoken');
const jwksClient = require('jwks-rsa');

class AppleService {
  constructor(idToken) {
    this.idToken = idToken;
  }

  async verify() {
    try {
      const decoded = await new Promise((resolve, reject) => {
        jwt.verify(this.idToken, this.getKey, {algorithms: ['RS256']}, (err, decoded) => {
          if (err) {
            console.error('Token verification failed:', err);
            reject(err);
          }
          resolve(decoded);
        });
      });
      this.validateTokenClaims(decoded);
    } catch (error) {
      throw new BadRequestError({
        code: SOCIAL_AUTH_FAILED,
        message: error.message,
      });
    }
  }

  getKey(header, callback) {
    const client = jwksClient({
      jwksUri: 'https://appleid.apple.com/auth/keys',
    });
    client.getSigningKey(header.kid, (err, key) => {
      if (err) {
        return callback(err);
      }
      const signingKey = key.getPublicKey();
      callback(null, signingKey);
    });
  }

  validateTokenClaims(decoded) {
    const now = Math.floor(Date.now() / 1000);

    if (decoded.iss !== 'https://appleid.apple.com') {
      throw new Error('Invalid issuer');
    }

    if (decoded.aud !== 'com.eventista.trecenter') {
      throw new Error('Invalid audience');
    }

    if (decoded.exp < now) {
      throw new Error('Token expired');
    }

    console.log('Token is valid:', decoded);
    this.payload = decoded;
  }

  get user() {
    return {
      email: this.payload['email'],
      appleIdentifier: this.payload['sub'],
      isApplePrivateEmail: this.payload['is_private_email'],
    };
  }
}

module.exports = {AppleService};
