const {errorHandler} = require('../../../utils/error-handler.util');
const ResponseBuilder = require('../../../utils/response-builder.util');
const ValidatorUtil = require('../../../utils/request-validator.util');
const {AuthEndPoint} = require('./const');
const UserService = require('./auth.service');
const {GoogleService} = require('./google-auth');
const {AppleService} = require('./apple-auth');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      case AuthEndPoint.REGISTER:
        data = await register(event);
        break;
      case AuthEndPoint.SEND_OTP:
        data = await sendOTP(event);
        break;
      case AuthEndPoint.VERIFY_OTP:
        data = await verifyOTP(event);
        break;
      case AuthEndPoint.LOGIN:
        data = await login(event);
        break;
      case AuthEndPoint.RESET_PASSWORD:
        data = await resetPassword(event);
        break;
      case AuthEndPoint.CONFIRM_RESET_PASSWORD:
        data = await confirmResetPassword(event);
        break;
      case AuthEndPoint.GOOGLE_LOGIN:
        data = await verifyGoogleLogin(event);
        break;
      case AuthEndPoint.APPLE_LOGIN:
        data = await verifyAppleLogin(event);
        break;
      case AuthEndPoint.ME:
        data = await getUserInfo(event);
        break;
      case AuthEndPoint.CHANGE_PASSWORD:
        data = await changePassword(event);
        break;
      case AuthEndPoint.VERIFY_STUDENT:
        data = await verifyStudent(event);
        break;
      case AuthEndPoint.UPDATE_PROFILE:
        data = await updateUserInfo(event);
        break;
      case AuthEndPoint.DELETE_ACCOUNT:
        data = await deleteUser(event);
        break;
      case AuthEndPoint.GET_CERT:
        data = await sendCert(event);
        break;
      case AuthEndPoint.GET_TOKEN:
        data = await getToken(event);
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (err) {
    return errorHandler(err);
  }
};

async function register(event) {
  const requiredFields = ['email', 'password'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const {email, phone, password} = JSON.parse(event.body);
  await UserService.register({email, phone, password});
  await sendOTP(event);

  return {message: 'Đăng kí thành công'};
}

async function sendOTP(event) {
  const requiredFields = ['email'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const {email} = JSON.parse(event.body);
  await UserService.sendOTP(email);

  return {message: 'Mã OTP đã được gửi tới email của bạn'};
}

async function verifyOTP(event) {
  const requiredFields = ['email', 'otp'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const {email, otp} = JSON.parse(event.body);
  return UserService.verifyOTP(email, otp);
}

async function login(event) {
  const requiredFields = ['email', 'password'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const body = JSON.parse(event.body);
  const response = await UserService.verifyUserLogin(body);

  return response;
}

async function resetPassword(event) {
  const requiredFields = ['email', 'redirectUrl'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const body = JSON.parse(event.body);
  await UserService.resetPassword(body);

  return {message: 'Email tạo lại mật khẩu đã được gửi tới bạn'};
}

async function confirmResetPassword(event) {
  const requiredFields = ['newPassword'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const email = event.requestContext.authorizer.lambda.principalId;
  const body = JSON.parse(event.body);
  await UserService.confirmResetPassword({...body, email});

  return {message: 'Mật khẩu đã được đổi'};
}

async function verifyGoogleLogin(event) {
  const requiredFields = ['tokenId'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const {tokenId} = JSON.parse(event.body);
  const googleService = new GoogleService(tokenId);
  await googleService.verify();
  return await UserService.verifyThirdPartyLogin(googleService.user);
}

async function verifyAppleLogin(event) {
  const requiredFields = ['idToken'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const {idToken} = JSON.parse(event.body);
  const appleService = new AppleService(idToken);
  await appleService.verify();
  return await UserService.verifyThirdPartyLogin(appleService.user);
}

async function verifyStudent(event) {
  const requiredFields = [
    'name',
    'birthday',
    'gender',
    'icCard',
    'phone',
    'university',
    'major',
    'schoolYear',
    'studentId',
    'schoolCertificate',
  ];
  await ValidatorUtil.requireParams(event.body, requiredFields);
  const email = event.requestContext.authorizer.lambda.principalId;
  const body = JSON.parse(event.body);
  await UserService.verifyStudent({...body, email});
  return {message: 'Cập nhật thông tin thành công'};
}

async function getUserInfo(event) {
  const userEmail = event.requestContext.authorizer.lambda.principalId;
  return await UserService.getUserInfo(userEmail);
}

async function updateUserInfo(event) {
  const requiredFields = ['name', 'icCard', 'birthday'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const email = event.requestContext.authorizer.lambda.principalId;
  const body = JSON.parse(event.body);
  await UserService.updateUserInfo({email, ...body});
  return {message: 'Cập nhật thông tin thành công'};
}

async function changePassword(event) {
  const requiredFields = ['oldPassword', 'newPassword'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const userEmail = event.requestContext.authorizer.lambda.principalId;
  const body = JSON.parse(event.body);
  await UserService.changePassword({userEmail, ...body});

  return {message: 'Mật khẩu của bạn đã được thay đổi'};
}

async function sendCert(event) {
  const email = event.requestContext.authorizer.lambda.principalId;
  await UserService.sendCert(email);

  return {message: 'Gửi giấy xác nhận thành công'};
}

async function getToken(event) {
  const requiredFields = ['refreshToken'];
  await ValidatorUtil.requireParams(event.body, requiredFields);
  const body = JSON.parse(event.body);
  return await UserService.getToken(body.refreshToken);
}

async function deleteUser(event) {
  const email = event.requestContext.authorizer.lambda.principalId;
  await UserService.deleteUser(email);

  return {message: 'Tài khoản của bạn đã được xóa'};
}
