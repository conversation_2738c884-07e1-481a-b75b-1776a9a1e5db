const {OAuth2Client} = require('google-auth-library');
const {BadRequestError} = require('../../../common/exceptions');
const {SOCIAL_AUTH_FAILED} = require('../../../constants/error-code');

class GoogleService {
  constructor(tokenId) {
    this.tokenId = tokenId;

    this.client = new OAuth2Client({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    });
  }

  async verify() {
    try {
      this.loginTicket = await this.client.verifyIdToken({
        idToken: this.tokenId,
      });
    } catch (error) {
      throw new BadRequestError({
        code: SOCIAL_AUTH_FAILED,
        message: error.message,
      });
    }
  }

  get user() {
    const payload = this.loginTicket.getPayload();
    return {
      googleIdentifier: payload['sub'],
      email: payload['email'],
      profileImg: payload['picture'],
      name: payload['name'],
    };
  }
}

module.exports = {GoogleService};
