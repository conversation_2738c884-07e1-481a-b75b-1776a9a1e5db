const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs-then');

const {BadRequestError} = require('../../../common/exceptions');
const {VERIFY_TOKEN_INVALID} = require('../../../constants/error-code');
const UserRoles = require('../../../common/types/user-roles');

const generateEmailVerifyToken = (email) => {
  return jwt.sign({email}, process.env.JWT_EMAIL_VERIFICATION_SECRET, {
    expiresIn: 7200, // expires in 2 hours
  });
};

const verifyEmailToken = (token) => {
  return new Promise((resolve, reject) => {
    jwt.verify(token, process.env.JWT_EMAIL_VERIFICATION_SECRET, (error, decoded) => {
      if (error) {
        reject(
          new BadRequestError({
            code: VERIFY_TOKEN_INVALID,
            message: '<PERSON> đã hết hạn hoặc đã được sử dụng',
          }),
        );
        return;
      }
      resolve(decoded);
    });
  });
};

const hashPassword = async (password) => {
  return await bcrypt.hash(password, 8);
};

const signToken = (email, role = UserRoles.USER) => {
  let secret = process.env.CLIENT_SECRET_KEY;
  if (!UserRoles.isUser(role)) {
    secret = process.env.ADMIN_SECRET;
  }

  return jwt.sign({email, role}, secret, {
    expiresIn: 86400 * 30, // expires in 1 month
  });
};

const comparePassword = async (eventPassword, userPassword) => {
  if (!eventPassword || !userPassword) {
    return false;
  }
  return await bcrypt.compare(eventPassword, userPassword);
};

const createRefreshToken = (email) => {
  return jwt.sign({email}, process.env.JWT_SECRET, {
    expiresIn: 86400 * 60, // 2 months
  });
};

function verifyRefreshToken(token) {
  try {
    return jwt.verify(token, process.env.JWT_SECRET);
  } catch (err) {
    console.error(err.message);
    return null;
  }
}

const UserUtil = {
  generateEmailVerifyToken,
  verifyEmailToken,
  hashPassword,
  signToken,
  comparePassword,
  createRefreshToken,
  verifyRefreshToken,
};
module.exports = UserUtil;
