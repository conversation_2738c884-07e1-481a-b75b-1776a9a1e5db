const jwt = require('jsonwebtoken'); // used to create, sign, and verify tokens

// Policy helper function
const generatePolicy = (principalId, effect, resource) => {
  const authResponse = {};
  authResponse.principalId = principalId;
  if (effect && resource) {
    const policyDocument = {};
    policyDocument.Version = '2012-10-17';
    policyDocument.Statement = [];
    const statementOne = {};
    statementOne.Action = 'execute-api:Invoke';
    statementOne.Effect = effect;
    statementOne.Resource = resource;
    policyDocument.Statement[0] = statementOne;
    authResponse.policyDocument = policyDocument;
  }
  authResponse.context = {principalId};
  console.log(JSON.stringify(authResponse, null, 4));
  return authResponse;
};

module.exports.auth = (event, context, callback) => {
  console.log('Event - ', event);

  try {
    // check header or url parameters or post parameters for token
    const token = event.identitySource[0];
    console.log('token - ', token);
    const anonymousId = event.headers.anonymousid;
    console.log('anonymousId - ', anonymousId);

    if (token) {
      const match = token.match(/^Bearer (.*)$/);
      if (!match || match.length < 2) {
        throw callback(null, generatePolicy(null, 'Deny', event.routeArn));
      }

      // verifies secret and checks exp
      jwt.verify(match[1], process.env.CLIENT_SECRET_KEY, (err, decoded) => {
        if (err) {
          console.log('Verify token error: ', err.message);
          return callback(null, generatePolicy(null, 'Deny', event.routeArn));
        }

        // if everything is good, save to request for use in other routes
        return callback(null, generatePolicy(decoded.email, 'Allow', event.routeArn));
      });
    }
    if (!anonymousId) {
      return callback(null, generatePolicy(null, 'Deny', event.routeArn));
    }

    return callback(null, generatePolicy(anonymousId, 'Allow', event.routeArn));
  } catch (err) {
    console.log('CatchBlock :: Verify token error: ', err.message);
  }
};
