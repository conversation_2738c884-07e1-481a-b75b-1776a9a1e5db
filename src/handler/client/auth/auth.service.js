const {
  BadRequestError,
  ResourceNotFoundError,
  UnauthorizationError,
} = require('../../../common/exceptions');
const {
  EMAIL_EXIST,
  PHONE_EXIST,
  INVALID_CREDENTIALS,
  EMAIL_NOT_CONFIRMED,
  ACCOUNT_DELETED,
  TOO_MANY_REQUEST,
} = require('../../../constants/error-code');
const {UserModel} = require('../../../models/UserModel');
const {sendEmail} = require('../../../utils/email-util');
const UserUtil = require('./util');
const UserStatus = require('../../../common/types/user-status');
const {generateStudentCertificate} = require('../../../utils/file-generate');
const {
  getUserByEmail,
  getUserByPhone,
  createUser,
  updateUser,
} = require('../../../repository/user');
const {v4} = require('../../../utils/uuid.util');

const createTokens = async (email) => {
  const token = UserUtil.signToken(email);
  const refreshToken = UserUtil.createRefreshToken(email);
  await updateUser({PK: email, refreshToken});
  return {token, refreshToken};
};

const checkPhoneExist = async (phone, currentUser) => {
  if (!phone) return;
  if (currentUser?.phone === phone) return;
  const user = await getUserByPhone(phone);
  if (user) {
    throw new BadRequestError({
      code: PHONE_EXIST,
      message: 'Số điện thoại đã được sử dụng',
    });
  }
};

const verifyUserLogin = async ({email, password}) => {
  let user = await getUserByEmail(email);
  if (!user) user = await getUserByPhone(email);
  const isValidPassword = await UserUtil.comparePassword(password, user?.password);
  if (!user || !isValidPassword) {
    throw new BadRequestError({
      code: INVALID_CREDENTIALS,
      message: 'Thông tin đăng nhập không chính xác',
    });
  }

  if (!user.verified) {
    throw new BadRequestError({
      code: EMAIL_NOT_CONFIRMED,
      message: 'Tài khoản chưa được xác thực',
    });
  }

  if (user.delFlg) {
    throw new BadRequestError({
      code: ACCOUNT_DELETED,
      message: 'Tài khoản không tồn tại',
    });
  }

  const {token, refreshToken} = await createTokens(user.PK);

  return {
    token,
    refreshToken,
    user: {
      email: user.PK,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    },
  };
};

const register = async ({email, phone, password}) => {
  try {
    const existingUser = await getUserByEmail(email);
    await checkPhoneExist(phone, existingUser);
    const hashPassword = await UserUtil.hashPassword(password);
    if (!existingUser) {
      const data = {
        PK: email,
        email: email,
        password: hashPassword,
        identityId: v4(),
      };
      if (phone) data.phone = phone;
      return UserModel.create(data);
    }

    if (existingUser.verified)
      throw new BadRequestError({
        code: EMAIL_EXIST,
        message: 'Email đã được sử dụng',
      });

    existingUser.password = hashPassword;
    existingUser.save();
    return existingUser;
  } catch (error) {
    throw error;
  }
};

const sendOTP = async (email) => {
  const user = await getUserByEmail(email);
  if (!user) {
    throw new BadRequestError({
      code: ACCOUNT_DELETED,
      message: 'Tài khoản không tồn tại',
    });
  }
  const now = Math.floor(Date.now() / 1000);
  const otpExistTime = 15 * 60; // 15 minutes
  const waitTime = 60; // Thời gian chờ giữa 2 lần gửi OTP
  if (now < user.otpExpiredTime && user.otpExpiredTime - now > otpExistTime - waitTime) {
    throw new BadRequestError({
      code: TOO_MANY_REQUEST,
      message: 'Vui lòng đợi ít nhất 1 phút trước khi yêu cầu lại',
    });
  }
  try {
    const otpSecret = Math.floor(1000 + Math.random() * 9000);
    user.otpSecret = otpSecret.toString();
    user.otpExpiredTime = Math.floor(Date.now() / 1000) + otpExistTime;
    user.save();

    // send mail to user
    const context = {otp: otpSecret};
    await sendEmail({
      to: email,
      subject: `[TRE CENTER] Xác minh tài khoản`,
      context,
      template: 'send-otp',
    });
  } catch (error) {
    throw error;
  }
};

const verifyOTP = async (email, otp) => {
  try {
    const user = await getUserByEmail(email);
    let token = null;

    if (user.otpSecret === otp) {
      user.otpSecret = '';
      user.verified = true;
      user.save();
      token = UserUtil.signToken(email);
    }

    return {
      token,
    };
  } catch (error) {
    throw error;
  }
};

const updateUserInfo = async (body) => {
  try {
    const {
      email,
      name,
      gender,
      birthday,
      icCard,
      university,
      major,
      schoolYear,
      studentId,
      profileImg,
      address,
    } = body;
    const user = await getUserByEmail(email);
    Object.assign(user, {
      name,
      birthday,
      icCard,
      ...(gender && {gender}),
      ...(university && {university}),
      ...(major && {major}),
      ...(schoolYear && {schoolYear}),
      ...(studentId && {studentId}),
      ...(profileImg && {profileImg}),
      ...(address && {address}),
    });

    let isFirstTimeUpdate = false;

    if (!user.status || user.status === UserStatus.CREATED) {
      isFirstTimeUpdate = true;
      user.status = UserStatus.UNVERIFIED;
    }

    await user.save();
    if (isFirstTimeUpdate) await sendCert(email);
  } catch (error) {
    throw error;
  }
};

const getUserInfo = async (email) => {
  const user = await getUserByEmail(email);
  if (!user) {
    throw new UnauthorizationError();
  }
  delete user.password;
  delete user.refreshToken;
  delete user.notificationTokens;
  delete user.otpSecret;
  return user;
};

const verifyThirdPartyLogin = async (user) => {
  const userEmail = user.email;
  let res = await getUserByEmail(userEmail);
  if (!res) {
    res = await createUser({
      ...user,
      email: userEmail,
      PK: userEmail,
      verified: true,
      status: UserStatus.CREATED,
      role: 'USER',
    });
  }

  const {token, refreshToken} = await createTokens(userEmail);

  return {
    token,
    refreshToken,
    user: res,
  };
};

const verifyStudent = async ({
  email,
  name,
  birthday,
  gender,
  icCard,
  phone,
  university,
  major,
  schoolYear,
  studentId,
  schoolCertificate,
}) => {
  const user = await getUserByEmail(email);
  await checkPhoneExist(phone, user);
  Object.assign(user, {
    name,
    birthday,
    icCard,
    gender,
    university,
    major,
    schoolYear,
    studentId,
    phone,
    schoolCertificate,
  });
  if (user.status !== UserStatus.VERIFIED) user.status = UserStatus.SUBMITTED;
  await user.save();
};

const resetPassword = async ({email, redirectUrl}) => {
  const user = await getUserByEmail(email);
  if (!user) {
    throw new ResourceNotFoundError('Tài khoản không tồn tại');
  }
  if (!user.verified) {
    throw new BadRequestError({
      code: EMAIL_NOT_CONFIRMED,
      message: 'Email chưa được xác thực',
    });
  }
  // gen one time token
  const token = UserUtil.generateEmailVerifyToken(email);
  const url = `${redirectUrl}&token=${token}`;

  user.resetPasswordToken = token;
  await user.save();

  await sendEmail({
    to: email,
    subject: `[TRECENTER] Đặt lại mật khẩu`,
    context: {url},
    template: 'reset-password',
  });
};

const confirmResetPassword = async ({email, newPassword}) => {
  // const {email} = await UserUtil.verifyEmailToken(resetToken);
  const user = await getUserByEmail(email);

  // if (!user?.resetPasswordToken) {
  //   throw new BadRequestError({
  //     code: VERIFY_TOKEN_INVALID,
  //     message: 'Link đã hết hạn hoặc đã được sử dụng',
  //   });
  // }

  const hashPassword = await UserUtil.hashPassword(newPassword);
  user.password = hashPassword;
  // delete user.resetPasswordToken;
  await user.save();
};

const changePassword = async ({userEmail, oldPassword, newPassword}) => {
  const user = await getUserByEmail(userEmail);
  const isValidPassword = await UserUtil.comparePassword(oldPassword, user?.password);
  if (!isValidPassword) {
    throw new BadRequestError({
      code: INVALID_CREDENTIALS,
      message: 'Mật khẩu cũ không chính xác',
    });
  }
  const hashPassword = await UserUtil.hashPassword(newPassword);
  user.password = hashPassword;
  await user.save();
};

const deleteUser = async (email) => {
  const user = await getUserByEmail(email);
  user.delFlg = true;
  await user.save();
};

const sendCert = async (email) => {
  try {
    const user = await getUserByEmail(email);
    const response = await generateStudentCertificate(user);
    if (response) {
      await sendEmail({
        to: email,
        subject: `[TRE CENTER] Xác nhận thông tin`,
        template: 'verify-info',
        attachments: [
          {
            filename: response.filename,
            path: response.filepath,
          },
        ],
      });
    }
  } catch (error) {
    throw error;
  }
};

const getToken = async (token) => {
  const decoded = UserUtil.verifyRefreshToken(token);
  if (!decoded) {
    throw new BadRequestError({
      code: INVALID_CREDENTIALS,
      message: 'Token không hợp lệ',
    });
  }
  const {email} = decoded;
  const user = await getUserByEmail(email);
  if (!user || user.refreshToken !== token) {
    throw new BadRequestError({
      code: INVALID_CREDENTIALS,
      message: 'Token không hợp lệ',
    });
  }
  return await createTokens(email);
};

const AuthService = {
  register,
  verifyUserLogin,
  getUserInfo,
  resetPassword,
  confirmResetPassword,
  verifyThirdPartyLogin,
  changePassword,
  verifyOTP,
  verifyStudent,
  sendOTP,
  updateUserInfo,
  deleteUser,
  sendCert,
  getToken,
};
module.exports = AuthService;
