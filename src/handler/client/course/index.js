const ResponseBuilder = require('../../../utils/response-builder.util');
const {errorHandler} = require('../../../utils/error-handler.util');
const {CourseEndPoint} = require('./const');
const {CONFIG_MODEL_KEY} = require('../../../common/types/config.type');
const {getContentByConfig} = require('../../../utils/config.util');
const {BlogModel} = require('../../../models/BlogModel');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      case CourseEndPoint.PAGE:
        data = await getContentByConfig(CONFIG_MODEL_KEY.COURSE_PAGE);
        break;
      case CourseEndPoint.GET_COURSE_DETAIL:
        data = await getCourseDetail(event);
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (err) {
    return errorHandler(err);
  }
};

const getCourseDetail = async (event) => {
  const courseId = event.pathParameters.courseId;
  return await BlogModel.get({
    PK: 'COURSE',
    SK: courseId,
  });
};
