const {ResourceNotFoundError} = require('../../../common/exceptions');
const TicketEventCategory = require('../../../common/types/ticketing-category.type');
const {EventModel} = require('../../../models/EventModel');
const {errorHandler} = require('../../../utils/error-handler.util');
const ResponseBuilder = require('../../../utils/response-builder.util');
const {EventEndPoint} = require('./const');
const {TicketClassServices} = require('./ticket-class.service');
const seatmapModel = require('../../../../drizzle/models/seatmap');
const ticketModel = require('../../../../drizzle/models/ticket');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      case EventEndPoint.EVENT_DETAIL:
        data = await getEventDetail(event);
        break;
      case EventEndPoint.EVENT_CONFIGURE:
        data = await getEventConfigure(event);
        break;
      case EventEndPoint.GET_TICKET_CLASS:
        data = await getTicketClass(event);
        break;
      case EventEndPoint.GET_EVENT_CATEGORIES:
        data = Object.values(TicketEventCategory).map((category) => ({
          key: category,
          text: TicketEventCategory.getText(category),
        }));
        break;
      case EventEndPoint.GET_SEATMAP:
        data = await getSeatMap(event);
        break;
      case EventEndPoint.GET_TICKETS:
        data = await getTickets(event);
        break;
      case EventEndPoint.GET_TICKETS_ZONE:
        data = await getTicketsZone(event);
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (error) {
    return errorHandler(error);
  }
};

async function getEventDetail(event) {
  const eventId = event.pathParameters.eventId;

  const eventInfo = await EventModel.get({
    PK: eventId,
  });

  if (!eventInfo) {
    throw new ResourceNotFoundError('Sự kiện không tồn tại');
  }

  const data = {
    ...eventInfo,
  };

  if (eventInfo.categories?.includes('concert')) {
    const query = await EventModel.query('concertId').eq(eventId).using('concertIdIndex').exec();
    data.workshops = query.toJSON();
  }

  return data;
}

async function getEventConfigure(event) {
  const pathName = event.pathParameters.pathName;
  const [eventInfo] = await EventModel.query('pathName')
    .eq(pathName)
    .using('pathNameIndex')
    .attributes(['PK', 'logo', 'socialShared', 'hostedBy', 'thumbnail', 'eventName'])
    .exec();

  if (!eventInfo) {
    throw new ResourceNotFoundError('Sự kiện không tồn tại');
  }

  if (!eventInfo.socialShared) {
    eventInfo.socialShared = {
      thumbnail: eventInfo.thumbnail,
      description: `Tham gia ${eventInfo.eventName}`,
    };
  }
  delete eventInfo.thumbnail;
  delete eventInfo.eventName;

  return eventInfo;
}

async function getTicketClass(event) {
  return await TicketClassServices.getTicketClasses(event);
}

async function getSeatMap(event) {
  const pathName = event.pathParameters.pathName;
  const calendarId = event.queryStringParameters.calendarId;
  const [eventInfo] = await EventModel.query('pathName').eq(pathName).using('pathNameIndex').exec();
  return await seatmapModel.getSeatmap({calendarId, eventId: eventInfo.PK});
}

async function getTickets(event) {
  const pathName = event.pathParameters.pathName;
  const calendarId = event.queryStringParameters.calendarId;
  const [eventInfo] = await EventModel.query('pathName').eq(pathName).using('pathNameIndex').exec();
  return await ticketModel.getAllTicketsClient({
    calendarId,
    eventId: eventInfo.PK,
  });
}

async function getTicketsZone(event) {
  const pathName = event.pathParameters.pathName;
  const calendarId = event.queryStringParameters.calendarId;
  const [eventInfo] = await EventModel.query('pathName').eq(pathName).using('pathNameIndex').exec();
  return await ticketModel.getAllZonesClient({
    eventId: eventInfo.PK,
    calendarId,
  });
}
