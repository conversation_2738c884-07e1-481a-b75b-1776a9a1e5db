const moment = require('moment');
const EventStatus = require('../../../common/types/event-status');
const {EventModel} = require('../../../models/EventModel');

module.exports.handler = async () => {
  try {
    await Promise.all([closeEvents()]);
  } catch (error) {
    console.error(error.message);
  }
};

async function closeEvents() {
  let events = await EventModel.query('status').eq(EventStatus.OPEN).using('statusIndex').exec();

  events = events.toJSON();
  console.log('Number of event is opening: ', events.length);

  let eventPromises = [];
  for (const event of events) {
    if (!event.ticketCloseTime) {
      continue;
    }
    const isEnded = moment().isAfter(moment(event.ticketCloseTime));
    if (isEnded) {
      console.log('Event %s, close time %s', event.eventName, event.ticketCloseTime);
      eventPromises.push(updateEventStatus(event.PK, EventStatus.CLOSE));
    }

    if (eventPromises.length === 25) {
      await Promise.all(eventPromises);
      eventPromises = [];
    }
  }
  if (!!eventPromises.length) {
    await Promise.all(eventPromises);
  }
}

async function updateEventStatus(eventId, status) {
  await EventModel.update({PK: eventId}, {status});
}
