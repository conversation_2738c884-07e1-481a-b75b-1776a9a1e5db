const {orderBy} = require('lodash');
const {EventModel} = require('../../../models/EventModel');
const {FanpassModel} = require('../../../models/FanpassModel');
const {OrderModel} = require('../../../models/OrderModel');
const ProductTypes = require('../../../common/types/product-type');
const {EventTicketsModel} = require('../../../models/EventTIckets');
const {UserModel} = require('../../../models/UserModel');
const {UserFanpassModel} = require('../../../models/UserFanpassModel');
const {ResourceNotFoundError, UnauthorizationError} = require('../../../common/exceptions');

const getOrderByUserId = async (userId) => {
  const results = await OrderModel.query('userId')
    .eq(userId)
    .using('userIdIndex')
    .where('status')
    .eq('SUCCESS')
    .exec();

  return orderBy(results.toJSON(), ['paymentTime'], ['desc']);
};

const getTicketByOrderId = async (userId, orderId) => {
  const [order] = await OrderModel.query('orderId')
    .eq(orderId)
    .using('orderIdIndex')
    .where('userId')
    .eq(userId)
    .exec()
    .then((res) => res.toJSON());

  const eventTickets = await EventTicketsModel.query('orderId')
    .eq(orderId)
    .using('orderIdIndex')
    .where('userId')
    .eq(userId)
    .exec()
    .then((res) => res.toJSON());

  const ticketInfo = eventTickets.map((t) => ({
    ...t,
    qrCodeUrl: `https://quickchart.io/chart?cht=qr&chld=H%7C1&chs=200x200&chl=${encodeURIComponent(
      JSON.stringify({
        refId: t.refId,
        ticketId: t.ticketId,
        orderId: t.orderId,
        productId: t.eventId,
      }),
    )}`,
  }));

  return {
    ...order,
    tickets: orderBy(ticketInfo, ['paymentTime'], ['desc']),
  };
};

const getOrderTickets = async (userId) => {
  const orders = await getOrderByUserId(userId);
  if (!orders?.length) {
    return [];
  }

  const tickets = [];
  for (const order of orders) {
    const productId = order.PK;
    const event = await EventModel.get({PK: productId});
    const eventTickets = await EventTicketsModel.query('eventId')
      .eq(productId)
      .where('userId')
      .eq(userId)
      .exec()
      .then((res) => res.toJSON());

    const ticketInfo = eventTickets.map((t) => ({
      ...t,
      qrCodeUrl: `https://quickchart.io/chart?cht=qr&chld=H%7C1&chs=200x200&chl=${encodeURIComponent(
        JSON.stringify({
          refId: t.refId,
          ticketId: t.ticketId,
          orderId: t.orderId,
          productId: productId,
        }),
      )}`,
    }));

    tickets.push({
      orderId: order.orderId,
      ...event,
      tickets: orderBy(ticketInfo, ['paymentTime'], ['desc']),
    });
  }

  return tickets;
};

const getOrderFanpass = async (userId) => {
  const orders = await getOrderByUserId(userId);
  if (!orders?.length) {
    return [];
  }

  const productIds = [...new Set(orders.map((o) => o.PK))];
  const getFanpassInfo = async (eventId) =>
    await FanpassModel.get(
      {id: eventId},
      {
        attributes: ['id', 'thumbnail', 'fanpassName', 'benefit', 'pathName'],
      },
    );

  const myFanpass = [];
  for (const productId of productIds) {
    const fanpass = await getFanpassInfo(productId);
    const orderTickets = orders.filter((o) => o.PK === productId);
    const ticketInfo = orderTickets
      .map((oTicket) =>
        oTicket.tickets
          .map((t) =>
            Array(t.quantity)
              .fill(0)
              .map(() => Object.assign({}, {...t, quantity: undefined, totalAmount: undefined})),
          )
          .flat()
          .map((t) => ({
            ...t,
            orderId: oTicket.orderId,
            paymentTime: oTicket.paymentTime,
            orderStatus: oTicket.status,
          }))
          .sort((a, b) => new Date(b.paymentTime) - new Date(a.paymentTime)),
      )
      .flat();

    myFanpass.push({
      ...fanpass,
      benefit: fanpass.benefit.values,
      tickets: orderBy(ticketInfo, ['paymentTime'], ['desc']),
    });
  }

  return myFanpass;
};

const updateShipmentAddress = async (email, address) => {
  await UserModel.update({PK: email}, {$SET: {shipmentPickAddress: address}});
};

const getMerchantOrders = async (userId) => {
  const orders = await getOrderByUserId(userId, ProductTypes.P_MERCHANDISE);
  if (!orders?.length) {
    return [];
  }

  return orders.map((order) => ({
    orderId: order.orderId,
    createdAt: order.createdAt,
    paymentStatus: 'Thanh toán khi nhận hàng',
    deliverStatus: 'Đã tiếp nhận',
    shipmentInfo: order.shipmentInfo,
    transportInfo: order.transportInfo || {},
    products: order.products,
    amount: order.amount,
  }));
};

const getFanpassBenefits = async (userId, pathName) => {
  const [fanpassInfo] = await FanpassModel.query('pathName')
    .eq(pathName)
    .using('pathNameIndex')
    .exec();
  if (!fanpassInfo) {
    throw new ResourceNotFoundError('Fanpass không tồn tại');
  }

  const result = await UserFanpassModel.query('userId')
    .eq(userId)
    .where('fanpassId')
    .eq(fanpassInfo.id)
    .where('status')
    .eq('ACTIVE')
    .attributes(['refId'])
    .exec();

  if (!result.count) {
    return [];
  }

  return fanpassInfo.membershipBenefits || [];
};

const registerNotification = async (userId, data) => {
  const user = await UserModel.get({PK: userId});
  if (!user) {
    throw new ResourceNotFoundError('Người dùng không tồn tại');
  }

  if (user.notificationTokens) {
    const deviceToken = user.notificationTokens.find((token) => token.deviceId === data.deviceId);
    if (deviceToken) {
      deviceToken.token = data.token;
    } else {
      user.notificationTokens.push(data);
    }
  } else {
    user.notificationTokens = [data];
  }
  await user.save();
};

const logout = async (userId, deviceId) => {
  const user = await UserModel.get({PK: userId});
  if (!user) {
    throw new UnauthorizationError();
  }
  // Filter out the device token with the specified deviceId
  user.notificationTokens = user.notificationTokens.filter((token) => token.deviceId !== deviceId);

  // Save the updated user object
  await user.save();
};

module.exports = {
  getOrderTickets,
  getOrderFanpass,
  updateShipmentAddress,
  getMerchantOrders,
  getFanpassBenefits,
  getTicketByOrderId,
  registerNotification,
  logout,
};
