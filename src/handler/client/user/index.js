const {errorHandler} = require('../../../utils/error-handler.util');
const ResponseBuilder = require('../../../utils/response-builder.util');
const ValidatorUtil = require('../../../utils/request-validator.util');
const {UserEndPoint} = require('./const');
const UserService = require('./user.service');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      case UserEndPoint.MY_TICKETS:
        data = await getMyTickets(event);
        break;
      case UserEndPoint.TICKET_DETAIL:
        data = await getTicketDetail(event);
        break;
      case UserEndPoint.MY_FANPASS:
        data = await getMyFanpass(event);
        break;
      case UserEndPoint.UPDATE_SHIPMENT_ADDRESS:
        data = await updateShipmentAddress(event);
        break;
      case UserEndPoint.GET_MERCHANT_ORDERS:
        data = await getOrders(event);
        break;
      case UserEndPoint.GET_FANPASS_MEMBERSHIP_BENEFITS:
        data = await getFanpassBenefits(event);
        break;
      case UserEndPoint.REGISTER_NOTIFICATION:
        data = await registerNotification(event);
        break;
      case UserEndPoint.LOGOUT:
        data = await logout(event);
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (error) {
    return errorHandler(error);
  }
};

async function getMyTickets(event) {
  const userEmail = event.requestContext.authorizer.lambda.principalId;
  return await UserService.getOrderTickets(userEmail);
}

async function getTicketDetail(event) {
  const userEmail = event.requestContext.authorizer.lambda.principalId;
  const orderId = event.pathParameters.orderId;
  return await UserService.getTicketByOrderId(userEmail, orderId);
}

async function getMyFanpass(event) {
  const userEmail = event.requestContext.authorizer.lambda.principalId;
  return await UserService.getOrderFanpass(userEmail);
}

async function updateShipmentAddress(event) {
  const userEmail = event.requestContext.authorizer.lambda.principalId;
  const orderFields = ['name', 'phoneNumber', 'province', 'district'];
  await ValidatorUtil.requireParams(event.body, orderFields);

  await UserService.updateShipmentAddress(userEmail, JSON.parse(event.body));
}

async function getOrders(event) {
  const userEmail = event.requestContext.authorizer.lambda.principalId;
  return await UserService.getMerchantOrders(userEmail);
}

async function getFanpassBenefits(event) {
  const userEmail = event.requestContext.authorizer.lambda.principalId;
  const pathName = event.pathParameters.pathName;

  return await UserService.getFanpassBenefits(userEmail, pathName);
}

async function registerNotification(event) {
  const requiredFields = ['token', 'deviceId'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const userId = event.requestContext.authorizer.lambda.principalId;
  await UserService.registerNotification(userId, JSON.parse(event.body));

  return ResponseBuilder.ok({successMessage: 'Đăng ký thông báo thành công'});
}

async function logout(event) {
  const requiredFields = ['deviceId'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const userId = event.requestContext.authorizer.lambda.principalId;
  const {deviceId} = JSON.parse(event.body);
  await UserService.logout(userId, deviceId);
}
