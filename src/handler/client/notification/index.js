const {errorHandler} = require('../../../utils/error-handler.util');
const ResponseBuilder = require('../../../utils/response-builder.util');
const {NotificationEndpoint} = require('./const');
const {NotificationService} = require('./notification.service');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      case NotificationEndpoint.GET_NOTIFICATIONS:
        data = await getNotifications(event);
        break;
      case NotificationEndpoint.GET_NOTIFICATION:
        data = await getNotification(event);
        break;
      case NotificationEndpoint.MARK_ALL_AS_READ:
        data = await markAllAsRead(event);
        break;
      case NotificationEndpoint.MARK_AS_READ:
        data = await markAsRead(event);
        break;
      case NotificationEndpoint.DELETE_NOTIFICATION:
        data = await deleteNotification(event);
        break;
      case NotificationEndpoint.DELETE_ALL_NOTIFICATIONS:
        data = await deleteAllNotifications(event);
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (error) {
    return errorHandler(error);
  }
};

async function getNotifications(event) {
  const userEmail = event.requestContext.authorizer.lambda.principalId;
  return NotificationService.getNotifications(userEmail);
}

async function getNotification(event) {
  const userEmail = event.requestContext.authorizer.lambda.principalId;
  return NotificationService.getNotification(userEmail, event.pathParameters);
}

async function markAllAsRead(event) {
  const userEmail = event.requestContext.authorizer.lambda.principalId;
  return NotificationService.markAllAsRead(userEmail);
}

async function markAsRead(event) {
  const userEmail = event.requestContext.authorizer.lambda.principalId;
  return NotificationService.markAsRead(userEmail, event.pathParameters);
}

async function deleteNotification(event) {
  const userEmail = event.requestContext.authorizer.lambda.principalId;
  return NotificationService.deleteNotification(userEmail, event.pathParameters);
}

async function deleteAllNotifications(event) {
  const userEmail = event.requestContext.authorizer.lambda.principalId;
  return NotificationService.deleteAllNotifications(userEmail);
}
