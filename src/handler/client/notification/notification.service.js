const {NotificationModel} = require('../../../models/NotificationModel');
const {ResourceNotFoundError} = require('../../../common/exceptions');

const getNotifications = async (userId) => {
  // <PERSON><PERSON><PERSON> danh sách thông báo chưa bị xóa
  const notifications = await NotificationModel.query('userId')
    .eq(userId)
    .using('userIdIndex')
    .sort('descending')
    .where('deleteFlag')
    .eq(false)
    .all()
    .exec();

  return notifications.toJSON();
};

const getNotification = async (userId, params) => {
  const {notificationId} = params;
  // L<PERSON>y thông báo cụ thể
  const notification = await NotificationModel.get({id: notificationId});

  if (notification?.userId !== userId || notification.deleteFlag) {
    throw new ResourceNotFoundError('Thông báo không tồn tại');
  }

  return notification.toJSON();
};

const markAllAsRead = async (userId) => {
  // Đ<PERSON>h dấu tất cả thông báo là đã đọc
  const notifications = await NotificationModel.query('userId')
    .eq(userId)
    .where('deleteFlag')
    .eq(false)
    .where('isRead')
    .eq(false)
    .all()
    .exec();

  for (const notification of notifications) {
    notification.isRead = true;
    await notification.save();
  }

  return {successMessage: 'Tất cả thông báo đã được đánh dấu là đã đọc'};
};

const markAsRead = async (userId, params) => {
  const {notificationId} = params;

  // Đánh dấu thông báo cụ thể là đã đọc
  const notification = await NotificationModel.get({id: notificationId});

  if (notification?.userId !== userId || notification.deleteFlag) {
    throw new ResourceNotFoundError('Thông báo không tồn tại');
  }

  notification.isRead = true;
  await notification.save();

  return {successMessage: 'Thông báo đã được đánh dấu là đã đọc'};
};

const deleteNotification = async (userId, params) => {
  const {notificationId} = params;

  // Xóa thông báo cụ thể (đặt cờ deleteFlag = true)
  const notification = await NotificationModel.get({id: notificationId});

  if (notification?.userId !== userId || notification.deleteFlag) {
    throw new ResourceNotFoundError('Thông báo không tồn tại');
  }

  notification.deleteFlag = true;
  await notification.save();

  return {successMessage: 'Thông báo đã được xóa'};
};

const deleteAllNotifications = async (userId) => {
  // Xóa tất cả thông báo (đặt cờ deleteFlag = true)
  const notifications = await NotificationModel.query('userId')
    .eq(userId)
    .where('deleteFlag')
    .eq(false)
    .exec();

  for (const notification of notifications) {
    notification.deleteFlag = true;
    await notification.save();
  }

  return {successMessage: 'Tất cả thông báo đã được xóa'};
};

module.exports.NotificationService = {
  getNotifications,
  getNotification,
  markAllAsRead,
  markAsRead,
  deleteNotification,
  deleteAllNotifications,
};
