const ResponseBuilder = require('../../../utils/response-builder.util');
const {CardRegistrationModel} = require('../../../models/CardRegistration');
const ValidatorUtil = require('../../../utils/request-validator.util');
const {errorHandler} = require('../../../utils/error-handler.util');
const {v4: uuidv4} = require('../../../utils/uuid.util');

module.exports.handler = async (event) => {
  const requiredFields = [
    'name',
    'icCard',
    'email',
    'phone',
    'studentId',
    'birthday',
    'university',
    'major',
    'schoolYear',
    'icCardImgFront',
    'icCardImgBack',
    'studentCardFront',
    'studentCardBack',
    'schoolCertificate',
  ];
  await ValidatorUtil.requireParams(event.body, requiredFields);
  const body = JSON.parse(event.body);
  try {
    const data = await CardRegistrationModel.create({
      PK: uuidv4(),
      ...body,
    });
    return ResponseBuilder.ok(data);
  } catch (err) {
    return errorHandler(err);
  }
};
