const {orderBy, pick} = require('lodash');
const {PaymentMethodModel} = require('../../../models/PaymentMethodModel');
const {EventModel} = require('../../../models/EventModel');
const {TenantModel} = require('../../../models/TenantModel');
const PaymentGatewayTypes = require('../../../common/types/payment-gateway.type');

const getPaymentMethods = async (eventId) => {
  const results = await PaymentMethodModel.scan().exec();
  let methods = orderBy(results.toJSON(), ['order'], ['asc']);

  if (!eventId) {
    return methods
      .filter((method) =>
        [
          PaymentGatewayTypes.VNPAY,
          PaymentGatewayTypes.MOMO,
          PaymentGatewayTypes.ZALOPAY,
          PaymentGatewayTypes.ZALOPAY_VIETQR,
          PaymentGatewayTypes.ZALOPAY_CC,
          PaymentGatewayTypes.INTCARD,
        ].includes(method.type),
      )
      .map((method) => pick(method, ['type', 'title', 'subTitle', 'thumbnailImage']));
  }

  const event = await EventModel.get({PK: eventId});
  const tenant = await TenantModel.get({tenantId: event.tenantId});
  const eventTicket = tenant.products.find((item) => item.product === 'ticketing');

  if (eventTicket?.paymentGateway) {
    methods = methods.filter((method) =>
      eventTicket.paymentGateway.some((gw) => method.type === gw),
    );
  }

  return methods.map((method) => pick(method, ['type', 'title', 'subTitle', 'thumbnailImage']));
};

module.exports.PaymentMethodService = {getPaymentMethods};
