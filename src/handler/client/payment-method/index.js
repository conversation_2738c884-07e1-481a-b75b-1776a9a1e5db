const {errorHandler} = require('../../../utils/error-handler.util');
const ResponseBuilder = require('../../../utils/response-builder.util');
const {PaymentMethodEndPoint} = require('./const');
const {PaymentMethodService} = require('./payment-method.service');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      case PaymentMethodEndPoint.PAYMENT_METHOD:
        data = await getPaymentMethods();
        break;
      case PaymentMethodEndPoint.PAYMENT_GATEWAY:
        data = await getPaymentGateways(event);
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (error) {
    return errorHandler(error);
  }
};

async function getPaymentGateways(event) {
  const eventId = event.queryStringParameters?.eventId;
  return await PaymentMethodService.getPaymentMethods(eventId);
}

async function getPaymentMethods() {
  return await PaymentMethodService.getPaymentMethods();
}
