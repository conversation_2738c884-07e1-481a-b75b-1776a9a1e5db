const moment = require('moment');
const {OrderModel} = require('../../models/OrderModel');
const {sendEmail} = require('../../utils/email-util');
const {FanpassModel} = require('../../models/FanpassModel');
const {EventModel} = require('../../models/EventModel');
const {OrderStatus} = require('./const');
const {TicketStorageModel} = require('../../models/TicketStorageModel');
const ProductTypes = require('../../common/types/product-type');
const {v4} = require('../../utils/uuid.util');
const {EventTicketsModel} = require('../../models/EventTIckets');
const {UserFanpassModel} = require('../../models/UserFanpassModel');
const {OrderUtil} = require('../../utils/order.util');
const CollaborationType = require('../../common/types/collaboration.type');
const {CartModel} = require('../../models/CartModel');
const {GhtkService} = require('../client/order/services/ghtk.service');
const MerchantProductType = require('../../common/types/merchant-product.type');
const {AutoAssignedSeatModel} = require('../../models/AutoAssignedSeatModel');
const {
  updateTicketStatusBySuccessOrder,
  resolveHoldTicketByOrder,
  getTicketsByOrderId,
} = require('../../../drizzle/models/ticket');
const {getTicketClassesByIds} = require('../../../drizzle/models/ticket-class');
const PaymentGatewayTypes = require('../../common/types/payment-gateway.type');

exports.handler = async function (event, context) {
  const batchItemFailures = [];
  // SQS may invoke with multiple messages
  for (const message of event.Records) {
    try {
      console.log('Update Order Status from', message.messageId);
      console.log('Record: ', message.body);
      const bodyData = JSON.parse(message.body);
      await updateOrderStatus(bodyData);
    } catch (error) {
      console.error(error);
      batchItemFailures.push({ItemIdentifier: message.messageId});
    }
  }
  return {
    statusCode: 200,
    body: JSON.stringify({batchItemFailures}),
  };
};

async function updateOrderStatus(data) {
  const now = new Date();
  const order = await OrderModel.get({
    PK: data.productId,
    orderId: data.orderId,
  });
  order.status = data.paymentStatus;
  order.transId = data.transId;
  order.message = data.message;
  order.paymentTime = now;

  await order.save();
  console.log('Update order status successfully [', data.orderId, ']');

  if (data.paymentStatus !== OrderStatus.SUCCESS) {
    if (order.extraData?.version === 'v2') {
      await resolveHoldTicketByOrder(data.orderId);
    }
    return;
  }

  if (order.productType === ProductTypes.P_MERCHANDISE) {
    const cartKey = {userId: order.userId, merchantId: order.PK};
    await CartModel.delete(cartKey);
    console.log('Cart is removed - ', cartKey);
    // đăng đơn hàng sang đơn vị vận chuyển
    const ghtkService = new GhtkService(order.PK);
    const result = await ghtkService.postOrder(order.toJSON());

    order.transportInfo = result;
    await order.save();

    await sendMerchantInvoice(order.toJSON());
    return;
  }

  if (order.extraData?.version === 'v2') {
    await processV2(order, data.productId);
    return;
  }

  // resolve ticket holding
  const productType = order.extraData.productType;
  let tickets = [];
  if (ProductTypes.isEvent(productType)) {
    await resolveHoldingTicket(data.productId, order);
    // save ticket by event
    tickets = await saveEventTickets(order);
  } else if (ProductTypes.isFanpass(productType)) {
    tickets = await saveUserFanpass(order);
  }
  // send invoice
  order.flatTickets = tickets;
  await sendInvoice(order, data.productId, now);
}

async function processV2(order, eventId) {
  const ticketClassIds = new Set();
  await updateTicketStatusBySuccessOrder(order);
  const tickets = await getTicketsByOrderId(order.orderId);
  tickets.forEach((v) => {
    ticketClassIds.add(v.ticketClassId);
  });

  const eventInfo = await EventModel.get({PK: eventId});
  const ticketClasses = await getTicketClassesByIds([...ticketClassIds]);
  order.flatTickets = tickets.map((ticket) => {
    const ticketClass = ticketClasses.find((v) => v.id === ticket.ticketClassId);
    return {
      ticketId: ticket.id,
      ticketType: ticket.ticketClassName,
      refId: ticket.generateCode,
      calendarId: ticket.calendarId,
      totalAmount: ticketClass.finalPriceVn || 0,
      deliveryInfo: {
        seatCode: `${ticket.zoneName}-${ticket.rowName}-${ticket.code}`,
      },
      ticketImage: ticketClass.prototypeUrl || eventInfo.thumbnail,
      customQr: {generateCode: ticket.generateCode},
    };
  });

  console.log(2222, order.flatTickets);

  // TODO: clone to old table: EventTickets - report
  await EventTicketsModel.batchPut(
    tickets.map((ticket) => {
      const ticketClass = ticketClasses.find((v) => v.id === ticket.ticketClassId);
      return {
        eventId,
        refId: ticket.generateCode,
        orderId: order.orderId,
        calendarId: ticket.calendarId,
        paymentGateway: order.paymentGateway,
        paymentInfo: order.paymentInfo,
        paymentTime: order.paymentTime,
        ticketDescription: ticketClass.description,
        ticketId: ticket.id,
        ticketPrice: ticketClass.finalPriceVn,
        ticketPriceInUSD: ticketClass.finalPriceUsd,
        ticketType: ticketClass.name,
        totalAmount: order.amount,
        userId: ticket.customerEmail,
        deliveryInfo: {
          seatCode: `${ticket.zoneName}-${ticket.rowName}-${ticket.code}`,
        },
        promotionApplied: order.promotionUsed
          ? {
              ...order.promotionUsed,
              percent: order.promotionUsed.discountPercent,
              percentInUSD: order.promotionUsed.discountPercentInUSD,
              discountPercent: undefined,
              discountPercentInUSD: undefined,
            }
          : undefined,
      };
    }),
  );
  await sendEventTicketInvoice(order, eventInfo, new Date());
}

async function sendInvoice(order, productId, paymentTime) {
  if (order.paymentGateway === PaymentGatewayTypes.COD) return;
  try {
    const {productType} = order.extraData;
    if (ProductTypes.isFanpass(productType)) {
      const fanpass = await FanpassModel.get({id: productId});
      await sendFanpassInvoice(order, fanpass, paymentTime);
    } else if (ProductTypes.isEvent(productType)) {
      const eventInfo = await EventModel.get({PK: productId});
      await sendEventTicketInvoice(order, eventInfo, paymentTime);
    }
  } catch (error) {
    console.error('Send invoice failed: ', error.message);
  }
}

async function sendFanpassInvoice(order, product, paymentTime) {
  process.env.TZ = 'Asia/Ho_Chi_Minh';
  const userEmail = order.paymentInfo.email;
  const payload = {
    orderId: order.orderId,
    userEmail,
    thumbnailUrl: product.thumbnail,
    benefits: product.benefit.values,
    paymentTime: moment(paymentTime).format('DD/MM/YYYY HH:mm:ss'),
    tickets: order.flatTickets.map((t) => ({
      ...t,
      totalAmount: t.totalAmount.toLocaleString(),
      quantity: 1,
      qrUrl: `https://quickchart.io/chart?cht=qr&chld=H%7C1&chs=200x200&chl=${encodeURIComponent(
        JSON.stringify({
          refId: t.refId,
          ticketId: t.ticketId,
          orderId: order.orderId,
          productId: order.PK,
        }),
      )}`,
    })),
    totalAmount: order.amount.toLocaleString(),
  };

  await sendEmail({
    to: userEmail,
    subject: `[FANIESTA] Invoice`,
    context: payload,
    template: 'fanpass-invoice',
  });
  console.log('Send fanpass invoice successfully to [%s]', order.paymentInfo.email);
}

async function sendEventTicketInvoice(order, product, paymentTime) {
  if (order.paymentGateway === PaymentGatewayTypes.COD) return;
  process.env.TZ = 'Asia/Ho_Chi_Minh';
  const userEmail = order.paymentInfo.email;
  const [calendarId] = order.flatTickets.map((item) => item.calendarId);
  const eventDate = product.eventCalendar?.find((item) => item.calendarId === calendarId);
  const payload = {
    orderId: order.orderId,
    type: 'Vé điện tử',
    customerName: order.paymentInfo.name,
    customerPhoneNumber: order.paymentInfo.phoneNumber,
    eventNote: product.note,
    userEmail: userEmail,
    thumbnailUrl: product.thumbnail,
    eventName: product.eventName,
    eventDate: eventDate ? moment(eventDate.startDate).format('DD/MM/YYYY') : '',
    paymentTime: moment(paymentTime).format('DD/MM/YYYY HH:mm:ss'),
    tickets: order.flatTickets.map((t) => ({
      ...t,
      totalAmount: t.totalAmount.toLocaleString(),
      quantity: 1,
      seatCode: t.deliveryInfo.seatCode,
      qrUrl: `https://quickchart.io/chart?cht=qr&chld=H%7C1&chs=200x200&chl=${encodeURIComponent(
        JSON.stringify(
          t.customQr || {
            refId: t.refId,
            ticketId: t.ticketId,
            orderId: order.orderId,
            productId: order.PK,
          },
        ),
      )}`,
    })),
    totalAmount: order.amount.toLocaleString(),
  };

  let subject = '[Eventista] Eventista gửi bạn thông tin vé và chỗ ngồi';
  let template = 'event-ticket-ticket-info';
  if (!CollaborationType.isEventista(product.general?.collaboration)) {
    subject = '[Eventista] Xác nhận mua vé thành công tại Eventista';
    template = 'event-ticket-invoice';
    payload.tickets = order.tickets;
  }

  await sendEmail({
    to: userEmail,
    subject,
    context: payload,
    template,
  });
  console.log('Send ticket invoice successfully to [%s]', order.paymentInfo.email);
}

async function resolveHoldingTicket(productId, order) {
  try {
    const holders = [];
    const updateTicket = async (ticket) => {
      await TicketStorageModel.update(
        {
          PK: productId,
          ticketId: `${ticket.ticketId}#${ticket.calendarId}`,
        },
        {
          $DELETE: {
            holders: [
              {
                userId: order.userId,
                orderId: order.orderId,
              },
            ],
          },
        },
      );
    };

    for (const ticket of order.tickets) {
      holders.push(updateTicket(ticket));
    }
    await Promise.all(holders);
    console.log('Holding ticket is resolved');
  } catch (error) {
    console.error('Resolve ticket holding: ', error.message);
  }
}

async function assignSeatForTicket(order, ticket) {
  const flatTickets = [];
  const {ticketId, calendarId} = ticket;
  const pk = `${order.PK}#${ticketId}#${calendarId}`;
  let autoAssignedSeat = await AutoAssignedSeatModel.get({eventId: pk});
  if (!autoAssignedSeat) {
    autoAssignedSeat = await AutoAssignedSeatModel.create({eventId: pk});
  }
  let currentSeat = autoAssignedSeat.currentSeat;
  for (let index = 0; index < ticket.quantity; index++) {
    flatTickets.push({
      eventId: order.PK,
      refId: v4(),
      orderId: order.orderId,
      ticketId: ticket.ticketId,
      ticketType: ticket.ticketType,
      ticketDescription: ticket.ticketDescription,
      ticketPrice: ticket.ticketPrice,
      totalAmount: order.amount,
      ticketPriceInUSD: ticket.ticketPriceInUSD,
      userId: order.userId,
      paymentTime: order.paymentTime,
      paymentGateway: order.paymentGateway,
      paymentInfo: order.paymentInfo,
      calendarId: ticket.calendarId,
      deliveryInfo: {
        seatCode: `${ticket.ticketType}-${++currentSeat}`,
      },
      promotionApplied: order.promotionUsed
        ? {
            ...order.promotionUsed,
            percent: order.promotionUsed.discountPercent,
            percentInUSD: order.promotionUsed.discountPercentInUSD,
            discountPercent: undefined,
            discountPercentInUSD: undefined,
          }
        : undefined,
    });
  }

  autoAssignedSeat.currentSeat = currentSeat;
  await autoAssignedSeat.save();

  return flatTickets;
}

async function saveEventTickets(order) {
  let flatTickets = [];
  for (const ticket of order.tickets) {
    const tickets = await assignSeatForTicket(order, ticket);
    flatTickets = [...flatTickets, ...tickets];
  }

  await EventTicketsModel.batchPut(flatTickets);

  return flatTickets;
}

async function saveUserFanpass(order) {
  const flatFanpass = order.tickets
    .map((t) =>
      Array(t.quantity)
        .fill(0)
        .map(() => Object.assign({}, {...t})),
    )
    .flat()
    .map((ticket) => ({
      fanpassId: order.PK,
      refId: v4(),
      orderId: order.orderId,
      ticketId: ticket.ticketId,
      ticketType: ticket.ticketType,
      ticketDescription: ticket.ticketDescription,
      ticketPrice: ticket.ticketPrice,
      discountPercent: ticket.discountPercent,
      totalAmount: Math.round(OrderUtil.calcTicketPrice(ticket) / ticket.quantity),
      userId: order.userId,
      // status: 'ACTIVE',
      paymentTime: order.paymentTime,
      paymentGateway: order.paymentGateway,
      paymentInfo: order.paymentInfo,
    }));

  await UserFanpassModel.batchPut(flatFanpass);

  return flatFanpass;
}

async function sendMerchantInvoice(order) {
  process.env.TZ = 'Asia/Ho_Chi_Minh';
  const userEmail = order.userId;
  const payload = {
    orderId: order.orderId,
    totalAmount: order.amount.toLocaleString(),
    transportFee: order.transportFee.toLocaleString(),
    codAmount: (order.amount - order.transportFee).toLocaleString(),
    products: order.products.map((t) => ({
      ...t,
      totalAmount: t.totalAmount.toLocaleString(),
      type: t.type === MerchantProductType.DEAL_EVENT ? 'Merchandise sự kiện' : 'Deal từ nhãn hàng',
      originalAmount: t.promotion ? `${(t.price * t.quantity).toLocaleString()} đ` : undefined,
    })),
    shipment: {
      name: order.shipmentInfo.name,
      phoneNumber: order.shipmentInfo.phoneNumber,
      address: `${order.shipmentInfo.address}, ${order.shipmentInfo.ward}, ${order.shipmentInfo.district}, ${order.shipmentInfo.province}`,
    },
    transportLabel: order.transportInfo?.label || '--',
    labelUrl: order.transportInfo?.labelUrl,
  };
  console.log(payload);

  await Promise.all([
    sendEmail({
      to: userEmail,
      subject: `[EVENTISTA] Hóa đơn mua hàng`,
      context: payload,
      template: 'merchandise-invoice',
    }),
    sendEmail({
      to: '<EMAIL>',
      subject: `[EVENTISTA] Đơn hàng mới ${order.orderId}`,
      context: payload,
      template: 'merchandise-print-label',
    }),
  ]);
  console.log('Send merchant invoice successfully to [%s]', order.userId);
}
