const QueryString = require('qs');
const crypto = require('crypto');
const {OrderModel} = require('../../models/OrderModel');
const {OrderStatus, VNPayResultCode} = require('./const');
const {getSecretValue} = require('../../common/secret-manager');
const {sendMessageToQueue} = require('../../common/sqs');

class VNPayService {
  #config;
  #orderId;
  #productId;
  #resultCode;
  #transId;
  #amount;

  constructor(payload) {
    this.#orderId = payload['vnp_TxnRef'];
    const extraData = JSON.parse(payload['vnp_OrderInfo']);
    this.#productId = extraData.productId;
    this.#resultCode = payload['vnp_ResponseCode'];
    this.#transId = payload['vnp_TransactionNo'];
    this.#amount = payload['vnp_Amount'];
    this.vnpParams = payload;
  }

  async process() {
    await this.#getConfig();
    const ipnCheckResult = await this.#validateIpnData();
    if (ipnCheckResult) {
      return ipnCheckResult;
    }
    const orderCheckResult = await this.#validateOrder();
    if (orderCheckResult) {
      return orderCheckResult;
    }

    await sendMessageToQueue(
      {
        productId: this.#productId,
        orderId: this.#orderId,
        message: this.#getPaymentMessage(this.#resultCode),
        transId: this.#transId,
        paymentStatus: this.paymentStatus,
      },
      this.#productId,
    );

    return {RspCode: '00', Message: 'Success'};
  }

  async #getConfig() {
    this.#config = await getSecretValue(process.env.VNPAY_SERVICE);
  }

  #sortObject(obj) {
    const sorted = {};
    const str = [];
    let key;
    for (key in obj) {
      if (obj.hasOwnProperty(key)) {
        str.push(encodeURIComponent(key));
      }
    }
    str.sort();
    for (key = 0; key < str.length; key++) {
      sorted[str[key]] = encodeURIComponent(obj[str[key]]).replace(/%20/g, '+');
    }

    return sorted;
  }

  #hashParams(vnpParams) {
    const signData = QueryString.stringify(vnpParams, {encode: false});
    const hmac = crypto.createHmac('sha512', this.#config.secretKey);
    const signed = hmac.update(Buffer.from(signData, 'utf-8')).digest('hex');

    return signed;
  }

  #validateIpnData() {
    return new Promise((res, _) => {
      let vnpParams = this.vnpParams;
      const secureHash = vnpParams['vnp_SecureHash'];
      // const rspCode = vnpParams['vnp_ResponseCode'];

      delete vnpParams['vnp_SecureHash'];
      delete vnpParams['vnp_SecureHashType'];

      vnpParams = this.#sortObject(vnpParams);
      const signed = this.#hashParams(vnpParams);
      console.log(vnpParams);
      console.log(signed);

      if (secureHash !== signed) {
        res({
          code: '97',
          message: 'Checksum failed',
        });
        return;
      }

      res();
    });
  }

  async #validateOrder() {
    const order = await OrderModel.get({
      PK: this.#productId,
      orderId: this.#orderId,
    });
    if (!order) {
      return {RspCode: '01', Message: 'Order not found'};
    }
    // validate amount
    const vnpAmount = +this.#amount;
    if (+order.amount !== vnpAmount / 100) {
      return {RspCode: '04', Message: 'Amount invalid'};
    }
    if (OrderStatus.isProcessed(order.status)) {
      return {
        RspCode: '02',
        Message: 'This order has been updated to the payment status',
      };
    }
  }

  #getPaymentMessage(rspCode) {
    const messages = {
      '00': 'Giao dịch thành công',
      '07': 'Trừ tiền thành công. Giao dịch bị nghi ngờ (liên quan tới lừa đảo, giao dịch bất thường).',
      '09': 'Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng chưa đăng ký dịch vụ InternetBanking tại ngân hàng.',
      10: 'Giao dịch không thành công do: Khách hàng xác thực thông tin thẻ/tài khoản không đúng quá 3 lần',
      11: 'Giao dịch không thành công do: Đã hết hạn chờ thanh toán. Xin quý khách vui lòng thực hiện lại giao dịch.',
      12: 'Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng bị khóa.',
      13: 'Giao dịch không thành công do Quý khách nhập sai mật khẩu xác thực giao dịch (OTP). Xin quý khách vui lòng thực hiện lại giao dịch.',
      24: 'Giao dịch không thành công do: Khách hàng hủy giao dịch',
      51: 'Giao dịch không thành công do: Tài khoản của quý khách không đủ số dư để thực hiện giao dịch.',
      65: 'Giao dịch không thành công do: Tài khoản của Quý khách đã vượt quá hạn mức giao dịch trong ngày.',
      75: 'Ngân hàng thanh toán đang bảo trì.',
      79: 'Giao dịch không thành công do: KH nhập sai mật khẩu thanh toán quá số lần quy định. Xin quý khách vui lòng thực hiện lại giao dịch',
      99: 'Các lỗi khác (lỗi còn lại, không có trong danh sách mã lỗi đã liệt kê)',
    };

    return messages[rspCode];
  }

  get paymentStatus() {
    let status = OrderStatus.FAILED;
    if (VNPayResultCode.isSuccess(this.#resultCode)) {
      status = OrderStatus.SUCCESS;
    } else if (VNPayResultCode.isCanceled(this.#resultCode)) {
      status = OrderStatus.CANCELED;
    }
    return status;
  }
}

module.exports = {VNPayService};
