const {sendMessageToQueue} = require('../../common/sqs');
const {OrderModel} = require('../../models/OrderModel');
const {OrderStatus, MomoResultCode} = require('./const');

class MomoService {
  #orderId;
  #productId;
  #resultCode;
  #message;
  #transId;

  constructor(payload) {
    this.#orderId = payload.orderId;
    const extraData = JSON.parse(atob(payload.extraData));
    this.#productId = extraData.productId;
    this.#resultCode = payload.resultCode;
    this.#message = payload.message;
    this.#transId = payload.transId;
  }

  async #validateOrder() {
    const order = await OrderModel.get({
      PK: this.#productId,
      orderId: this.#orderId,
    });
    if (!order) {
      throw new Error('Order not found');
    }
    if (OrderStatus.isProcessed(order.status)) {
      throw new Error('This order has been updated to the payment status');
    }
  }

  async process() {
    await this.#validateOrder();
    await sendMessageToQueue(
      {
        productId: this.#productId,
        orderId: this.#orderId,
        message: this.#message,
        transId: this.#transId,
        paymentStatus: this.paymentStatus,
      },
      // this.#productId,
      this.#orderId, // TODO: work around
    );
  }

  get paymentStatus() {
    let status = OrderStatus.FAILED;
    if (MomoResultCode.isSuccess(this.#resultCode)) {
      status = OrderStatus.SUCCESS;
    } else if (MomoResultCode.isCanceled(this.#resultCode)) {
      status = OrderStatus.CANCELED;
    }
    return status;
  }
}

module.exports = {MomoService};
