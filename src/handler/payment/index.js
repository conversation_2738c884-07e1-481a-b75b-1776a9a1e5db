const ResponseBuilder = require('../../utils/response-builder.util');
const {PaymentEndpoint} = require('./const');
const {MomoService} = require('./momo.service');
const {ZaloPayService} = require('./zalopay.service');
const {PaymentwallService, PaymentwallActions} = require('./paymentwall.service');
const {PaypalService} = require('./paypal.service');
const {VNPayService} = require('./vnpay.service');

module.exports.handler = async (event) => {
  try {
    const routeKey = event.routeKey;
    console.log('routeKey: ', routeKey);
    switch (routeKey) {
      case PaymentEndpoint.MOMO_IPN:
        console.log('Momo IPN data: ', event.body);
        const body = JSON.parse(event.body);
        const momoService = new MomoService(body);
        await momoService.process();
        break;
      case PaymentEndpoint.ZALOPAY_IPN:
        console.log('Zalo IPN data: ', event.body);
        const zalopayService = new ZaloPayService(JSON.parse(event.body));
        await zalopayService.process();
        break;
      case PaymentEndpoint.VNPAY_IPN:
        console.log('VNPay IPN data: ', event.queryStringParameters);
        const vnpayService = new VNPayService(event.queryStringParameters);
        const result = await vnpayService.process();
        console.log('VNPay process result: ', result);
        return result;
      case PaymentEndpoint.MY_LOCATION:
        return await getMyLocation(event);
      case PaymentEndpoint.PAYMENTWALL_PAYMENT_SOLUTION:
        return await getPaymentSolution(event);
      case PaymentEndpoint.PAYMENTWALL_IPN:
        return await paymentwallProcess(event);
      case PaymentEndpoint.PAYPAL_IPN:
        return await paypalProcess(event);
    }
  } catch (error) {
    console.error('Payment IPN error: ', error.message);
    return ResponseBuilder.ok({successMessage: error.message});
  }
};

async function getMyLocation(event) {
  const {sourceIp} = event.requestContext.http;
  const paymentwallService = new PaymentwallService(PaymentwallActions.GET_GEO_LOCATION, {
    ipAddress: sourceIp,
  });
  await paymentwallService.process();
  return ResponseBuilder.ok(paymentwallService.geoLocation);
}

async function getPaymentSolution(event) {
  const countryCode = event.pathParameters.countryCode;
  const paymentwallService = new PaymentwallService(PaymentwallActions.GET_PAYMENT_SOLUTION, {
    countryCode,
  });
  await paymentwallService.process();
  return ResponseBuilder.ok(paymentwallService.paymentSolution);
}

async function paymentwallProcess(event) {
  const {sourceIp} = event.requestContext.http;
  const queryParams = event.queryStringParameters;

  try {
    const extraData = JSON.parse(queryParams.extra_data);
    const {productId} = extraData;
    console.log('IPN::Paymentwall - sourceIp ', sourceIp);
    console.log('IPN::Paymentwall - queryParams ', queryParams);
    console.log('IPN::Paymentwall - extraData ', extraData);
    console.log('IPN::Paymentwall - ref ', queryParams.ref);

    const data = {
      eventId: productId,
      orderId: queryParams.goodsid,
      rawQueryString: event.rawQueryString,
      sourceIp,
      ref: queryParams.ref,
    };
    const paymentwallService = new PaymentwallService(PaymentwallActions.ORDER_PROCESS, data);
    await paymentwallService.process();
  } catch (error) {
    console.error(error.message);
    return 'NOK';
  }
}

async function paypalProcess(event) {
  try {
    let body = event.body;
    if (event.isBase64Encoded) {
      body = atob(event.body);
    }
    console.log('IPN::Paypal - body ', body);

    const paypalService = new PaypalService(body);
    await paypalService.process();

    console.log('IPN::Paypal - DONE');
    return 'OK';
  } catch (error) {
    console.error('IPN::Paypal - error: ', error.message);
  }
}
