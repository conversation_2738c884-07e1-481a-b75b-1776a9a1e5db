const PaymentEndpoint = {
  MOMO_IPN: 'POST /v1/payment/momo',
  ZALOPAY_IPN: 'POST /v1/payment/zalopay',
  VNPAY_IPN: 'GET /v1/payment/vnpay',
  MY_LOCATION: 'GET /v1/payment/my-location',
  PAYMENTWALL_PAYMENT_SOLUTION: 'GET /v1/payment/paymentwall/payment-solution/{countryCode}',
  PAYMENTWALL_IPN: 'GET /v1/payment/paymentwall',
  PAYPAL_IPN: 'POST /v1/payment/paypal',
};

class OrderStatus {
  static REVIEW = 'REVIEW';
  static OPEN = 'OPEN';
  static SUCCESS = 'SUCCESS';
  static FAILED = 'FAILED';
  static CANCELED = 'CANCELED';

  static isProcessed(status) {
    return [this.SUCCESS, this.FAILED, this.CANCELED].includes(status);
  }

  static isFailed(status) {
    return [this.FAILED, this.CANCELED].includes(status);
  }
}

class MomoResultCode {
  static SUCCESS = String(0);
  static CANCELED = String(1006);

  static isSuccess(code) {
    return this.SUCCESS === String(code);
  }

  static isCanceled(code) {
    return this.CANCELED === String(code);
  }

  static isFailed(code) {
    return !Object.values(this).includes(String(code));
  }
}

class VNPayResultCode {
  static SUCCESS = '00';
  static CANCELED = '24';

  static isSuccess(code) {
    return this.SUCCESS === String(code);
  }

  static isCanceled(code) {
    return this.CANCELED === String(code);
  }

  static isFailed(code) {
    return !Object.values(this).includes(String(code));
  }
}

module.exports = {
  PaymentEndpoint,
  OrderStatus,
  MomoResultCode,
  VNPayResultCode,
};
