const {sendMessageToQueue} = require('../../common/sqs');
const {OrderModel} = require('../../models/OrderModel');
const {OrderStatus} = require('./const');
const {getSecretValue} = require('../../common/secret-manager');
const CryptoJS = require('crypto-js');

class ZaloPayService {
  #orderId;
  #productId;
  #message;
  #transId;
  #secret;
  #payload;

  constructor(payload) {
    this.#payload = payload;
    const data = JSON.parse(payload.data);
    console.log(data);

    this.#orderId = data.app_trans_id.split('_')[1];
    const extraData = JSON.parse(data.item)[0];
    this.#productId = extraData.productId;
    this.#message = '';
    this.#transId = data.app_trans_id;
  }

  async #validateOrder() {
    this.#secret = await getSecretValue(process.env.ZALOPAY_SERVICE);
    const order = await OrderModel.get({
      PK: this.#productId,
      orderId: this.#orderId,
    });
    if (!order) {
      throw new Error('Order not found');
    }
    if (OrderStatus.isProcessed(order.status)) {
      throw new Error('This order has been updated to the payment status');
    }
  }

  async process() {
    await this.#validateOrder();
    await sendMessageToQueue(
      {
        productId: this.#productId,
        orderId: this.#orderId,
        message: this.#message,
        transId: this.#transId,
        paymentStatus: this.paymentStatus,
      },
      // this.#productId,
      this.#orderId, // TODO: work around
    );
  }

  get paymentStatus() {
    let status = OrderStatus.FAILED;
    // eslint-disable-next-line new-cap
    const mac = CryptoJS.HmacSHA256(this.#payload.data, this.#secret.callbackKey).toString();
    if (mac === this.#payload.mac) status = OrderStatus.SUCCESS;

    return status;
  }
}

module.exports = {ZaloPayService};
