const Paymentwall = require('paymentwall');
const ApiObject = require('paymentwall/lib/ApiObject');
const HttpAction = require('paymentwall/lib/HttpAction');
const https = require('https');
const querystring = require('querystring');
const moment = require('moment');

const {v4: uuidv4} = require('../../utils/uuid.util');
const {getSecretValue} = require('../../common/secret-manager');
const {OrderModel} = require('../../models/OrderModel');
const {sendMessageToQueue} = require('../../common/sqs');
const {OrderStatus} = require('./const');

const PaymentwallActions = {
  GET_GEO_LOCATION: 'geo-location',
  GET_PAYMENT_SOLUTION: 'payment-solution',
  ORDER_PROCESS: 'order-process',
};

class PaymentwallService {
  #config;
  #action;
  #data;
  #isDeliverable;
  #isCancelable;
  #buyerEmail;
  geoLocation;
  paymentSolution;

  constructor(action, payload) {
    this.#action = action;
    this.#data = payload;
  }

  async #getConfig() {
    this.#config = await getSecretValue(process.env.PAYMENTWALL_SERVICE);
  }

  async #validateOrder() {
    const order = await OrderModel.get({
      PK: this.#data.eventId,
      orderId: this.#data.orderId,
    });
    // validate order id
    if (!order) {
      console.error('Paymentwall::validateOrder - Không tim thấy đơn hàng');
      throw new Error('NOK');
    }
    // vaidate payment status of order
    if (OrderStatus.isProcessed(order.status)) {
      console.error('Paymentwall::validateOrder - Đơn hàng đã được xử lý');
      throw new Error('NOK');
    }
    console.log('Paymentwall::validateOrder - Kiểm tra đơn hàng thành công');
    this.#buyerEmail = order.userId;
  }

  async #validatePingback() {
    const {publicKey, privateKey} = this.#config;
    // eslint-disable-next-line new-cap
    Paymentwall.Configure(Paymentwall.Base.API_GOODS, publicKey, privateKey);

    const pingback = new Paymentwall.Pingback(
      this.#data.rawQueryString,
      '**************', // this.#data.sourceIp, -> todo
    );

    const result = pingback.validate();
    console.log('Paymentwall::pingback.validate - result: ', result);
    if (!result) {
      console.error('Paymentwall::validatePingback - ', pingback.getErrorSummary());
      throw new Error(pingback.getErrorSummary());
    }
    this.#isDeliverable = pingback.isDeliverable();
    this.#isCancelable = pingback.isCancelable();
  }

  async #delivery() {
    process.env.TZ = 'Asia/Ho_Chi_Minh';
    try {
      const {publicKey, privateKey} = this.#config;
      // eslint-disable-next-line new-cap
      Paymentwall.Configure(Paymentwall.Base.API_GOODS, publicKey, privateKey);

      const api = new ApiObject();
      api.createDeliveryRequest = function () {
        const url = this.BRICK_BASE_URL;
        const method = 'POST';
        const postOptions = this.createPostOptions(url, '/api/delivery', method);

        return postOptions;
      };
      const postOptions = api.createDeliveryRequest();
      const now = moment().format('YYYY/MM/DD HH:mm:ss +7000');
      let postData = {
        payment_id: this.#data.ref,
        merchant_reference_id: this.#data.orderId,
        type: 'digital',
        status: 'order_placed',
        estimated_delivery_datetime: now,
        estimated_update_datetime: now,
        refundable: false,
        reason: 'none',
        attachments: {},
        details: 'Item was delivered to the user account and via email',
        'shipping_address[email]': this.#buyerEmail,
      };

      postData = querystring.stringify(postData);

      HttpAction.runAction(postOptions, postData, true, function (response) {
        response = response.JSON_chunk;
        console.log('Paymentwall::delivery - response: ', response);
        if (response.error) {
          console.log('Paymentwall::delivery - error: ', response.error);
          console.log('Paymentwall::delivery - notices: ', response.notices);
        }
      });
    } catch (error) {}
  }

  async #updateOrder(status, transId, message) {
    await sendMessageToQueue(
      {
        productId: this.#data.eventId,
        orderId: this.#data.orderId,
        message: message,
        transId: transId,
        paymentStatus: status,
      },
      this.#data.orderId, // TODO: work around
    );
  }

  async process() {
    await this.#getConfig();
    switch (this.#action) {
      case PaymentwallActions.GET_GEO_LOCATION:
        this.geoLocation = await this.#getGeolocation();
        break;
      case PaymentwallActions.GET_PAYMENT_SOLUTION:
        this.paymentSolution = await this.#getPaymentSolution();
        break;
      case PaymentwallActions.ORDER_PROCESS:
        await this.#validateOrder();
        await this.#validatePingback();
        if (this.#isDeliverable) {
          await this.#delivery();
          await this.#updateOrder(OrderStatus.SUCCESS, 'Thành công');
        } else if (this.#isCancelable) {
          await this.#updateOrder(OrderStatus.FAILED, 'Đơn hàng đã bị hủy');
        }
    }
  }

  #getGeolocation() {
    const {publicKey} = this.#config;

    const endpoint = 'https://api.paymentwall.com/api/rest/country';
    const requestParams = `key=${publicKey}&uid=${uuidv4()}&user_ip=${this.#data.ipAddress}`;

    return new Promise((resolve, reject) => {
      https
        .get(`${endpoint}?${requestParams}`, (resp) => {
          let data = '';
          resp.setEncoding('utf8');
          // A chunk of data has been received.
          resp.on('data', (chunk) => {
            data += chunk;
          });

          // The whole response has been received. Print out the result.
          resp.on('end', () => {
            resolve(JSON.parse(data));
          });
        })
        .on('error', (err) => {
          reject(err);
        });
    });
  }

  #getPaymentSolution() {
    const {publicKey, privateKey} = this.#config;
    const countryCode = this.#data.countryCode;
    // eslint-disable-next-line new-cap
    Paymentwall.Configure(Paymentwall.Base.API_GOODS, publicKey, privateKey);
    const sign = Paymentwall.WidgetSignature.calculateSignature(
      {
        country_code: countryCode,
        key: publicKey,
        sign_version: 2,
      },
      privateKey,
      2,
    );

    const endpoint = 'https://api.paymentwall.com/api/payment-systems';
    const requestParams = `key=${publicKey}&country_code=${countryCode}&sign_version=2&sign=${sign}`;

    return new Promise((resolve, reject) => {
      https
        .get(`${endpoint}?${requestParams}`, (resp) => {
          let data = '';
          resp.setEncoding('utf8');
          // A chunk of data has been received.
          resp.on('data', (chunk) => {
            data += chunk;
          });

          // The whole response has been received. Print out the result.
          resp.on('end', () => {
            const result = JSON.parse(data);
            if (!Array.isArray(result)) {
              resolve([]);
              return;
            }
            resolve(result);
          });
        })
        .on('error', (err) => {
          reject(err);
        });
    });
  }
}

module.exports = {PaymentwallService, PaymentwallActions};
