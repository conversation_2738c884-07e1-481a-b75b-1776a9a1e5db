const {getSecretValue} = require('../../common/secret-manager');
const {sendMessageToQueue} = require('../../common/sqs');
const OrderStatus = require('../../common/types/order-status');
const {OrderModel} = require('../../models/OrderModel');

class PaypalService {
  #verificationUrl;
  #data;
  #isSuccess;

  #COMPLETED = 'completed';

  constructor(data) {
    const urlParams = new URLSearchParams(data);
    const paymentStatus = urlParams.get('payment_status') || '';
    console.log('PaypalService::updateOrder - paymentStatus: ', paymentStatus);

    this.#isSuccess = paymentStatus.toLowerCase() === this.#COMPLETED;

    const customValue = urlParams.get('custom');
    console.info('PaypalService::updateOrder - customValue: ', customValue);
    const [, orderId, productId] = customValue.split('|');

    this.#data = {orderId, productId, transId: urlParams.get('txn_id')};
  }

  async #loadConfig() {
    console.time('PaypalService::loadConfig');
    const config = await getSecretValue(process.env.PAYPAL_SERVICE);
    this.#verificationUrl = config.verificationUrl;
    console.timeEnd('PaypalService::loadConfig');
  }

  #validateIPN() {
    // Read the IPN message sent from PayPal and prepend 'cmd=_notify-validate'
    const body = `cmd=_notify-validate&${this.#data}`;

    return new Promise((resolve, reject) => {
      fetch(this.#verificationUrl, {
        method: 'post',
        headers: {
          Connection: 'close',
        },
        body,
      })
        .then((response) => response.text())
        .then((data) => {
          console.info('PaypalService::validateIPN - response: ', data);
          resolve(data === 'VERIFIED');
        })
        .catch((error) => {
          console.error('PaypalService::validateIPN - error: ', error.message);
          reject(error);
        });
    });
  }

  async #validateOrder() {
    const order = await OrderModel.get({
      PK: this.#data.productId,
      orderId: this.#data.orderId,
    });
    // validate order id
    if (!order) {
      console.error('PaypalService::validateOrder - Không tim thấy đơn hàng');
      throw new Error('Order not found');
    }
    // vaidate payment status of order
    if (OrderStatus.isProcessed(order.status)) {
      console.error('PaypalService::validateOrder - Đơn hàng đã được xử lý');
      throw new Error('Order is already processed');
    }
    console.log('PaypalService::validateOrder - Kiểm tra đơn hàng thành công');
  }

  async #updateOrder() {
    const {productId, orderId, transId} = this.#data;

    await sendMessageToQueue(
      {
        productId,
        orderId,
        message: this.#isSuccess ? 'Thành công' : 'Thanh toán không thành công',
        transId,
        paymentStatus: this.#isSuccess ? OrderStatus.SUCCESS : OrderStatus.FAILED,
      },
      orderId,
    );
    console.info('PaypalService::updateOrder - Message has been sent to queue!');
  }

  async process() {
    console.debug('PaypalService::process() - data: ', this.#data);
    // load config
    await this.#loadConfig();
    // validate order
    await this.#validateOrder();
    // const isValid = await this.#validateIPN();
    // if (!isValid) {
    //   console.log('PaypalService::process - Request is invalid');
    //   return;
    // }
    await this.#updateOrder();
  }
}

module.exports = {PaypalService};
