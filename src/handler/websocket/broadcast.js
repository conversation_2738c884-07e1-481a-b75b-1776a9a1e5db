const {sendMessage} = require('../../common/api-gateway');
const {WsConnectionService} = require('./ws.service');

exports.handler = async function (event) {
  // For debug purposes only.
  // You should not log any sensitive information in production.
  console.log('EVENT: \n' + JSON.stringify(event, null, 2));

  const {body, topicName} = event;
  const connections = await WsConnectionService.getAllConnections();
  await Promise.all(connections.map((connectionId) => sendMessage(connectionId, topicName, body)));
};
