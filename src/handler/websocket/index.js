const {WsConnectionService} = require('./ws.service');

module.exports.handler = async function (event, context) {
  // For debug purposes only.
  // You should not log any sensitive information in production.
  console.log('EVENT: \n' + JSON.stringify(event, null, 2));

  const {
    body,
    requestContext: {connectionId, routeKey},
  } = event;
  switch (routeKey) {
    case '$connect':
      console.log(`Connect to connectionId ${connectionId}`);
      await WsConnectionService.createConnection(connectionId);
      break;

    case '$disconnect':
      console.log(`Disconnect to connectionId ${connectionId}`);
      await WsConnectionService.deleteConnection(connectionId);
      break;

    case '$default':
    default:
      console.log(`$default route body: ${JSON.stringify(body)}`);
  }

  // Return a 200 status to tell API Gateway the message was processed
  // successfully.
  // Otherwise, API Gateway will return a 500 to the client.
  return {statusCode: 200};
};
