const {WSConnectionModel} = require('../../models/WsConnectionModel');

const getAllConnections = async () => {
  const connections = await WSConnectionModel.scan().all().exec();
  return connections.toJSON();
};

const deleteConnection = async (connectionId) => {
  await WSConnectionModel.delete({connectionId});
};

module.exports.createConnection = async (connectionId) => {
  putItemInput = {
    TableName: connectionTable,
    Item: {
      connectionId: {
        S: connectionId,
      },
      ttl: {
        N: parseInt(Date.now() / 1000 + 6 * 60 * 60).toString(),
      },
    },
  };
  await executePutItem(putItemInput);
};

const createConnection = async (connectionId) => {
  await WSConnectionModel.create({
    connectionId,
    ttl: parseInt(Date.now() / 1000 + 6 * 60 * 60),
  });
};

module.exports.WsConnectionService = {
  createConnection,
  deleteConnection,
  getAllConnections,
};
