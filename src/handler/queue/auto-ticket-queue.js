const {OrderModel} = require('../../models/OrderModel');
const {
  getTicketsByTicketClassId,
  holdTicketsForCustomer,
} = require('../../../drizzle/models/ticket');
const OrderStatus = require('../../common/types/order-status');

async function getOrder(data) {
  return await OrderModel.get({
    PK: data.productId,
    orderId: data.orderId,
  });
}

function groupedData(results) {
  return results.reduce((zones, item) => {
    // Tìm zone hiện tại theo zoneId
    let zone = zones.find((z) => z.zoneId === item.zoneId);

    // Nếu zone chưa tồn tại, tạo mới
    if (!zone) {
      zone = {zoneId: item.zoneId, zonePosition: item.zonePosition, rows: []};
      zones.push(zone);
    }

    // Tìm row hiện tại theo rowId trong zone
    let row = zone.rows.find((r) => r.rowId === item.rowId);

    // Nếu row chưa tồn tại, tạo mới
    if (!row) {
      row = {rowId: item.rowId, rowPosition: item.rowPosition, items: []};
      zone.rows.push(row);
    }

    // Thêm item vào row
    row.items.push({
      id: item.id,
      position: item.position,
    });

    return zones;
  }, []);
}

// Hàm để tìm n item có vị trí liên tiếp
function findNConsecutiveItems(groupedData, n) {
  if (n < 2) {
    console.log('Số lượng item phải lớn hơn hoặc bằng 2 để có thể kiểm tra liên tiếp.');
    return;
  }
  let result = null;

  for (const zone of groupedData) {
    console.log(`Zone ID: ${zone.zoneId}`);

    for (const row of zone.rows) {
      console.log(`  Row ID: ${row.rowId}`);

      // Sắp xếp các item theo position
      const sortedItems = row.items.sort((a, b) => a.position - b.position);

      // Duyệt qua các item để tìm nhóm n item liên tiếp
      for (let i = 0; i <= sortedItems.length - n; i++) {
        let isConsecutive = true;

        // Kiểm tra n item có position liên tiếp nhau không
        for (let j = 0; j < n - 1; j++) {
          if (sortedItems[i + j + 1].position !== sortedItems[i + j].position + 1) {
            isConsecutive = false;
            break;
          }
        }

        // Nếu tìm thấy nhóm n item liên tiếp, in ra
        if (isConsecutive) {
          console.log(`    Found ${n} consecutive items:`);
          console.log(`${JSON.stringify(sortedItems.slice(i, i + n), null, 2)}`);
          result = sortedItems.slice(i, i + n);
          break;
        }
      }

      if (!!result) {
        break;
      }
    }

    if (!!result) {
      break;
    }
  }
  return result;
}

exports.handler = async function (event, context) {
  const batchItemFailures = [];
  // SQS may invoke with multiple messages
  const cancelOrder = async (order, reason) => {
    order.status = OrderStatus.CANCELED;
    order.cancelReason = reason;
    await order.save();
  };
  for (const message of event.Records) {
    try {
      console.log('Message ID: ', message.messageId);
      console.log('Record: ', message.body);
      const bodyData = JSON.parse(message.body);

      const order = await getOrder(bodyData);
      if (!order) {
        console.log('Order not found');
        continue;
      }
      const [orderTicket] = order.tickets;
      let results = await getTicketsByTicketClassId(bodyData.ticketClassId);
      if (!!bodyData.zoneId) {
        results = results.filter((item) => item.zoneId === bodyData.zoneId);
      }
      console.log('Open tickets: ', results);
      if (!results?.length || results?.length < orderTicket.quantity) {
        await cancelOrder(order, 'Số lượng vé bạn chọn không còn đủ.');
        continue;
      }
      if (orderTicket.quantity === 1) {
        const [ticket] = results;
        await holdTicketsForCustomer([ticket.id], bodyData.orderId);
      } else {
        const tickets = groupedData(results);
        console.log('Ticket grouped: ', tickets);
        const ticketsMatched = findNConsecutiveItems(tickets, orderTicket.quantity);
        console.log('Ticket matched: ', ticketsMatched);

        if (!ticketsMatched) {
          // trường hợp ko đủ vé thỏa mãn liên tiếp thì assign ghế tróng cho user
          // sau đó cần confirm lại với user xem có muốn lấy ghế ko liên tiếp ko?
          const randomTickets = results.splice(0, orderTicket.quantity);
          console.log('Ticket random: ', randomTickets);
          await holdTicketsForCustomer(
            randomTickets.map((t) => t.id),
            bodyData.orderId,
          );
          order.status = OrderStatus.CONFIRM;
          order.confirmAt = new Date();
          await order.save();
          continue;
        }

        await holdTicketsForCustomer(
          ticketsMatched.map((t) => t.id),
          bodyData.orderId,
        );
      }
      order.status = OrderStatus.REVIEW;
      await order.save();
    } catch (error) {
      console.error(error);
      batchItemFailures.push({ItemIdentifier: message.messageId});
    }
  }
  return {
    statusCode: 200,
    body: JSON.stringify({batchItemFailures}),
  };
};
