const {exec} = require('child_process');
const fs = require('fs');
const {getFileBuffer, updateFileObject} = require('../../utils/s3.utils');
const {BlogModel} = require('../../models/BlogModel');
const {updateBlog} = require('../../repository/blog');
const ffmpegPath = '/opt/bin/ffmpeg';

module.exports.handler = async (event) => {
  const key = event.Records[0].s3.object.key;
  console.log(`Processing file: ${key}`);
  let uploadPath = process.env.PODCAST_UPLOAD_PATH;
  if (key.endsWith('.mp4')) uploadPath = process.env.COURSE_UPLOAD_PATH;
  const pathSplited = key.split('/');
  const filename = pathSplited[pathSplited.length - 1];
  const inputPath = `/tmp/${filename}`;
  const outputDir = `/tmp/${Date.now()}`;
  const outputPath = `${outputDir}/${filename.split('.')[0]}.m3u8`;
  console.log(`Tmp output path: ${outputPath}`);

  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, {recursive: true});
  }

  // Tải file từ S3
  const file = await getFileBuffer(key);
  await fs.writeFileSync(inputPath, file);

  // Chạy FFmpeg để xử lý
  await new Promise((resolve, reject) => {
    const command = `${ffmpegPath} -i ${inputPath} -hls_time 60 -hls_playlist_type vod -f hls ${outputPath}`;
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`FFmpeg Error: ${stderr}`);
        reject(error);
      }
      console.log(`FFmpeg Output: ${stdout}`);
      resolve();
    });
  });

  // Tải file kết quả lên S3
  const outputFiles = fs.readdirSync(outputDir);
  const s3OutputDir = `${uploadPath}/${pathSplited[pathSplited.length - 2]}`;
  console.log('Output dir: ', s3OutputDir);
  let mainFile = '';
  for (const file of outputFiles) {
    const fileStream = fs.createReadStream(`${outputDir}/${file}`);
    const s3Key = `${s3OutputDir}/${file}`;

    if (file.endsWith('.m3u8')) {
      mainFile = s3Key;
    }

    const uploadParams = {
      Body: fileStream,
    };
    await updateFileObject(uploadParams, s3Key);
  }

  if (uploadPath === process.env.PODCAST_UPLOAD_PATH) {
    const query = await BlogModel.query('status')
      .eq('PROCESSING')
      .where('file')
      .contains(mainFile)
      .using('statusIndex')
      .all()
      .exec();
    const blogs = query.toJSON();
    for (const blog of blogs) {
      await updateBlog({
        PK: blog.PK,
        SK: blog.SK,
        status: 'DONE',
      });
    }
  }

  return {message: 'Processing completed successfully!'};
};
