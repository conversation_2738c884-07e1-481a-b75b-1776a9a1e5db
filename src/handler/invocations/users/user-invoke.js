const ErrorCode = require('../../../constants/error-code');
const UserUtil = require('../../client/auth/util');
const {UserService} = require('./user.service');

module.exports.verifyUserAuth = async (payload) => {
  try {
    console.log('User Email: ', payload.email);
    const user = await UserService.getUserByEmail(payload.email);

    const isValidPassword = await UserUtil.comparePassword(payload.password, user?.password);
    if (!user || !isValidPassword || !user.verified) {
      return {
        code: ErrorCode.INVALID_CREDENTIALS,
        message: 'Thông tin đăng nhập không chính xác',
      };
    }

    return user;
  } catch (_) {
    return null;
  }
};

module.exports.getUserByEmail = async (payload) => {
  try {
    console.log('User Email: ', payload.email);
    return await UserService.getUserByEmail(payload.email);
  } catch (_) {
    return null;
  }
};
