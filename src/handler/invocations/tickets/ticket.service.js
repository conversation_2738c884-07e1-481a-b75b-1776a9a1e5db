const {orderBy} = require('lodash');
const ErrorCode = require('../../../constants/error-code');
const {EventModel} = require('../../../models/EventModel');
const {EventTicketsModel} = require('../../../models/EventTIckets');
const {OrderModel} = require('../../../models/OrderModel');
const {UserModel} = require('../../../models/UserModel');
const {TicketDao} = require('./ticket.dao');

const getOrder = async ({productId, orderId}) => {
  return await OrderModel.get({
    PK: productId,
    orderId,
  });
};

const updateTicketStatus = async (data) => {
  const [ticket] = await TicketDao.getTicketByGenerateCodeAndOrderId(data.refId, data.orderId);
  if (!ticket) {
    return {
      code: ErrorCode.TICKET_INVALID,
      message: '<PERSON><PERSON> không hợp lệ',
    };
  }
  if (ticket.checkin) {
    return {
      code: ErrorCode.TICKET_CHECKED_IN,
      message: 'Vé đã được check-in',
    };
  }
  await TicketDao.updateCheckinStatusByGenerateCode(data);
  await EventTicketsModel.update(
    {
      eventId: ticket.eventId,
      refId: ticket.generateCode,
    },
    {
      checkedIn: true,
      checkinTime: new Date(),
      checkinBy: data.userId,
    },
  );

  return {
    code: 0,
    message: 'Check-in thành công',
  };
};

const getEvent = async (productId) => {
  return await EventModel.get({PK: productId});
};

const getEventTickets = async (userId, conditions) => {
  const staff = await UserModel.get({PK: userId});
  const eventId = staff.assignedEvent;
  if (!eventId) {
    return {
      code: ErrorCode.STAFF_NOT_ASSIGNED,
      message: 'Nhân viên chưa được phân vào sự kiện',
    };
  }

  const whereConditions = {eventId};
  if (conditions.ticketClassId) {
    whereConditions.ticketClassId = conditions.ticketClassId;
  }
  if (conditions.zoneId) {
    whereConditions.zoneId = conditions.zoneId;
  }
  if (conditions.rowId) {
    whereConditions.rowId = conditions.rowId;
  }

  const [tickets, ticketClasses] = await Promise.all([
    TicketDao.getAllTicketsByEvent(eventId),
    TicketDao.getTicketClassByEvent(eventId),
  ]);

  const summary = ticketClasses.map((tc) => {
    const ticketsByTicketClass = tickets.filter((t) => tc.id === t.ticketClassId);
    return {
      name: tc.name,
      totalTickets: ticketsByTicketClass.length,
      totalCheckedIn: ticketsByTicketClass.filter((t) => t.checkin).length,
    };
  });

  const results = tickets.map((t) => ({
    name: t.ticketClassName,
    seatCode: `${t.zoneName} | ${t.rowName}${t.code}`,
    checkedIn: !!t.checkin,
    email: t.customerEmail,
    phoneNumber: t.customerPhone,
    refId: t.generateCode,
    ticketId: t.id,
    orderId: t.orderId,
    productId: t.eventId,
  }));

  return {
    code: 0,
    data: {
      summary,
      tickets: orderBy(results, ['name', 'seatCode'], ['asc', 'asc']),
    },
  };
};

const getTicketFilterConditions = async (userId) => {
  const staff = await UserModel.get({PK: userId});
  const eventId = staff.assignedEvent;
  if (!eventId) {
    return {
      code: ErrorCode.STAFF_NOT_ASSIGNED,
      message: 'Nhân viên chưa được phân vào sự kiện',
    };
  }

  const [ticketClasses, zones, rows] = await Promise.all([
    TicketDao.getTicketClassByEvent(eventId),
    TicketDao.getZonesByEvent(eventId),
    TicketDao.getRowsByEvent(eventId),
  ]);

  return {
    code: 0,
    data: ticketClasses.map((tc) => ({
      ...tc,
      zones: zones
        .filter((z) => z.ticketClassId === tc.id)
        .map((z) => ({
          ...z,
          rows: rows.filter((r) => r.zoneId === z.id),
        })),
    })),
  };
};

module.exports.TicketService = {
  getOrder,
  updateTicketStatus,
  getEvent,
  getEventTickets,
  getTicketFilterConditions,
};
