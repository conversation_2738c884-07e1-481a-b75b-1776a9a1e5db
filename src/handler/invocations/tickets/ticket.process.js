const ErrorCode = require('../../../constants/error-code');
const {TicketDao} = require('./ticket.dao');
const {TicketService} = require('./ticket.service');

class TicketProcess {
  #requestTicket;
  #ticket;
  #event;
  #order;
  #data;
  #error;

  constructor(data) {
    this.#data = data;
  }

  async process() {
    await this.#checkTicket();
    await this.#getOrder();
    await this.#getEvent();
  }

  async #getOrder() {
    if (!!this.#error) return;
    const order = await TicketService.getOrder(this.#data);
    if (!order) {
      this.#error = {
        code: ErrorCode.TICKET_INVALID,
        message: 'Vé không hợp lệ',
      };
      return;
    }
    this.#order = order;
  }

  async #getEvent() {
    if (!!this.#error) return;
    this.#event = await TicketService.getEvent(this.#requestTicket.productId);
  }

  #checkTicket() {
    return new Promise(async (resolve, _) => {
      const [ticket] = await TicketDao.getTicketByGenerateCodeAndOrderId(
        this.#data.refId,
        this.#data.orderId,
      );
      if (!ticket) {
        this.#error = {
          code: ErrorCode.TICKET_INVALID,
          message: 'Vé không hợp lệ',
        };
        resolve();
        return;
      }
      this.#requestTicket = {
        productId: ticket.eventId,
        orderId: ticket.orderId,
      };
      this.#ticket = ticket;
      resolve();
    });
  }

  get error() {
    return this.#error;
  }

  get info() {
    const {paymentInfoFields = []} = this.#event.general;
    const customFields = paymentInfoFields.filter((f) => !f.isDefault);
    const calendar = this.#event.eventCalendar?.find(
      (item) => item.calendarId === this.#ticket.calendarId,
    );

    const data = {
      productId: this.#requestTicket.productId,
      orderId: this.#requestTicket.orderId,
      ticket: {
        name: this.#ticket.ticketName,
        zoneName: this.#ticket.zoneName,
        seatCode: this.#ticket.rowName + this.#ticket.code,
        ticketId: this.#ticket.ticketClassId,
        refId: this.#data.refId,
        checkedIn: !!this.#ticket.checkin,
        checkinTime: this.#ticket.checkinAt,
        paymentTime: this.#order.paymentTime,
        customerName: this.#order.paymentInfo?.name || 'N/A',
        account: this.#order.paymentInfo?.email || this.#order.userId,
        phoneNumber: this.#order.paymentInfo?.phoneNumber || 'N/A',
        customValues: customFields.map((f) => ({
          key: f.key,
          text: f.text,
          value: this.#order.paymentInfo[f.key] || '--',
        })),
      },
      event: {
        eventName: this.#event.eventName,
        location: this.#event.location,
        startDate: calendar?.startDate,
        endDate: calendar?.endDate,
      },
    };

    return {
      code: 0,
      data,
    };
  }
}

module.exports = {TicketProcess};
