const {TicketProcess} = require('./ticket.process');
const {TicketService} = require('./ticket.service');

const errorHandler = (error) => ({
  code: 100500,
  message: '<PERSON><PERSON> có lỗi xảy ra',
  error: {
    message: error.message,
    stack: error.stack,
  },
});

module.exports.scanTicket = async (payload) => {
  console.log('payload', payload);
  try {
    const ticket = new TicketProcess(payload);
    await ticket.process();
    if (ticket.error) {
      return ticket.error;
    }
    return ticket.info;
  } catch (error) {
    return errorHandler(error);
  }
};

module.exports.checkinTicket = async (payload) => {
  console.log('payload', payload);
  try {
    return await TicketService.updateTicketStatus(payload);
  } catch (error) {
    return errorHandler(error);
  }
};

module.exports.getTickets = async (payload) => {
  console.log('payload', payload);
  try {
    return await TicketService.getEventTickets(payload.userId, payload.conditions);
  } catch (error) {
    return errorHandler(error);
  }
};

module.exports.getFilterConditions = async (payload) => {
  console.log('payload', payload);
  try {
    return await TicketService.getTicketFilterConditions(payload.userId);
  } catch (error) {
    return errorHandler(error);
  }
};
