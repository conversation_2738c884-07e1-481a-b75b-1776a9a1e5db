const {eq, asc, and} = require('drizzle-orm');
const {withConnection} = require('../../../../drizzle/db');
const schema = require('../../../../drizzle/schema');

const getAllTicketsByEvent = async (eventId) => {
  return withConnection(async (db) => {
    const result = await db
      .select({
        ...schema.tickets,
        zoneName: schema.zones.name,
        rowName: schema.rows.name,
        zonePosition: schema.zones.position,
        rowPosition: schema.rows.position,
        ticketClassName: schema.ticketClass.name,
      })
      .from(schema.tickets)
      .leftJoin(schema.rows, eq(schema.tickets.rowId, schema.rows.id))
      .leftJoin(schema.zones, eq(schema.tickets.zoneId, schema.zones.id))
      .leftJoin(schema.ticketClass, eq(schema.tickets.ticketClassId, schema.ticketClass.id))
      .where(eq(schema.tickets.eventId, eventId))
      .orderBy(asc(schema.zones.position), asc(schema.rows.position), asc(schema.tickets.position));
    return result;
  });
};

const getTicketByGenerateCodeAndOrderId = async (generateCode, orderId) => {
  return withConnection(async (db) => {
    const result = await db
      .select({
        ...schema.tickets,
        zoneName: schema.zones.name,
        rowName: schema.rows.name,
        ticketName: schema.ticketClass.name,
      })
      .from(schema.tickets)
      .leftJoin(schema.zones, eq(schema.zones.id, schema.tickets.zoneId))
      .leftJoin(schema.rows, eq(schema.rows.id, schema.tickets.rowId))
      .leftJoin(schema.ticketClass, eq(schema.tickets.ticketClassId, schema.ticketClass.id))
      .where(
        and(eq(schema.tickets.generateCode, generateCode), eq(schema.tickets.orderId, orderId)),
      );
    return result;
  });
};

const updateCheckinStatusByGenerateCode = async (data) => {
  return withConnection(async (db) => {
    await db
      .update(schema.tickets)
      .set({checkin: true, checkinAt: new Date(), checkinBy: data.userId})
      .where(eq(schema.tickets.generateCode, data.refId));
  });
};

const getTicketClassByEvent = async (eventId) => {
  return withConnection(async (db) => {
    const result = await db
      .select({
        id: schema.ticketClass.id,
        name: schema.ticketClass.name,
      })
      .from(schema.ticketClass)
      .where(eq(schema.ticketClass.eventId, eventId))
      .orderBy(asc(schema.ticketClass.name));
    return result;
  });
};

const getZonesByEvent = async (eventId) => {
  return withConnection(async (db) => {
    const result = await db
      .select({
        id: schema.zones.id,
        name: schema.zones.name,
        ticketClassId: schema.zones.ticketClassId,
      })
      .from(schema.zones)
      .where(eq(schema.zones.eventId, eventId))
      .orderBy(asc(schema.zones.name));
    return result;
  });
};

const getRowsByEvent = async (eventId) => {
  return withConnection(async (db) => {
    const result = await db
      .select({
        id: schema.rows.id,
        name: schema.rows.name,
        zoneId: schema.rows.zoneId,
      })
      .from(schema.rows)
      .where(eq(schema.rows.eventId, eventId))
      .orderBy(asc(schema.rows.name));
    return result;
  });
};

module.exports.TicketDao = {
  getAllTicketsByEvent,
  getTicketByGenerateCodeAndOrderId,
  updateCheckinStatusByGenerateCode,
  getTicketClassByEvent,
  getZonesByEvent,
  getRowsByEvent,
};
