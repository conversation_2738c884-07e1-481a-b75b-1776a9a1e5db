const {AmplifyClient, CreateBranchCommand} = require('@aws-sdk/client-amplify');
const amplifyClient = new AmplifyClient();

const {Bitbucket} = require('./bitbucket-fetch');

module.exports.handler = async (payload) => {
  console.log('payload - ', payload);
  try {
    const subDomain = payload.domain?.split('.')[0];
    const branchName = process.env.ENV === 'prod' ? subDomain : `${process.env.ENV}-${subDomain}`;
    await Bitbucket.createBranch(branchName, 'fan-engagement-fe');
    const response = await createAmplifyBranch(branchName, payload.eventPath, payload.domain);
    console.log('Deployed amplify: %s', JSON.stringify(response));
  } catch (error) {
    console.error(error.message);
  }
};

async function createAmplifyBranch(branchName, eventPath, domain) {
  const input = {
    appId: process.env.AMPLIFY_APP_ID,
    branchName: branchName,
    environmentVariables: {
      EVENT_URL: eventPath,
      NEXTAUTH_URL: `https://${domain}`,
      TRANSLATION_URL: `https://${domain}`,
    },
    customDomains: [domain],
  };
  const command = new CreateBranchCommand(input);
  return amplifyClient.send(command);
}
