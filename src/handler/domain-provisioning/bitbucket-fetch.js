const fetch = require('node-fetch');

const createBranch = async (branchName, repoName) => {
  const url = `https://api.bitbucket.org/2.0/repositories/quantd95/${repoName}/refs/branches`;
  const option = {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${process.env.BITBUCKET_TOKEN}`,
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      name: branchName,
      target: {
        hash: 'prod',
      },
    }),
  };
  const response = await fetch(url, option);
  console.log(`Response: ${response.status} ${response.statusText}`);
  if (response.status != 201) {
    throw Error('Error create branch');
  }
  console.log(`Created branch: ${branchName}`);
  return response.json();
};

module.exports.Bitbucket = {
  createBranch,
};
