const {UserModel} = require('../models/UserModel');
const {errorHandler} = require('../utils/error-handler.util');
const ResponseBuilder = require('../utils/response-builder.util');

module.exports.handler = async (event) => {
  // const tesst =''
  try {
    const result = await UserModel.create({
      PK: '<EMAIL>',
      password: '123456',
    });
    return ResponseBuilder.ok(result);
  } catch (error) {
    return errorHandler(error);
  }
};
