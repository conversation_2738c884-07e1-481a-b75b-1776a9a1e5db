const {ResourceNotFoundError} = require('../../common/exceptions');
const {Creators} = require('../../postgres/init-models');
const {SequelizeUtil} = require('../../utils/sequelize.util');

const createCreator = async (creator) => {
  const result = await Creators.create(creator);
  return result.toJSON();
};

const getCreators = async (data) => {
  const {limit, offset, params, orderBy} = SequelizeUtil.build(data, ['name']);

  const {rows, count} = await Creators.findAndCountAll({
    attributes: {exclude: ['deleteFlag']},
    where: {
      ...params,
      deleteFlag: false,
    },
    limit,
    offset,
    order: orderBy,
    raw: true,
  });

  return {
    totalRecords: count,
    items: rows,
  };
};

const getCreator = async (id) => {
  const creator = await Creators.findOne({
    attributes: {exclude: ['deleteFlag']},
    where: {id, deleteFlag: false},
    raw: true,
  });
  if (!creator) {
    throw new ResourceNotFoundError('Creator not found');
  }

  return creator;
};

const updateCreator = async (id, creator) => {
  const result = await Creators.findOne({where: {id, deleteFlag: false}});
  if (!result) {
    throw new ResourceNotFoundError('Creator not found');
  }
  await result.update(creator);

  return result.toJSON();
};

const deleteCreator = async (id) => {
  const result = await Creators.findOne({where: {id, deleteFlag: false}});
  if (!result) {
    throw new ResourceNotFoundError('Creator not found');
  }
  await result.update({deleteFlag: true});
};

module.exports.CreatorService = {
  createCreator,
  getCreators,
  getCreator,
  updateCreator,
  deleteCreator,
};
