const {pick} = require('lodash');
const {errorHandler} = require('../../utils/error-handler.util');
const ResponseBuilder = require('../../utils/response-builder.util');
const ValidatorUtil = require('../../utils/request-validator.util');
const {CmsCreatorEndpoints, CreatorEndpoints} = require('./const');
const {CreatorService} = require('./creators.service');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      case CmsCreatorEndpoints.CREATE_CREATOR:
        data = await createCreator(event);
        break;
      case CreatorEndpoints.SEARCH_CREATOR:
        data = await searchCreators(event);
        break;
      case CreatorEndpoints.GET_CREATOR:
        data = await getCreator(event);
        break;
      case CmsCreatorEndpoints.UPDATE_CREATOR:
        data = await updateCreator(event);
        break;
      case CmsCreatorEndpoints.DELETE_CREATOR:
        data = await deleteCreator(event);
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (err) {
    return errorHandler(err);
  }
};

async function createCreator(event) {
  const requiredFields = ['name', 'description', 'position', 'avatar'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const body = JSON.parse(event.body);

  return await CreatorService.createCreator(pick(body, requiredFields));
}

async function searchCreators(event) {
  const queryParams = event.queryStringParameters;
  return await CreatorService.getCreators(queryParams);
}

async function getCreator(event) {
  const creatorId = event.pathParameters.creatorId;
  return await CreatorService.getCreator(creatorId);
}

async function updateCreator(event) {
  const creatorId = event.pathParameters.creatorId;
  const requiredFields = ['name', 'description', 'position', 'avatar'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const body = JSON.parse(event.body);

  return await CreatorService.updateCreator(creatorId, pick(body, requiredFields));
}

async function deleteCreator(event) {
  const creatorId = event.pathParameters.creatorId;
  await CreatorService.deleteCreator(creatorId);

  return {successMessage: 'Creator deleted successfully'};
}
