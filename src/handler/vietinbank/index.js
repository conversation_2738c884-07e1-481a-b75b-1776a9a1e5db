const {errorHandler} = require('../../utils/error-handler.util');
const ResponseBuilder = require('../../utils/response-builder.util');
const {VietinbankEndpoint} = require('./const');
const ValidatorUtil = require('../../utils/request-validator.util');
const {VietinbankUtil} = require('./util');
const {removeVietnameseTones} = require('../../utils/object.util');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      case VietinbankEndpoint.CREATE_DEEP_LINK:
        data = await createDeepLink(event);
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (error) {
    return errorHandler(error);
  }
};

async function createDeepLink(event) {
  const requiredFields = ['fullName', 'phoneNumber', 'identificationNumber'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const {fullName, phoneNumber, identificationNumber} = JSON.parse(event.body);
  const targetPage = 'LinkOpenCardEkyc';
  const source = 'DST';
  const timestamp = Date.now().toString();
  const name = removeVietnameseTones(fullName).replace(/\s/g, '');
  const payload = `${targetPage}${source}${timestamp}${name}${phoneNumber}${identificationNumber}`;

  const signature = VietinbankUtil.createSignature(payload);

  return `https://vietinbank.vnpay.vn/ekyc-dst-vtb?targetPage=${targetPage}&source=${source}&timestamp=${timestamp}&fullName=${name}&phoneNumber=${phoneNumber}&identificationNumber=${identificationNumber}&signature=${signature}`;
}
