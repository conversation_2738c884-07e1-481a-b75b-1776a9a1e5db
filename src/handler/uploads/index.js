const {PutObjectCommand, S3Client} = require('@aws-sdk/client-s3');
const {getSignedUrl} = require('@aws-sdk/s3-request-presigner');
const ResponseBuilder = require('../../utils/response-builder.util');
const {BadRequestError} = require('../../common/exceptions');
const {errorHandler} = require('../../utils/error-handler.util');
const ValidatorUtil = require('../../utils/request-validator.util');
const {Type} = require('./const');
const {getUserByEmail} = require('../../repository/user');

const client = new S3Client();
const s3Bucket = process.env.S3_UPLOAD_BUCKET;

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      case Type.CMS_IMAGE_UPLOAD:
      case Type.CLIENT_IMAGE_UPLOAD:
        data = await profileImgUpload(event);
        break;
      case Type.CMS_PODCAST_UPLOAD:
        data = await podcastUpload(event);
        break;
      case Type.CMS_COURSE_UPLOAD:
        data = await courseUpload(event);
        break;
      default:
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (error) {
    return errorHandler(error);
  }
};

const profileImgUpload = async (event) => {
  await ValidatorUtil.requireParams(event.body, ['srcKey']);
  const email = event.requestContext.authorizer.lambda.principalId;
  const user = await getUserByEmail(email);
  if (!user) {
    throw new BadRequestError('User not found');
  }
  const body = JSON.parse(event.body);
  const pathSplited = body.srcKey.split('/');
  const filename = pathSplited[pathSplited.length - 1];
  const path = `${process.env.CLIENT_UPLOAD_PATH}/${user.identityId}/img/${filename}`;
  const params = {
    Bucket: s3Bucket,
    Key: path,
    ContentType: 'image',
  };
  const command = new PutObjectCommand(params);

  const url = await getSignedUrl(client, command, {
    expiresIn: 300,
  });

  return {
    signedUrl: url,
    sourceUrl: `${process.env.MEDIA_DOMAIN}/${path}`,
  };
};

const podcastUpload = async (event) => {
  await ValidatorUtil.requireParams(event.body, ['srcKey']);
  const body = JSON.parse(event.body);
  const pathSplited = body.srcKey.split('/');
  const filename = pathSplited[pathSplited.length - 1]; // get file name, eg: audio.mp3
  const path = `${process.env.PODCAST_UPLOAD_PATH}/${Date.now()}/${filename}`;
  const params = {
    Bucket: s3Bucket,
    Key: path,
    ContentType: 'audio/mpeg',
  };
  const command = new PutObjectCommand(params);

  const url = await getSignedUrl(client, command, {
    expiresIn: 300,
  });

  return {
    signedUrl: url,
    sourceUrl: `${process.env.MEDIA_DOMAIN}/${path.split('.')[0]}.m3u8`,
  };
};

const courseUpload = async (event) => {
  await ValidatorUtil.requireParams(event.body, ['srcKey']);
  const body = JSON.parse(event.body);
  const pathSplited = body.srcKey.split('/');
  const filename = pathSplited[pathSplited.length - 1]; // get file name
  const path = `${process.env.COURSE_UPLOAD_PATH}/${Date.now()}/${filename}`;
  const params = {
    Bucket: s3Bucket,
    Key: path,
    ContentType: 'video/mp4',
  };
  const command = new PutObjectCommand(params);

  const url = await getSignedUrl(client, command, {
    expiresIn: 300,
  });

  return {
    signedUrl: url,
    sourceUrl: `${process.env.MEDIA_DOMAIN}/${path.split('.')[0]}.m3u8`,
  };
};
