// dependencies
const {GetObjectCommand, PutObjectCommand, S3Client} = require('@aws-sdk/client-s3');

const {Readable} = require('stream');

const sharp = require('sharp');
const util = require('util');
const path = require('path');

// create S3 client
const s3 = new S3Client();

// define the handler function
module.exports.handler = async (event, context) => {
  try {
    // Read options from the event parameter and get the source bucket
    console.log('Reading options from event:\n', util.inspect(event, {depth: 5}));
    const srcBucket = event.Records[0].s3.bucket.name;

    // Object key may have spaces or unicode non-ASCII characters
    const srcKey = decodeURIComponent(event.Records[0].s3.object.key.replace(/\+/g, ' '));

    const imageExt = path.extname(srcKey).toLowerCase();
    if (!['.png', '.jpeg', '.jpg'].includes(imageExt)) {
      return;
    }

    const dstBucket = srcBucket;
    const dstKey = 'thumbnails/' + src<PERSON>ey;

    // Get the image from the source bucket. GetObjectCommand returns a stream.
    const params = {
      Bucket: srcBucket,
      Key: srcKey,
    };
    const response = await s3.send(new GetObjectCommand(params));
    const stream = response.Body;

    // Convert stream to buffer to pass to sharp resize function.
    if (stream instanceof Readable) {
      const contentBuffer = Buffer.concat(await stream.toArray());

      // Use the sharp module to resize the image and save in a buffer.
      const outputBuffer = await resizeBufferByFileType(imageExt, contentBuffer);

      // Upload the thumbnail image to the destination bucket
      try {
        const destparams = {
          Bucket: dstBucket,
          Key: dstKey,
          Body: outputBuffer,
        };

        await s3.send(new PutObjectCommand(destparams));
      } catch (error) {
        console.log(error);
        return;
      }

      console.log(
        `Successfully resized ${srcBucket + srcKey} and uploaded to ${dstBucket}/${dstKey}`,
      );
    } else {
      throw new Error('Unknown object stream type');
    }
  } catch (err) {
    console.error(
      `An exception occurred, investigate and configure retry strategy. Error: ${JSON.stringify(
        err,
      )}`,
    );
    return;
  }
};

async function resizeBufferByFileType(imageExt, contentBuffer) {
  // set thumbnail width. Resize will set the height automatically to maintain aspect ratio.
  const width = parseInt(process.env.WIDTH);

  // Use the sharp module to resize the image and save in a buffer.
  if (['.png'].includes(imageExt)) {
    return await sharp(contentBuffer).resize(width).png().toBuffer();
  } else {
    return await sharp(contentBuffer).resize(width).jpeg().toBuffer();
  }
}
