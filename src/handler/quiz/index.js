const {errorHandler} = require('../../utils/error-handler.util');
const ResponseBuilder = require('../../utils/response-builder.util');
const {QuizEndpoints, CmsQuizEndpoints} = require('./const');
const {QuizService} = require('./quiz.service');
const ValidatorUtil = require('../../utils/request-validator.util');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      // course categories
      case QuizEndpoints.GET_QUIZ:
        data = await getQuiz(event);
        break;
      case QuizEndpoints.SUBMIT_QUIZ:
        data = await submitQuiz(event);
        break;
      case CmsQuizEndpoints.CREATE_QUIZ:
        data = await createQuiz(event);
        break;
      case CmsQuizEndpoints.UPDATE_QUIZ:
        data = await updateQuiz(event);
        break;
      case CmsQuizEndpoints.DELETE_QUIZ:
        data = await deleteQuiz(event);
        break;
      case CmsQuizEndpoints.CREATE_QUESTIONS:
        data = await createQuestions(event);
        break;
      case QuizEndpoints.GET_QUIZ_RESULTS:
        data = await getQuizResults(event);
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (err) {
    return errorHandler(err);
  }
};

async function createQuiz(event) {
  const requiredFields = ['name'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const body = JSON.parse(event.body);
  await QuizService.createQuiz(body);

  return {
    successMessage: 'Tạo bài kiểm tra thành công',
  };
}

async function updateQuiz(event) {
  const quizId = event.pathParameters.quizId;
  const requiredFields = ['name'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const body = JSON.parse(event.body);
  await QuizService.updateQuiz(quizId, body);

  return {
    successMessage: 'Cập nhật bài kiểm tra thành công',
  };
}

async function deleteQuiz(event) {
  const quizId = event.pathParameters.quizId;
  await QuizService.removeQuiz(quizId);
  return {
    successMessage: 'Xoá bài kiểm tra thành công',
  };
}

async function createQuestions(event) {
  const quizId = event.pathParameters.quizId;
  await ValidatorUtil.requireParams(event.body, ['questions']);
  const body = JSON.parse(event.body);
  for (const [index, question] of Object.entries(body.questions)) {
    const requiredFields = ['text', 'answers', 'correctAnswer'];
    await ValidatorUtil.requireParams(JSON.stringify(question), requiredFields, index);
  }
  await QuizService.createQuestions(quizId, body.questions);

  return {
    successMessage: 'Tạo câu hỏi thành công',
  };
}

async function getQuiz(event) {
  const quizId = event.pathParameters.quizId;

  return await QuizService.getQuiz(quizId);
}

async function submitQuiz(event) {
  const userId = event.requestContext.authorizer.lambda.principalId;
  const quizId = event.pathParameters.quizId;

  // validate body - required anwsers, timeSpend
  const requiredFields = ['answers', 'timeSpend'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const body = JSON.parse(event.body);
  for (const [index, answer] of Object.entries(body.answers)) {
    await ValidatorUtil.requireParams(JSON.stringify(answer), ['questionId', 'answer'], index);
  }

  return await QuizService.calculateScore(userId, quizId, body);
}

async function getQuizResults(event) {
  const userId = event.requestContext.authorizer.lambda.principalId;
  const quizType = event.pathParameters.quizType;

  return await QuizService.getQuizResults(userId, quizType);
}
