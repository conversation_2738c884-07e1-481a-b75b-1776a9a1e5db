const moment = require('moment');
const {Quiz, Questions, QuizResult} = require('../../postgres/init-models');
const {ResourceNotFoundError, BadRequestError} = require('../../common/exceptions');
const ErrorCode = require('../../constants/error-code');

const createQuiz = async (data) => {
  if (data.startTime && data.endTime) {
    // using moment to calculate duration
    const startTime = moment(data.startTime);
    const endTime = moment(data.endTime);
    data.duration = endTime.diff(startTime, 'minutes');
  }
  await Quiz.create(data);
};

// TODO:
const updateQuiz = async (quizId, data) => {
  if (data.startTime && data.endTime) {
    // using moment to calculate duration
    const startTime = moment(data.startTime);
    const endTime = moment(data.endTime);
    data.duration = endTime.diff(startTime, 'minutes');
  }
  await Quiz.update(data, {
    where: {
      id: quizId,
    },
  });
};

const removeQuiz = async (quizId) => {
  await Quiz.update(
    {deleteFlag: true},
    {
      where: {
        id: quizId,
      },
    },
  );
};

const getQuiz = async (quizId) => {
  const result = await Quiz.findOne({
    attributes: ['id', 'name', 'duration', 'startTime', 'endTime'],
    where: {
      id: quizId,
      deleteFlag: false,
    },
    include: [
      {
        model: Questions,
        as: 'questions',
        required: false,
        attributes: ['id', 'text', 'answers'],
      },
    ],
  });

  if (!result) {
    throw new ResourceNotFoundError('Bài kiểm tra không tồn tại');
  }

  const quiz = result.toJSON();
  // if startTime is over now, throw error
  if (quiz.startTime && moment(quiz.startTime).isAfter(moment())) {
    throw new BadRequestError({
      code: ErrorCode.Quiz.QUIZ_NOT_STARTED,
      message: 'Bài kiểm tra chưa bắt đầu',
      quiz: {...quiz, questions: undefined},
    });
  }
  // if endTime is over now, throw error
  if (quiz.endTime && moment(quiz.endTime).isBefore(moment())) {
    throw new BadRequestError({
      code: ErrorCode.Quiz.QUIZ_EXPIRED,
      message: 'Đã hết thời gian làm bài',
      quiz: {...quiz, questions: undefined},
    });
  }

  return quiz;
};

const createQuestions = async (quizId, questions) => {
  const quiz = await Quiz.findByPk(quizId);
  if (!quiz) {
    throw new ResourceNotFoundError('Bài kiểm tra không tồn tại');
  }

  const questionData = questions.map((question) => ({
    quizId: quizId,
    text: question.text,
    answers: question.answers,
    correctAnswer: question.correctAnswer,
  }));

  await Questions.bulkCreate(questionData);
};

const calculateScore = async (userId, quizId, data) => {
  const {answers, timeSpend} = data;
  const quiz = await Quiz.findByPk(quizId);
  if (!quiz) {
    throw new ResourceNotFoundError('Bài kiểm tra không tồn tại');
  }

  // if endTime is over now, throw error
  if (quiz.endTime && moment(quiz.endTime).add(30, 'seconds').isBefore(moment())) {
    throw new BadRequestError({
      code: ErrorCode.Quiz.QUIZ_EXPIRED,
      message: 'Đã hết thời gian làm bài',
      quiz: {...quiz.dataValues, questions: undefined},
    });
  }

  const questions = await Questions.findAll({
    where: {
      quizId: quizId,
    },
    raw: true,
  });

  let correctAnswers = 0;
  for (const question of questions) {
    const {answer} = answers.find((item) => item.questionId === question.id) || {};
    if (!answer) {
      continue;
    }
    if (answer === question.correctAnswer.value) {
      correctAnswers++;
    }
  }
  // calculate score
  const score = Math.round((correctAnswers / questions.length) * 100);

  // store test result
  await QuizResult.create({
    userId,
    quizId,
    score,
    totalAnswers: questions.length,
    totalCorrectAnswer: correctAnswers,
    timeSpend,
  });

  return {
    quizId: quizId,
    score,
    pass: score >= quiz.passScore,
    totalQuestions: questions.length,
    correctAnswers: correctAnswers,
  };
};

const getQuizResults = async (userId, quizType) => {
  // lay thong tin ket qua quiz cua user theo quizType trong bang quiz-result
  // join voi table quiz de lay thong tin bai test
  const quizResults = await QuizResult.findAll({
    attributes: ['id', 'score', 'totalAnswers', 'totalCorrectAnswer', 'timeSpend', 'submittedAt'],
    where: {
      userId,
    },
    include: [
      {
        model: Quiz,
        as: 'quiz',
        attributes: ['id', 'name'],
        where: {
          type: quizType,
        },
      },
    ],
    nest: true,
    raw: true,
  });

  return quizResults;
};

module.exports.QuizService = {
  createQuiz,
  updateQuiz,
  removeQuiz,
  getQuiz,
  createQuestions,
  calculateScore,
  getQuizResults,
};
