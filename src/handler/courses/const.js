const CourseEndpoints = {
  SEARCH_COURSE: 'GET /v1/courses',
  HOME_CONFIG: 'GET /v1/courses/home',
  GET_COURSE: 'GET /v1/courses/{courseId}',
  GET_ALL_CATEGORY: 'GET /v1/courses/categories',
};

const CmsCourseEndpoints = {
  CREATE_COURSE: 'POST /v1/cms/courses',
  SEARCH_COURSE: 'GET /v1/cms/courses',
  GET_COURSE: 'GET /v1/cms/courses/{courseId}',
  UPDATE_COURSE: 'PUT /v1/cms/courses/{courseId}',
  DELETE_COURSE: 'DELETE /v1/cms/courses/{courseId}',
  // course categories
  CREATE_CATEGORY: 'POST /v1/cms/courses/categories',
  GET_ALL_CATEGORY: 'GET /v1/cms/courses/categories',
  GET_CATEGORY: 'GET /v1/cms/courses/categories/{categoryId}',
  UPDATE_CATEGORY: 'PUT /v1/cms/courses/categories/{categoryId}',
  DELETE_CATEGORY: 'DELETE /v1/cms/courses/categories/{categoryId}',
};

const CmsLessonEndpoints = {
  CREATE_LESSON: 'POST /v1/cms/courses/{courseId}/lessons',
  SEARCH_LESSON: 'GET /v1/cms/courses/lessons',
  GET_LESSON: 'GET /v1/cms/courses/lessons/{lessonId}',
  UPDATE_LESSON: 'PUT /v1/cms/courses/lessons/{lessonId}',
  DELETE_LESSON: 'DELETE /v1/cms/courses/lessons/{lessonId}',
};

module.exports = {CourseEndpoints, CmsCourseEndpoints, CmsLessonEndpoints};
