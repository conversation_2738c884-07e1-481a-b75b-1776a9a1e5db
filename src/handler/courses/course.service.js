const {orderBy} = require('lodash');
const {BadRequestError, ResourceNotFoundError} = require('../../common/exceptions');
const {ConfigModel} = require('../../models/ConfigModel');
const {Courses, Creators, CourseCategories, Lessons} = require('../../postgres/init-models');
const {SequelizeUtil} = require('../../utils/sequelize.util');
const {Op} = require('sequelize');
const {getSectionItems} = require('../../repository/blog');

const createCategory = async (category) => {
  const result = await CourseCategories.create(category);
  return result.toJSON();
};

const getAllCategories = async () => {
  return await CourseCategories.findAll({
    where: {deleteFlag: false},
    raw: true,
  });
};

const getCategoryById = async (id) => {
  const category = await CourseCategories.findByPk(id, {raw: true});
  if (!category || !!category.deleteFlag) {
    throw new ResourceNotFoundError('Category not found');
  }

  return category;
};

const updateCategory = async (id, category) => {
  const result = await CourseCategories.findByPk(id);
  if (!result || !!result.deleteFlag) {
    throw new ResourceNotFoundError('Category not found');
  }

  await result.update(category);

  return result.toJSON();
};

const deleteCategory = async (id) => {
  const category = await CourseCategories.findByPk(id);
  if (!category || !!category.deleteFlag) {
    throw new ResourceNotFoundError('Category not found');
  }

  await category.update({deleteFlag: true});
};

const _validateCourse = async (course) => {
  const creator = await Creators.findOne({
    where: {id: course.creatorId, deleteFlag: false},
  });
  if (!creator) {
    throw new BadRequestError({
      code: 111003,
      message: 'Creator not found',
    });
  }
  const category = await CourseCategories.findOne({
    where: {id: course.categoryId, deleteFlag: false},
  });
  if (!category) {
    throw new BadRequestError({
      code: 111004,
      message: 'Category not found',
    });
  }
};

const createCourse = async (course) => {
  await _validateCourse(course);
  const result = await Courses.create(course);

  return result.toJSON();
};

const searchCourses = async (data) => {
  const {limit, offset, params, orderBy} = SequelizeUtil.build(data, ['name']);

  const {rows, count} = await Courses.findAndCountAll({
    attributes: {
      exclude: ['deleteFlag', 'description', 'categoryId', 'creatorId'],
    },
    where: {
      ...params,
      deleteFlag: false,
    },
    include: [
      {
        model: Creators,
        as: 'creator',
        required: true,
        attributes: ['id', 'name', 'avatar', 'position'],
      },
      {
        model: CourseCategories,
        as: 'category',
        required: true,
        attributes: ['id', 'name'],
      },
    ],
    limit,
    offset,
    order: orderBy,
    raw: true,
    nest: true,
  });

  return {
    totalRecords: count,
    items: rows,
  };
};

const getCourse = async (id) => {
  const course = await Courses.findOne({
    attributes: {exclude: ['deleteFlag', 'categoryId', 'creatorId']},
    where: {id, deleteFlag: false},
    include: [
      {
        model: Creators,
        as: 'creator',
        required: true,
        attributes: ['id', 'name', 'avatar', 'position'],
        where: {deleteFlag: false},
      },
      {
        model: CourseCategories,
        as: 'category',
        required: true,
        attributes: ['id', 'name'],
        where: {deleteFlag: false},
      },
    ],
    raw: true,
    nest: true,
  });
  if (!course) {
    throw new ResourceNotFoundError('Course not found');
  }

  return course;
};

const updateCourse = async (id, course) => {
  const result = await Courses.findByPk(id);
  if (!result || !!result.deleteFlag) {
    throw new ResourceNotFoundError('Course not found');
  }

  await _validateCourse(course);
  await result.update(course);

  return result.toJSON();
};

const deleteCourse = async (id) => {
  const course = await Courses.findByPk(id);
  if (!course || !!course.deleteFlag) {
    throw new ResourceNotFoundError('Course not found');
  }

  await course.update({deleteFlag: true});
};

const getCourseClient = async (id) => {
  const course = await Courses.findOne({
    attributes: {exclude: ['deleteFlag', 'categoryId', 'creatorId']},
    where: {id, deleteFlag: false},
    include: [
      {
        model: Lessons,
        as: 'lessons',
        required: true,
        attributes: ['id', 'name', 'duration', 'mediaUrl', 'mediaType', 'section'],
        where: {deleteFlag: false},
      },
      {
        model: Creators,
        as: 'creator',
        required: true,
        attributes: ['id', 'name', 'avatar', 'position'],
        where: {deleteFlag: false},
      },
      {
        model: CourseCategories,
        as: 'category',
        required: true,
        attributes: ['id', 'name'],
        where: {deleteFlag: false},
      },
    ],
    nest: true,
  });

  if (!course) {
    throw new ResourceNotFoundError('Course not found');
  }
  const result = course.toJSON();
  result.summary = {
    duration: result.lessons.reduce((total, lesson) => total + lesson.duration, 0),
    totalLessons: result.lessons.length,
  };

  return result;
};

const getHomeConfig = async () => {
  const configs = await ConfigModel.query('PK')
    .eq('COURSEPAGE')
    .where('display')
    .eq(true)
    .all()
    .exec()
    .then((res) => res.toJSON())
    .then((configs) => orderBy(configs, ['sort'], ['asc']));

  for (const config of configs) {
    const items = orderBy(config.items, ['position'], ['asc']);
    delete config.items;
    if (config.position !== 'SECTION_COURSE') {
      if (config.position !== 'BUTTON') config.content = await getSectionItems(items);
      continue;
    }

    const courseIds = items.map((item) => item.PK);
    const courses = await Courses.findAll({
      attributes: ['id', 'name', 'shortDescription', 'banner', 'price'],
      where: {id: {[Op.in]: courseIds}, deleteFlag: false},
      include: [
        {
          model: Creators,
          as: 'creator',
          required: true,
          attributes: ['id', 'name', 'avatar', 'position'],
          where: {deleteFlag: false},
        },
      ],
      raw: true,
      nest: true,
    });
    config.content = courses.map((course) => ({
      ...course,
      sort: courseIds.indexOf(`${course.id}`) + 1,
    }));
  }

  return configs;
};

module.exports.CourseService = {
  createCategory,
  getAllCategories,
  getCategoryById,
  updateCategory,
  deleteCategory,
  createCourse,
  searchCourses,
  getCourse,
  updateCourse,
  deleteCourse,
  getCourseClient,
  getHomeConfig,
};
