const {pick} = require('lodash');
const {errorHandler} = require('../../utils/error-handler.util');
const ResponseBuilder = require('../../utils/response-builder.util');
const ValidatorUtil = require('../../utils/request-validator.util');
const {CmsLessonEndpoints} = require('./const');
const {LessonService} = require('./lesson.service');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      case CmsLessonEndpoints.CREATE_LESSON:
        data = await createLesson(event);
        break;
      case CmsLessonEndpoints.UPDATE_LESSON:
        data = await updateLesson(event);
        break;
      case CmsLessonEndpoints.GET_LESSON:
        data = await getLessonById(event);
        break;
      case CmsLessonEndpoints.DELETE_LESSON:
        data = await deleteLesson(event);
        break;
      case CmsLessonEndpoints.SEARCH_LESSON:
        data = await searchLessons(event);
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (err) {
    return errorHandler(err);
  }
};

async function createLesson(event) {
  const requiredFields = ['name', 'duration', 'mediaUrl', 'mediaType'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const courseId = event.pathParameters.courseId;
  const body = JSON.parse(event.body);

  return await LessonService.createLesson({
    ...pick(body, [...requiredFields, 'section']),
    courseId,
  });
}

const updateLesson = async (event) => {
  const requiredFields = ['courseId', 'name', 'duration', 'mediaUrl', 'mediaType'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const lessonId = event.pathParameters.lessonId;
  const body = JSON.parse(event.body);

  return await LessonService.updateLesson(lessonId, pick(body, [...requiredFields, 'section']));
};

const getLessonById = async (event) => {
  const lessonId = event.pathParameters.lessonId;
  return await LessonService.getLessonById(lessonId);
};

const deleteLesson = async (event) => {
  const lessonId = event.pathParameters.lessonId;
  await LessonService.deleteLesson(lessonId);

  return {successMessage: 'Lesson deleted successfully'};
};

const searchLessons = async (event) => {
  const queryParams = event.queryStringParameters;
  return await LessonService.searchLessons(queryParams);
};
