const {pick} = require('lodash');
const {errorHandler} = require('../../utils/error-handler.util');
const ResponseBuilder = require('../../utils/response-builder.util');
const ValidatorUtil = require('../../utils/request-validator.util');
const {CmsCourseEndpoints, CourseEndpoints} = require('./const');
const {CourseService} = require('./course.service');
const {BadRequestError} = require('../../common/exceptions');

module.exports.handler = async (event) => {
  const routeKey = event.routeKey;
  try {
    let data;
    switch (routeKey) {
      // course categories
      case CmsCourseEndpoints.CREATE_CATEGORY:
        data = await createCategory(event);
        break;
      case CmsCourseEndpoints.GET_ALL_CATEGORY:
        data = await getAllCategories(event);
        break;
      case CmsCourseEndpoints.GET_CATEGORY:
        data = await getCategoryById(event);
        break;
      case CmsCourseEndpoints.UPDATE_CATEGORY:
        data = await updateCategory(event);
        break;
      case CmsCourseEndpoints.DELETE_CATEGORY:
        data = await deleteCategory(event);
        break;
      case CmsCourseEndpoints.CREATE_COURSE:
        data = await createCourse(event);
        break;
      case CmsCourseEndpoints.SEARCH_COURSE:
        data = await searchCourse(event);
        break;
      case CmsCourseEndpoints.GET_COURSE:
        data = await getCourse(event);
        break;
      case CmsCourseEndpoints.UPDATE_COURSE:
        data = await updateCourse(event);
        break;
      case CmsCourseEndpoints.DELETE_COURSE:
        data = await deleteCourse(event);
        break;
      case CourseEndpoints.SEARCH_COURSE:
        data = await searchCourse(event);
        break;
      case CourseEndpoints.GET_COURSE:
        data = await getCourseClient(event);
        break;
      case CourseEndpoints.HOME_CONFIG:
        data = await CourseService.getHomeConfig();
        break;
      case CourseEndpoints.GET_ALL_CATEGORY:
        data = await CourseService.getAllCategories().then((items) =>
          items.map((item) => ({id: item.id, name: item.name})),
        );
        break;
    }

    return ResponseBuilder.ok(data);
  } catch (err) {
    return errorHandler(err);
  }
};

async function createCategory(event) {
  const requiredFields = ['name', 'description'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  return await CourseService.createCategory(JSON.parse(event.body));
}

async function getAllCategories(event) {
  return await CourseService.getAllCategories();
}

async function getCategoryById(event) {
  const {categoryId} = event.pathParameters;
  return await CourseService.getCategoryById(categoryId);
}

async function updateCategory(event) {
  const {categoryId} = event.pathParameters;
  const requiredFields = ['name', 'description'];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  return await CourseService.updateCategory(categoryId, JSON.parse(event.body));
}

async function deleteCategory(event) {
  const {categoryId} = event.pathParameters;
  await CourseService.deleteCategory(categoryId);

  return {successMessage: 'Delete category successfully'};
}

async function createCourse(event) {
  const requiredFields = [
    'creatorId',
    'categoryId',
    'name',
    'description',
    'shortDescription',
    'banner',
    'price',
  ];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const body = JSON.parse(event.body);
  if (body.price < 0) {
    throw new BadRequestError({
      code: 111002,
      message: 'Price must be greater than or equal to 0',
    });
  }

  return await CourseService.createCourse(pick(body, requiredFields));
}

async function searchCourse(event) {
  const queryParams = event.queryStringParameters;
  return await CourseService.searchCourses(queryParams);
}

async function getCourse(event) {
  const {courseId} = event.pathParameters;
  return await CourseService.getCourse(courseId);
}

async function updateCourse(event) {
  const {courseId} = event.pathParameters;
  const requiredFields = [
    'creatorId',
    'categoryId',
    'name',
    'description',
    'shortDescription',
    'banner',
    'price',
  ];
  await ValidatorUtil.requireParams(event.body, requiredFields);

  const body = JSON.parse(event.body);
  if (body.price < 0) {
    throw new BadRequestError({
      code: 111002,
      message: 'Price must be greater than or equal to 0',
    });
  }

  return await CourseService.updateCourse(courseId, pick(body, requiredFields));
}

async function deleteCourse(event) {
  const {courseId} = event.pathParameters;
  await CourseService.deleteCourse(courseId);

  return {successMessage: 'Delete course successfully'};
}

async function getCourseClient(event) {
  const {courseId} = event.pathParameters;
  return await CourseService.getCourseClient(courseId);
}
