const {BadRequestError, ResourceNotFoundError} = require('../../common/exceptions');
const {Lessons, Courses} = require('../../postgres/init-models');
const {SequelizeUtil} = require('../../utils/sequelize.util');

const _validateLesson = async (lesson) => {
  const course = await Courses.findOne({
    where: {id: lesson.courseId, deleteFlag: false},
  });
  if (!course) {
    throw new BadRequestError({
      code: 111004,
      message: 'Course not found',
    });
  }
};

const createLesson = async (lesson) => {
  await _validateLesson(lesson);
  const result = await Lessons.create(lesson);
  return result.toJSON();
};

const updateLesson = async (id, lesson) => {
  await _validateLesson(lesson);
  const result = await Lessons.findByPk(id);
  if (!result || !!result.deleteFlag) {
    throw new ResourceNotFoundError('Lesson not found');
  }

  await result.update(lesson);

  return result.toJSON();
};

const getLessonById = async (id) => {
  const lesson = await Lessons.findOne({
    where: {id, deleteFlag: false},
    raw: true,
  });
  if (!lesson) {
    throw new ResourceNotFoundError('Lesson not found');
  }

  return lesson;
};

const deleteLesson = async (id) => {
  const lesson = await Lessons.findByPk(id);
  if (!lesson || !!lesson.deleteFlag) {
    throw new ResourceNotFoundError('Lesson not found');
  }

  await lesson.update({deleteFlag: true});
};

const searchLessons = async (data) => {
  const {limit, offset, params, orderBy} = SequelizeUtil.build(data, ['name']);

  const {rows, count} = await Lessons.findAndCountAll({
    attributes: {exclude: ['deleteFlag']},
    where: {
      ...params,
      deleteFlag: false,
    },
    limit,
    offset,
    order: orderBy,
    raw: true,
  });

  return {
    totalRecords: count,
    items: rows,
  };
};

module.exports.LessonService = {
  createLesson,
  updateLesson,
  getLessonById,
  deleteLesson,
  searchLessons,
};
