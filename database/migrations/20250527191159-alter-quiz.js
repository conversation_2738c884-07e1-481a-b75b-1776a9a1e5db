'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.addColumn('quiz', 'type', {
      type: Sequelize.STRING(30),
      allowNull: false,
      defaultValue: 'course_test',
    });
    await queryInterface.addColumn('quiz', 'passScore', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 100,
    });
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.removeColumn('quiz', 'type');
    await queryInterface.removeColumn('quiz', 'passScore');
  },
};
