'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.createTable('lessons', {
      id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      courseId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'courses',
          key: 'id',
        },
        allowNull: false,
      },
      name: {
        type: Sequelize.STRING(500),
        allowNull: false,
      },
      duration: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      mediaUrl: {
        type: Sequelize.STRING(1000),
        allowNull: false,
      },
      mediaType: {
        type: Sequelize.STRING(30),
        allowNull: false,
      },
      section: {
        type: Sequelize.STRING(250),
        allowNull: true,
      },
      deleteFlag: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });

    // create indexs
    const indexs = [
      {
        name: 'lessons_name_index',
        fields: ['name'],
      },
    ];
    const promises = indexs.map((index) => queryInterface.addIndex('lessons', index));
    await Promise.all(promises);
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.dropTable('lessons');
  },
};
