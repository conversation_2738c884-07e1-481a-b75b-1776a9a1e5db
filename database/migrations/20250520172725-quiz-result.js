'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.createTable('quiz-result', {
      id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      userId: {
        type: Sequelize.STRING(500),
        allowNull: false,
      },
      quizId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'quiz',
          key: 'id',
        },
        allowNull: false,
      },
      score: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      totalAnswers: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      totalCorrectAnswer: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      submittedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // create indexs
    const indexs = [
      {
        name: 'user_id_index',
        fields: ['userId'],
      },
      {
        name: 'quiz_id_index',
        fields: ['quizId'],
      },
      {
        name: 'user_id_quiz_id_index',
        fields: ['userId', 'quizId'],
      },
    ];
    const promises = indexs.map((index) => queryInterface.addIndex('quiz-result', index));
    await Promise.all(promises);
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.dropTable('quiz-result');
  },
};
