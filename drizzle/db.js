const {drizzle} = require('drizzle-orm/postgres-js');
const postgres = require('postgres');

// Disable prefetch as it is not supported for "Transaction" pool mode
const createConnection = () => {
  return postgres(
    `postgresql://${process.env.DB_USERNAME}:${process.env.DB_PASSWORD}@${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_NAME}`,
    {prepare: false},
  );
};

const withConnection = async (operation) => {
  const client = createConnection();
  const db = drizzle(client);
  try {
    const result = await operation(db);
    return result;
  } finally {
    await client.end();
  }
};

module.exports = {
  withConnection,
};
