const {withConnection} = require('../db');
const schema = require('../schema');
const {eq, and, sql, count, inArray} = require('drizzle-orm');
const {ticketStatus} = require('../../src/handler/cms/ticket/const');

const createTicketClass = async (data) => {
  return withConnection(async (db) => {
    const result = await db
      .insert(schema.ticketClass)
      .values(data)
      .returning({insertedId: schema.ticketClass.id});
    return result;
  });
};

const getTicketClasses = async ({eventId, calendarId}) => {
  return withConnection(async (db) => {
    const result = await db
      .select({
        ...schema.ticketClass,
        countTickets: sql`COUNT(DISTINCT ${schema.tickets.id})`.as('count_tickets'),
        soldTickets: sql`COUNT(
        DISTINCT CASE WHEN ${schema.tickets.status} = ${ticketStatus.SOLD} THEN ${schema.tickets.id} ELSE NULL END
      )`,
        blockedTickets: sql`COUNT(
        DISTINCT CASE WHEN ${schema.tickets.status} = ${ticketStatus.BLOCK} THEN ${schema.tickets.id} ELSE NULL END
      )`,
      })
      .from(schema.ticketClass)
      .leftJoin(schema.tickets, eq(schema.ticketClass.id, schema.tickets.ticketClassId))
      .where(
        and(eq(schema.ticketClass.eventId, eventId), eq(schema.ticketClass.calendarId, calendarId)),
      )
      .groupBy(schema.ticketClass.id);
    return result;
  });
};

const deleteTicketClass = async ({ticketClassId, eventId, calendarId}) => {
  return withConnection(async (db) => {
    const result = await db
      .delete(schema.ticketClass)
      .where(
        and(
          eq(schema.ticketClass.id, ticketClassId),
          eq(schema.ticketClass.eventId, eventId),
          eq(schema.ticketClass.calendarId, calendarId),
        ),
      )
      .returning();
    return result;
  });
};
const getTicketClassesByEventIds = async ({eventId}) => {
  return withConnection(async (db) => {
    const result = await db
      .select({
        ...schema.ticketClass,
        openTickets: sql`COUNT(
        DISTINCT CASE WHEN ${schema.tickets.status} = ${ticketStatus.IDLE} THEN ${schema.tickets.id} ELSE NULL END
      )`,
        countTickets: sql`COUNT(DISTINCT ${schema.tickets.id})`.as('count_tickets'),
        soldTickets: sql`COUNT(
        DISTINCT CASE WHEN ${schema.tickets.status} = ${ticketStatus.SOLD} THEN ${schema.tickets.id} ELSE NULL END
      )`,
        blockedTickets: sql`COUNT(
        DISTINCT CASE WHEN ${schema.tickets.status} = ${ticketStatus.BLOCK} THEN ${schema.tickets.id} ELSE NULL END
      )`,
      })
      .from(schema.ticketClass)
      .leftJoin(schema.tickets, eq(schema.ticketClass.id, schema.tickets.ticketClassId))
      .where(and(eq(schema.ticketClass.eventId, eventId)))
      .groupBy(schema.ticketClass.id);
    return result;
  });
};

const getTicketClassesByEvent = async (eventId) => {
  return withConnection(async (db) => {
    const result = await db
      .select({
        ...schema.ticketClass,
        countTickets: sql`COUNT(DISTINCT ${schema.tickets.id})`.as('count_tickets'),
        soldTickets: sql`COUNT(
        DISTINCT CASE WHEN ${schema.tickets.status} = ${ticketStatus.SOLD} THEN ${schema.tickets.id} ELSE NULL END
      )`,
        blockedTickets: sql`COUNT(
        DISTINCT CASE WHEN ${schema.tickets.status} = ${ticketStatus.BLOCK} THEN ${schema.tickets.id} ELSE NULL END
      )`,
      })
      .from(schema.ticketClass)
      .leftJoin(schema.tickets, eq(schema.ticketClass.id, schema.tickets.ticketClassId))
      .where(eq(schema.ticketClass.eventId, eventId))
      .groupBy(schema.ticketClass.id);
    return result;
  });
};

const getTicketClassesById = async (ticketClassId) => {
  return withConnection(async (db) => {
    const result = await db
      .select({
        ...schema.ticketClass,
      })
      .from(schema.ticketClass)
      .where(eq(schema.ticketClass.id, ticketClassId));
    return result;
  });
};
const getTicketClassesByIds = async (ticketClassIds) => {
  return withConnection(async (db) => {
    const result = await db
      .select({
        ...schema.ticketClass,
      })
      .from(schema.ticketClass)
      .where(inArray(schema.ticketClass.id, ticketClassIds));
    return result;
  });
};

const getTicketClassesByEventId = async (eventId) => {
  return withConnection(async (db) => {
    const result = await db
      .select({
        ...schema.ticketClass,
      })
      .from(schema.ticketClass)
      .where(eq(schema.ticketClass.eventId, eventId));
    return result;
  });
};

const updateTicketClass = async (data) => {
  return withConnection(async (db) => {
    const result = await db
      .update(schema.ticketClass)
      .set(data)
      .where(
        and(
          eq(schema.ticketClass.id, data.ticketClassId),
          eq(schema.ticketClass.eventId, data.eventId),
          eq(schema.ticketClass.calendarId, data.calendarId),
        ),
      )
      .returning();
    return result;
  });
};

const getTicketClassByOrderId = async (orderId) => {
  return withConnection(async (db) => {
    const result = await db
      .select({
        id: schema.ticketClass.id,
        calendar: schema.ticketClass.calendarId,
        eventId: schema.ticketClass.eventId,
        finalPriceVn: schema.ticketClass.finalPriceVn,
        finalPriceUsd: schema.ticketClass.finalPriceUsd,
        quantity: count(schema.tickets.id),
      })
      .from(schema.tickets)
      .leftJoin(schema.ticketClass, eq(schema.tickets.ticketClassId, schema.ticketClass.id))
      .where(eq(schema.tickets.orderId, orderId))
      .groupBy(schema.ticketClass.id);
    return result;
  });
};

module.exports = {
  createTicketClass,
  getTicketClasses,
  deleteTicketClass,
  getTicketClassesByEventIds,
  getTicketClassesByEventId,
  getTicketClassesByEvent,
  getTicketClassesById,
  getTicketClassByOrderId,
  updateTicketClass,
  getTicketClassesByIds,
};
