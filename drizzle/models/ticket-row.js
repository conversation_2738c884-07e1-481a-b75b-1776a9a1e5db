const {withConnection} = require('../db');
const schema = require('../schema');
const {eq, and, sql, desc, ne} = require('drizzle-orm');
const {ticketStatus} = require('../../src/handler/cms/ticket/const');

const createTicketRow = async (data) => {
  return withConnection(async (db) => {
    const result = await db
      .insert(schema.rows)
      .values(data)
      .returning({insertedId: schema.rows.id});
    return result;
  });
};

const getRowsByZone = async (data) => {
  return withConnection(async (db) => {
    const result = await db
      .select({
        ...schema.rows,
        countTickets: sql`COUNT(DISTINCT ${schema.tickets.id})`.as('count_tickets'),
        countSoldTickets: sql`COUNT(
        DISTINCT CASE WHEN ${schema.tickets.status} = ${ticketStatus.SOLD} THEN ${schema.tickets.id} ELSE NULL END
      )`,
      })
      .from(schema.rows)
      .leftJoin(schema.tickets, eq(schema.rows.id, schema.tickets.rowId))
      .where(
        and(
          eq(schema.rows.calendarId, data.calendarId),
          eq(schema.rows.eventId, data.eventId),
          eq(schema.rows.zoneId, data.zoneId),
        ),
      )
      .groupBy(schema.rows.id);
    return result;
  });
};

const deleteRow = async ({rowId, calendarId, eventId}) => {
  return withConnection(async (db) => {
    const result = await db
      .delete(schema.rows)
      .where(
        and(
          eq(schema.rows.id, rowId),
          eq(schema.rows.calendarId, calendarId),
          eq(schema.rows.eventId, eventId),
        ),
      )
      .returning();
    return result;
  });
};

const updateRow = async (data, tickets) => {
  return withConnection(async (db) => {
    const result = await db.transaction(async (tx) => {
      await tx
        .delete(schema.tickets)
        .where(
          and(
            eq(schema.tickets.rowId, data.id),
            eq(schema.tickets.calendarId, data.calendarId),
            eq(schema.tickets.eventId, data.eventId),
            eq(schema.tickets.zoneId, data.zoneId),
            ne(schema.tickets.status, `${ticketStatus.CHECKIN}`),
            ne(schema.tickets.status, `${ticketStatus.SOLD}`),
          ),
        );
      await tx
        .update(schema.rows)
        .set(data)
        .where(
          and(
            eq(schema.rows.id, data.id),
            eq(schema.rows.calendarId, data.calendarId),
            eq(schema.rows.eventId, data.eventId),
            eq(schema.rows.zoneId, data.zoneId),
          ),
        );
      await tx.insert(schema.tickets).values(tickets);
    });

    return result;
  });
};

const sortRows = async (data) => {
  return withConnection(async (db) => {
    const caseStatements = data.positions
      .map((item) => `WHEN id = '${item.id}' THEN ${item.position}`)
      .join('\n');
    const result = await db
      .update(schema.rows)
      .set({
        position: sql`CASE 
      ${sql.raw(caseStatements)}
      ELSE position 
    END`,
      })
      .where(
        and(
          eq(schema.rows.eventId, data.eventId),
          eq(schema.rows.calendarId, data.calendarId),
          eq(schema.rows.zoneId, data.zoneId),
        ),
      );

    return result;
  });
};

const getLatestPosition = async ({eventId, calendarId, zoneId}) => {
  return withConnection(async (db) => {
    const result = await db
      .select({position: schema.rows.position})
      .from(schema.rows)
      .where(
        and(
          eq(schema.rows.eventId, eventId),
          eq(schema.rows.calendarId, calendarId),
          eq(schema.rows.zoneId, zoneId),
        ),
      )
      .orderBy(desc(schema.rows.position))
      .limit(1);
    return result;
  });
};

module.exports = {
  createTicketRow,
  getRowsByZone,
  deleteRow,
  updateRow,
  sortRows,
  getLatestPosition,
};
