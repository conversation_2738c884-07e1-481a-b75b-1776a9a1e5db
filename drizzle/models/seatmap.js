const {withConnection} = require('../db');
const schema = require('../schema');
const {eq, and} = require('drizzle-orm');

const createSeatmap = async (data) => {
  return await withConnection(async (db) => {
    const result = await db
      .insert(schema.seatMap)
      .values(data)
      .returning({insertedId: schema.seatMap.id});
    return result;
  });
};

const getSeatmap = async ({eventId, calendarId}) => {
  return await withConnection(async (db) => {
    const result = await db
      .select()
      .from(schema.seatMap)
      .where(and(eq(schema.seatMap.eventId, eventId), eq(schema.seatMap.calendarId, calendarId)));
    return result;
  });
};

const editSeatmap = async (data) => {
  return await withConnection(async (db) => {
    const result = await db
      .update(schema.seatMap)
      .set(data)
      .where(
        and(
          eq(schema.seatMap.eventId, data.eventId),
          eq(schema.seatMap.calendarId, data.calendarId),
        ),
      )
      .returning();
    return result;
  });
};

const deleteSeatmap = async ({eventId, calendarId}) => {
  return await withConnection(async (db) => {
    const result = await db
      .delete(schema.seatMap)
      .where(and(eq(schema.seatMap.eventId, eventId), eq(schema.seatMap.calendarId, calendarId)))
      .returning();
    return result;
  });
};

module.exports = {
  createSeatmap,
  getSeatmap,
  editSeatmap,
  deleteSeatmap,
};
