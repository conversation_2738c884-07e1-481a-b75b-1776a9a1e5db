const {eq, and, sql, desc} = require('drizzle-orm');
const {withConnection} = require('../db');
const schema = require('../schema');
const {ticketStatus} = require('../../src/handler/cms/ticket/const');

const createTicketZone = async (data) => {
  return withConnection(async (db) => {
    const result = await db
      .insert(schema.zones)
      .values(data)
      .returning({insertedId: schema.zones.id});
    return result;
  });
};

const deleteTicketZone = async ({zoneId, calendarId, eventId}) => {
  return withConnection(async (db) => {
    const result = await db
      .delete(schema.zones)
      .where(
        and(
          eq(schema.zones.id, zoneId),
          eq(schema.zones.calendarId, calendarId),
          eq(schema.zones.eventId, eventId),
        ),
      )
      .returning();
    return result;
  });
};

const getZoneHaveTickets = async ({eventId, calendarId}) => {
  return withConnection(async (db) => {
    const subQueryGetTickets = db.$with('T').as(
      db
        .select({zoneId: schema.tickets.zoneId})
        .from(schema.tickets)
        .where(and(eq(schema.tickets.calendarId, calendarId), eq(schema.tickets.eventId, eventId)))
        .groupBy(schema.tickets.zoneId),
    );
    const result = await db
      .with(subQueryGetTickets)
      .select(schema.zones)
      .from(schema.zones)
      .innerJoin(subQueryGetTickets, eq(schema.zones.id, subQueryGetTickets.zoneId));
    return result;
  });
};

const getZones = async ({eventId, calendarId, ticketClassId}) => {
  return withConnection(async (db) => {
    const result = await db
      .select({
        ...schema.zones,
        countTickets: sql`COUNT(DISTINCT ${schema.tickets.id})`.as('count_tickets'),
        countTicketsSold:
          sql`COUNT(DISTINCT CASE WHEN ${schema.tickets.status} = ${ticketStatus.SOLD} THEN ${schema.tickets.id} END)`.as(
            'count_tickets_sold',
          ),
        countRows: sql`COUNT(DISTINCT ${schema.rows.id})`.as('count_rows'),
      })
      .from(schema.zones)
      .leftJoin(schema.rows, eq(schema.zones.id, schema.rows.zoneId))
      .leftJoin(schema.tickets, eq(schema.zones.id, schema.tickets.zoneId))
      .where(
        and(
          eq(schema.zones.calendarId, calendarId),
          eq(schema.zones.eventId, eventId),
          eq(schema.zones.ticketClassId, ticketClassId),
        ),
      )
      .groupBy(schema.zones.id);
    return result;
  });
};

const sortZones = async (data) => {
  return withConnection(async (db) => {
    const caseStatements = data.positions
      .map((item) => `WHEN id = '${item.id}' THEN ${item.position}`)
      .join('\n');
    const result = await db
      .update(schema.zones)
      .set({
        position: sql`CASE 
      ${sql.raw(caseStatements)}
      ELSE position 
    END`,
      })
      .where(
        and(eq(schema.zones.eventId, data.eventId), eq(schema.zones.calendarId, data.calendarId)),
      );

    return result;
  });
};

const getLatestPosition = async ({eventId, calendarId}) => {
  return withConnection(async (db) => {
    const result = await db
      .select({position: schema.zones.position})
      .from(schema.zones)
      .where(and(eq(schema.zones.eventId, eventId), eq(schema.zones.calendarId, calendarId)))
      .orderBy(desc(schema.zones.position))
      .limit(1);
    return result;
  });
};

module.exports = {
  createTicketZone,
  deleteTicketZone,
  getZoneHaveTickets,
  getZones,
  sortZones,
  getLatestPosition,
};
