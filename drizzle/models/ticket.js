const {eq, and, ilike, sql, or, count, inArray, ne, asc} = require('drizzle-orm');
const {withConnection} = require('../db');
const schema = require('../schema');
const ticketConsts = require('../../src/handler/cms/ticket/const');

const createTickets = async (data) => {
  return withConnection(async (db) => {
    const result = await db
      .insert(schema.tickets)
      .values(data)
      .returning({insertedId: schema.tickets.id});
    return result;
  });
};

const getTicketsByRow = async (data) => {
  return withConnection(async (db) => {
    const result = await db
      .select()
      .from(schema.tickets)
      .where(
        and(
          eq(schema.tickets.rowId, data.rowId),
          eq(schema.tickets.eventId, data.eventId),
          eq(schema.tickets.calendarId, data.calendarId),
        ),
      );
    return result;
  });
};

const getTicketsByFilter = async (data, queryParams) => {
  return withConnection(async (db) => {
    const whereClauses = [
      eq(schema.tickets.eventId, data.eventId),
      eq(schema.tickets.calendarId, data.calendarId),
    ];
    let limit = 100;
    let offset = 0;
    if ('ticketClassId' in queryParams) {
      whereClauses.push(eq(schema.tickets.ticketClassId, queryParams.ticketClassId));
    }
    if ('zoneId' in queryParams) {
      whereClauses.push(eq(schema.tickets.zoneId, queryParams.zoneId));
    }
    if ('rowId' in queryParams) {
      whereClauses.push(eq(schema.tickets.rowId, queryParams.rowId));
    }
    if ('status' in queryParams) {
      whereClauses.push(eq(schema.tickets.status, queryParams.status));
    }
    if ('limit' in queryParams) {
      limit = queryParams.limit;
    }
    if ('page' in queryParams && queryParams.page > 0) {
      offset = (queryParams.page - 1) * limit;
    }

    if ('search' in queryParams) {
      whereClauses.push(
        or(
          ilike(schema.tickets.customerEmail, `%${queryParams.search}%`),
          ilike(schema.tickets.customerPhone, `%${queryParams.search}%`),
          ilike(schema.tickets.orderId, `%${queryParams.search}%`),
        ),
      );
    }

    const getBaseQuery = (qb) => {
      return qb
        .from(schema.tickets)
        .leftJoin(schema.rows, eq(schema.tickets.rowId, schema.rows.id))
        .leftJoin(schema.zones, eq(schema.tickets.zoneId, schema.zones.id))
        .leftJoin(schema.ticketClass, eq(schema.tickets.ticketClassId, schema.ticketClass.id))
        .where(and(...whereClauses));
    };
    const queryFilterSelect = db.select({
      ...schema.tickets,
      rowName: schema.rows.name,
      ticketClassName: schema.ticketClass.name,
      zoneName: schema.zones.name,
      rowPosition: schema.rows.position,
      zonePosition: schema.zones.position,
    });

    const queryCountSelect = db.select({count: count(schema.tickets.id)});

    const [result, resultCount] = await Promise.all([
      getBaseQuery(queryFilterSelect)
        .orderBy(
          asc(schema.zones.position),
          asc(schema.rows.position),
          asc(schema.tickets.position),
        )
        .limit(sql`${limit}`)
        .offset(sql`${offset}`),
      getBaseQuery(queryCountSelect),
    ]);

    return {items: result, total: resultCount[0].count};
  });
};

const updateTicketsStatus = async (data) => {
  return withConnection(async (db) => {
    const whereClauses = [
      eq(schema.tickets.eventId, data.eventId),
      eq(schema.tickets.calendarId, data.calendarId),
      inArray(schema.tickets.id, data.ids),
    ];
    if (data.inStatus) {
      whereClauses.push(eq(schema.tickets.status, data.inStatus));
    }
    if (data.notInStatus) {
      whereClauses.push(ne(schema.tickets.status, data.notInStatus));
    }
    const result = await db
      .update(schema.tickets)
      .set({status: data.status})
      .where(and(...whereClauses))
      .returning({
        id: schema.tickets.id,
        ticketClassId: schema.tickets.ticketClassId,
      });
    return result;
  });
};

const cancelTickets = async (data) => {
  return withConnection(async (db) => {
    const result = await db
      .update(schema.tickets)
      .set({
        status: ticketConsts.ticketStatus.IDLE,
        customerEmail: null,
        customerExtraData: null,
        customerName: null,
        customerPhone: null,
        customerNote: null,
      })
      .where(
        and(
          eq(schema.tickets.eventId, data.eventId),
          eq(schema.tickets.calendarId, data.calendarId),
          inArray(schema.tickets.id, data.ids),
          ne(schema.tickets.status, `${ticketConsts.ticketStatus.CHECKIN}`),
        ),
      );
    return result;
  });
};

const getTicketsByIds = async ({ids}) => {
  return withConnection(async (db) => {
    const result = await db
      .select({
        ...schema.tickets,
        ticketClassName: schema.ticketClass.name,
        finalPriceVn: schema.ticketClass.finalPriceVn,
        finalPriceUsd: schema.ticketClass.finalPriceUsd,
        rowName: schema.rows.name,
        zoneName: schema.zones.name,
        zonePosition: schema.zones.position,
        rowPosition: schema.rows.position,
      })
      .from(schema.tickets)
      .leftJoin(schema.ticketClass, eq(schema.tickets.ticketClassId, schema.ticketClass.id))
      .leftJoin(schema.zones, eq(schema.tickets.zoneId, schema.zones.id))
      .leftJoin(schema.rows, eq(schema.tickets.rowId, schema.rows.id))
      .where(inArray(schema.tickets.id, ids))
      .orderBy(asc(schema.zones.position), asc(schema.rows.position), asc(schema.tickets.position));
    return result;
  });
};

const updateTickets = async (data) => {
  return withConnection(async (db) => {
    const whereClauses = [];
    if (data.eventId) {
      whereClauses.push(eq(schema.tickets.eventId, data.eventId));
    }
    if (data.calendarId) {
      whereClauses.push(eq(schema.tickets.calendarId, data.calendarId));
    }
    if (data.orderId) {
      whereClauses.push(eq(schema.tickets.orderId, data.orderId));
    }
    if (data.ids) {
      whereClauses.push(inArray(schema.tickets.id, data.ids));
    }

    const updateValues = {};
    if (data.customerEmail) {
      updateValues.customerEmail = data.customerEmail;
    }
    if (data.customerName) {
      updateValues.customerName = data.customerName;
    }
    if (data.customerPhone) {
      updateValues.customerPhone = data.customerPhone;
    }
    if (data.customerExtraData) {
      updateValues.customerExtraData = data.customerExtraData;
    }
    if (data.status) {
      updateValues.status = data.status;
    }
    if (data.orderIdUpdate) {
      updateValues.orderId = data.orderIdUpdate;
    }
    const result = await db
      .update(schema.tickets)
      .set(updateValues)
      .where(and(...whereClauses));
    return result;
  });
};

const updateTicketCheckinStatus = async (data) => {
  return withConnection(async (db) => {
    const whereClauses = [
      eq(schema.tickets.eventId, data.eventId),
      eq(schema.tickets.calendarId, data.calendarId),
      inArray(schema.tickets.id, data.ids),
    ];
    const result = await db
      .update(schema.tickets)
      .set({checkin: data.checkin})
      .where(and(...whereClauses));
    return result;
  });
};

const getTicketsByTicketClassId = async (ticketClassId) => {
  return withConnection(async (db) => {
    const result = await db
      .select({
        id: schema.tickets.id,
        position: schema.tickets.position,
        zoneId: schema.zones.id,
        zonePosition: schema.zones.position,
        rowId: schema.rows.id,
        rowPosition: schema.rows.position,
      })
      .from(schema.tickets)
      .leftJoin(schema.zones, eq(schema.zones.id, schema.tickets.zoneId))
      .leftJoin(schema.rows, eq(schema.rows.id, schema.tickets.rowId))
      .where(
        and(
          eq(schema.tickets.ticketClassId, ticketClassId),
          eq(schema.tickets.status, ticketConsts.ticketStatus.IDLE),
        ),
      )
      .orderBy(asc(schema.zones.position), asc(schema.rows.position), asc(schema.tickets.position));

    return result;
  });
};

const holdTicketsForCustomer = async (ticketIds, orderId) => {
  return withConnection(async (db) => {
    await db
      .update(schema.tickets)
      .set({
        orderId: orderId,
        status: ticketConsts.ticketStatus.HOLD,
        holdAt: new Date(),
      })
      .where(inArray(schema.tickets.id, ticketIds));
  });
};

const resolveHoldTicketByOrder = async (orderId) => {
  return withConnection(async (db) => {
    await db
      .update(schema.tickets)
      .set({
        orderId: null,
        status: ticketConsts.ticketStatus.IDLE,
        holdAt: null,
      })
      .where(eq(schema.tickets.orderId, orderId));
  });
};

const updateTicketStatusBySuccessOrder = async (order) => {
  return withConnection(async (db) => {
    await db
      .update(schema.tickets)
      .set({
        status: ticketConsts.ticketStatus.SOLD,
        customerName: order.paymentInfo?.name,
        customerEmail: order.paymentInfo?.email,
        customerPhone: order.paymentInfo?.phoneNumber,
        customerNote: order.paymentInfo?.note,
        customerExtraData: order.paymentInfo,
        holdAt: null,
        soldAt: order.paymentTime,
        paymentGateway: order.paymentGateway,
      })
      .where(eq(schema.tickets.orderId, order.orderId));
  });
};

const getTicketsByOrderId = async (orderId) => {
  return withConnection(async (db) => {
    const result = await db
      .select({
        ...schema.tickets,
        zoneName: schema.zones.name,
        rowName: schema.rows.name,
        ticketClassName: schema.ticketClass.name,
        finalPriceVn: schema.ticketClass.finalPriceVn,
        finalPriceUsd: schema.ticketClass.finalPriceUsd,
      })
      .from(schema.tickets)
      .leftJoin(schema.zones, eq(schema.zones.id, schema.tickets.zoneId))
      .leftJoin(schema.rows, eq(schema.rows.id, schema.tickets.rowId))
      .leftJoin(schema.ticketClass, eq(schema.ticketClass.id, schema.tickets.ticketClassId))
      .where(eq(schema.tickets.orderId, orderId))
      .orderBy(asc(schema.zones.position), asc(schema.rows.position), asc(schema.tickets.position));
    return result;
  });
};

const cancelTicketsOrder = async ({eventId, orderId}) => {
  return withConnection(async (db) => {
    const result = await db
      .update(schema.tickets)
      .set({
        status: ticketConsts.ticketStatus.IDLE,
        orderId: null,
      })
      .where(and(eq(schema.tickets.eventId, eventId), eq(schema.tickets.orderId, orderId)));
    return result;
  });
};

const batchResolveHoldingTickets = async () => {
  return withConnection(async (db) => {
    // Lấy thời gian hiện tại trừ đi 10 phút
    const tenMinutesAgo = new Date(Date.now() - 20 * 60 * 1000);
    // Chuyển đổi thời gian 10 phút trước thành định dạng ISO để tương thích với PostgreSQL
    const tenMinutesAgoIso = tenMinutesAgo.toISOString();

    await db
      .update(schema.tickets)
      .set({
        status: ticketConsts.ticketStatus.IDLE,
        orderId: null,
        holdAt: null,
      })
      .where(
        and(
          eq(schema.tickets.status, ticketConsts.ticketStatus.HOLD),
          sql`${schema.tickets.holdAt} < ${tenMinutesAgoIso}::timestamptz`,
        ),
      )
      .returning({
        ticketClassId: schema.tickets.ticketClassId,
        eventId: schema.tickets.eventId,
      });
  });
};

const getAllTicketsByEventAndCalendar = async (data) => {
  return withConnection(async (db) => {
    const result = await db
      .select({
        id: schema.tickets.id,
        position: schema.tickets.position,
        code: schema.tickets.code,
        eventId: schema.tickets.eventId,
        rowId: schema.tickets.rowId,
        zoneId: schema.tickets.zoneId,
        status: schema.tickets.status,
        ticketClassId: schema.tickets.ticketClassId,
        zoneName: schema.zones.name,
        rowName: schema.rows.name,
        zonePosition: schema.zones.position,
        rowPosition: schema.rows.position,
        ticketColor: schema.ticketClass.color,
      })
      .from(schema.tickets)
      .leftJoin(schema.rows, eq(schema.tickets.rowId, schema.rows.id))
      .leftJoin(schema.zones, eq(schema.tickets.zoneId, schema.zones.id))
      .leftJoin(schema.ticketClass, eq(schema.tickets.ticketClassId, schema.ticketClass.id))
      .where(
        and(
          eq(schema.tickets.eventId, data.eventId),
          eq(schema.tickets.calendarId, data.calendarId),
        ),
      )
      .orderBy(asc(schema.zones.position), asc(schema.rows.position), asc(schema.tickets.position));
    return result;
  });
};

const getTicketByGenerateCode = async (generateCode) => {
  return withConnection(async (db) => {
    const result = await db
      .select({
        ...schema.tickets,
        zoneName: schema.zones.name,
        rowName: schema.rows.name,
        ticketName: schema.ticketClass.name,
      })
      .from(schema.tickets)
      .leftJoin(schema.zones, eq(schema.zones.id, schema.tickets.zoneId))
      .leftJoin(schema.rows, eq(schema.rows.id, schema.tickets.rowId))
      .leftJoin(schema.ticketClass, eq(schema.tickets.ticketClassId, schema.ticketClass.id))
      .where(eq(schema.tickets.generateCode, generateCode));
    return result;
  });
};

const updateCheckinStatusByGenerateCode = async (generateCode) => {
  return withConnection(async (db) => {
    await db
      .update(schema.tickets)
      .set({checkin: true})
      .where(eq(schema.tickets.generateCode, generateCode));
  });
};

const getAllTicketsClient = async ({eventId, calendarId}) => {
  return withConnection(async (db) => {
    const result = await db
      .select({
        id: schema.tickets.id,
        status: schema.tickets.status,
        ticketClassId: schema.tickets.ticketClassId,
        generateCode: schema.tickets.generateCode,
      })
      .from(schema.tickets)
      .where(and(eq(schema.tickets.eventId, eventId), eq(schema.tickets.calendarId, calendarId)));
    return result;
  });
};

const getAllZonesClient = async ({eventId, calendarId}) => {
  return withConnection(async (db) => {
    const result = await db
      .select({
        ...schema.zones,
        ticketClassId: schema.ticketClass.id,
        countTicketsAvailable:
          sql`COUNT(DISTINCT CASE WHEN ${schema.tickets.status} = ${ticketConsts.ticketStatus.IDLE} THEN ${schema.tickets.id} END)`.as(
            'count_tickets_available',
          ),
      })
      .from(schema.zones)
      .leftJoin(schema.tickets, eq(schema.zones.id, schema.tickets.zoneId))
      .leftJoin(schema.ticketClass, eq(schema.tickets.ticketClassId, schema.ticketClass.id))
      .where(and(eq(schema.zones.eventId, eventId), eq(schema.zones.calendarId, calendarId)))
      .groupBy(schema.zones.id, schema.ticketClass.id);
    return result;
  });
};

module.exports = {
  createTickets,
  getTicketsByRow,
  getTicketsByFilter,
  updateTicketsStatus,
  cancelTickets,
  getTicketsByIds,
  updateTickets,
  updateTicketCheckinStatus,
  getTicketsByTicketClassId,
  holdTicketsForCustomer,
  resolveHoldTicketByOrder,
  updateTicketStatusBySuccessOrder,
  getTicketsByOrderId,
  cancelTicketsOrder,
  batchResolveHoldingTickets,
  getAllTicketsByEventAndCalendar,
  getTicketByGenerateCode,
  updateCheckinStatusByGenerateCode,
  getAllTicketsClient,
  getAllZonesClient,
};
