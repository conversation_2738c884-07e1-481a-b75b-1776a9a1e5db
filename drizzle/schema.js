const {sql} = require('drizzle-orm');
const {
  pgTable,
  uuid,
  smallint,
  varchar,
  timestamp,
  text,
  real,
  index,
  jsonb,
  boolean,
} = require('drizzle-orm/pg-core');

const zones = pgTable(
  'Zones',
  {
    id: uuid('id')
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    name: varchar('name', {length: 256}),
    calendarId: varchar('calendar_id', {length: 256}),
    eventId: varchar('event_id', {length: 256}),
    ticketClassId: uuid('ticket_class_id', {length: 256}).references(() => ticketClass.id, {
      onDelete: 'cascade',
    }),
    position: smallint('position'),
    createdAt: timestamp('created_at').defaultNow(),
  },
  (table) => {
    return {
      positionIdx: index('zone_position_idx').on(table.position),
    };
  },
);

const rows = pgTable(
  'Rows',
  {
    id: uuid('id')
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    calendarId: varchar('calendar_id', {length: 256}),
    eventId: varchar('event_id', {length: 256}),
    zoneId: uuid('zone_id', {length: 256}).references(() => zones.id, {
      onDelete: 'cascade',
    }),
    seatNumberingType: varchar('seat_numbering_type', {length: 256}),
    seatNumberingFrom: smallint('seat_numbering_from'),
    seatNumberingTo: smallint('seat_numbering_to'),
    name: varchar('name', {length: 256}),
    position: smallint('position'),
    created_at: timestamp('created_at').defaultNow(),
  },
  (table) => {
    return {
      positionIdx: index('row_position_idx').on(table.position),
    };
  },
);

const tickets = pgTable(
  'Tickets',
  {
    id: uuid('id')
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    calendarId: varchar('calendar_id', {length: 256}),
    eventId: varchar('event_id', {length: 256}),
    rowId: uuid('row_id').references(() => rows.id, {onDelete: 'cascade'}),
    zoneId: uuid('zone_id', {length: 256}).references(() => zones.id, {
      onDelete: 'cascade',
    }),
    code: varchar('code', {length: 256}),
    status: varchar('status', {length: 20}).notNull().default('idle'),
    checkin: boolean('checkin').default(false),
    customerName: varchar('customer_name', {length: 256}),
    customerEmail: varchar('customer_email', {length: 256}),
    customerPhone: varchar('customer_phone', {length: 256}),
    customerNote: text('customer_note'),
    customerExtraData: jsonb('customer_extra_data'),
    orderId: varchar('order_id', {length: 256}),
    ticketClassId: uuid('ticket_class_id').references(() => ticketClass.id),
    position: smallint('position'),
    generateCode: varchar('generic_code', {length: 10}),
    createdAt: timestamp('created_at').defaultNow(),
    holdAt: timestamp('hold_at'),
    soldAt: timestamp('sold_at'),
    paymentGateway: varchar('payment_gateway', {length: 30}),
    checkinAt: timestamp('checkin_at'),
    checkinBy: varchar('checkin_by', {length: 256}),
  },
  (table) => {
    return {
      customerNameIdx: index('customer_name_idx').on(table.customerName),
      customerEmailIdx: index('customer_email_idx').on(table.customerEmail),
      customerPhoneIdx: index('customer_phone_idx').on(table.customerPhone),
      statusIdx: index('status_idx').on(table.status),
      codeIdx: index('code_idx').on(table.code),
      orderIdx: index('order_idx').on(table.orderId),
      positionIdx: index('ticket_position_idx').on(table.position),
      customerExtraDataIdx: index('customer_extra_data_idx').using('gin', sql`customer_extra_data`),
    };
  },
);

const ticketClass = pgTable(
  'TicketClasses',
  {
    id: uuid('id')
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    eventId: varchar('event_id', {length: 256}),
    calendarId: varchar('calendar_id', {length: 256}),
    name: varchar('name', {length: 256}),
    color: varchar('color', {length: 256}),
    originalPriceVn: real('original_price_vn'),
    originalPriceUsd: real('original_price_usd'),
    finalPriceVn: real('final_price_vn'),
    finalPriceUsd: real('final_price_usd'),
    description: text('description'),
    seatType: varchar('seat_type'),
    maxTicketPerUser: smallint('max_ticket_per_user'),
    prototypeUrl: varchar('prototype_url', {length: 256}),
    createdAt: timestamp('created_at').defaultNow(),
  },
  (table) => {
    return {
      eventIdIdx: index('ticket_class_event_calendar_idx').on(table.eventId, table.calendarId),
    };
  },
);

const seatMap = pgTable(
  'SeatMaps',
  {
    id: uuid('id')
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    eventId: varchar('event_id', {length: 256}),
    calendarId: varchar('calendar_id', {length: 256}),
    seatMap: jsonb('seat_map'),
    createdAt: timestamp('created_at').defaultNow(),
  },
  (table) => {
    return {
      eventIdIdx: index('seat_map_event_calendar_idx').on(table.eventId, table.calendarId),
    };
  },
);

module.exports = {
  tickets,
  ticketClass,
  zones,
  rows,
  seatMap,
};
