openapi: 3.0.0
components:
  schemas:
    ee8981ed-416d-4792-b2b7-166015dfe773:
      type: object
      properties:
        configs:
          type: array
          items:
            type: object
            properties:
              PK:
                type: string
                example: HOMEPAGE
              SK:
                type: string
                example: SECTION_EVENT
              sort:
                type: number
                example: 1
      required:
        - configs
    885a8a7c-da65-4642-a629-da4ca5db2e1e:
      type: object
      properties:
        page:
          type: string
          example: HOMEPAGE
        position:
          type: string
          example: BANNER
        navigationUrl:
          type: string
        title:
          type: string
        sort:
          type: number
          example: 1
        items:
          type: array
          items:
            type: object
            properties:
              PK:
                type: string
              SK:
                type: string
              sort:
                type: number
                example: 1
      required:
        - page
        - position
    cd54499f-19ea-4f82-9c14-678a1d478967:
      type: object
      properties:
        PK:
          type: string
        SK:
          type: string
        position:
          type: string
          example: BANNER
        navigationUrl:
          type: string
        title:
          type: string
        sort:
          type: number
          example: 1
        items:
          type: array
          items:
            type: object
            properties:
              PK:
                type: string
              SK:
                type: string
              sort:
                type: number
                example: 1
      required:
        - PK
        - SK
    6c707aed-5618-4c4e-a2ec-62302c422823:
      type: object
      properties:
        PK:
          type: string
        SK:
          type: string
      required:
        - PK
        - SK
    type:
      type: string
    8bb6214b-085b-49d0-954b-66784e2660cf:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        type:
          type: string
        banner:
          type: string
        button:
          type: object
          properties:
            title:
              type: string
            url:
              type: string
        startTime:
          type: string
        endTime:
          type: string
        author:
          type: string
        file:
          type: string
        children:
          type: array
          items:
            type: string
        sort:
          type: number
          example: 1
      required:
        - title
        - description
        - banner
        - type
    f9577c6b-2ff8-48ed-87c5-b6bf4ef54201:
      type: object
      properties:
        PK:
          type: string
        SK:
          type: string
        title:
          type: string
        description:
          type: string
        banner:
          type: string
        button:
          type: object
          properties:
            title:
              type: string
            url:
              type: string
        sort:
          type: number
          example: 1
      required:
        - PK
        - SK
    90900f03-f119-468e-a2bb-af26c9827f94:
      type: object
      properties:
        PK:
          type: string
        SK:
          type: string
      required:
        - PK
        - SK
    courseId:
      type: string
    04615c57-34df-4e89-ac53-3d5acdc957e0:
      type: object
      properties:
        title:
          type: string
        type:
          type: string
        videoUrl:
          type: string
        sort:
          type: number
          example: 1
      required:
        - title
        - type
    lessonId:
      type: string
    e45957f1-0e95-489a-8daa-230b0c260d78:
      type: object
      properties:
        title:
          type: string
        sort:
          type: number
          example: 1
    podcastId:
      type: string
    albumId:
      type: string
    1852582d-6e7a-42da-b095-2396d36e6525:
      type: object
      properties:
        email:
          type: string
        phone:
          type: string
        password:
          type: string
      required:
        - email
        - password
    b1d691cc-181d-4033-af03-d9a11a657bc9:
      type: object
      properties:
        email:
          type: string
        otp:
          type: string
      required:
        - email
        - otp
    212c75bc-70e1-44b4-9357-7f9272a5c3ba:
      type: object
      properties:
        email:
          type: string
      required:
        - email
    ae323ecd-590e-44e3-9594-56a5d986f088:
      type: object
      properties:
        email:
          type: string
        password:
          type: string
      required:
        - email
        - password
    a26b92eb-dd28-470d-bf8c-b7c14ecc3832:
      type: object
      properties:
        newPassword:
          type: string
      required:
        - newPassword
    2970501f-c47e-4d4e-beef-a0138e486974:
      type: object
      properties:
        refreshToken:
          type: string
      required:
        - refreshToken
    f209095e-57fc-48be-807e-2462e62d8252:
      type: object
      properties:
        name:
          type: string
        icCard:
          type: string
        birthday:
          type: string
        phone:
          type: string
        gender:
          type: number
        studentId:
          type: string
        profileImg:
          type: string
        university:
          type: string
        major:
          type: string
        schoolYear:
          type: string
        address:
          type: string
      required:
        - name
        - icCard
        - birthday
    5de52dc6-6e0d-4724-ac33-048b780f35cf:
      type: object
      properties:
        oldPassword:
          type: string
        newPassword:
          type: string
      required:
        - oldPassword
        - newPassword
    e7007f7d-646e-42a2-bb68-d5311feae241:
      type: object
      properties:
        name:
          type: string
        icCard:
          type: string
        birthday:
          type: string
        phone:
          type: string
        gender:
          type: number
        studentId:
          type: string
        profileImg:
          type: string
        university:
          type: string
        major:
          type: string
        schoolYear:
          type: string
        schoolCertificate:
          type: string
      required:
        - name
        - icCard
        - birthday
        - gender
        - phone
        - university
        - major
        - studentId
        - schoolYear
        - schoolCertificate
    a84192e7-7af7-4ea3-a56e-98fe89e8b5da:
      type: object
      properties:
        productId:
          type: string
        orderId:
          type: string
        paymentGateway:
          type: string
        paymentInfo:
          type: string
      required:
        - productId
        - orderId
        - paymentGateway
        - paymentInfo
    eventId:
      type: string
    orderId:
      type: string
    6ed05bdb-e21e-4e2d-81c0-4be85f42787d:
      type: object
      properties:
        srcKey:
          type: string
      required:
        - srcKey
    a5b0a8d6-4aec-42ad-8105-019b9afeab65:
      type: object
      properties:
        srcKey:
          type: string
      required:
        - srcKey
    a4d041c2-80ae-4ca3-a5b9-363b3765fd93:
      type: object
      properties:
        srcKey:
          type: string
      required:
        - srcKey
    7bf667fb-cb63-4d73-81e5-d815227943a7:
      type: object
      properties:
        srcKey:
          type: string
      required:
        - srcKey
  securitySchemes:
    auth:
      type: apiKey
      name: Authorization
      in: header
info:
  title: Trẻ Center API
  description: Trẻ Center API
  version: '1.0'
  termsOfService: https://google.com
security:
  - auth: []
paths:
  /v1/cms/configs:
    get:
      summary: ''
      description: Get page's configs
      operationId: config
      parameters: []
      tags:
        - Config
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/cms/configs/sort:
    post:
      summary: ''
      description: Sort configs
      operationId: config-62515e34-3449-4ff0-a207-39f4f404cfe9
      parameters: []
      tags:
        - Config
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ee8981ed-416d-4792-b2b7-166015dfe773'
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/cms/config:
    post:
      summary: ''
      description: Create config
      operationId: config-5f53547a-7d79-491d-8d99-2735b3320449
      parameters: []
      tags:
        - Config
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/885a8a7c-da65-4642-a629-da4ca5db2e1e'
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
    put:
      summary: ''
      description: Update config
      operationId: config-f386dcb0-0bdf-4876-9cbc-18a1344b44b5
      parameters: []
      tags:
        - Config
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/cd54499f-19ea-4f82-9c14-678a1d478967'
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
    delete:
      summary: ''
      description: Delete config
      operationId: config-699007a7-fe63-4c07-9ce5-66908148c343
      parameters: []
      tags:
        - Config
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/6c707aed-5618-4c4e-a2ec-62302c422823'
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/cms/blogs:
    get:
      summary: ''
      description: Get all blogs
      operationId: blog
      parameters:
        - name: type
          in: query
          description: ''
          required: false
          schema:
            $ref: '#/components/schemas/type'
      tags:
        - Blog
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/cms/blog:
    post:
      summary: ''
      description: Create blog
      operationId: blog-3e06497f-7f11-4efa-87a8-ac906d5ed0ae
      parameters: []
      tags:
        - Blog
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/8bb6214b-085b-49d0-954b-66784e2660cf'
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
    put:
      summary: ''
      description: Update blog
      operationId: blog-fc447898-834a-4abc-8950-70a31bc29216
      parameters: []
      tags:
        - Blog
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/f9577c6b-2ff8-48ed-87c5-b6bf4ef54201'
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
    delete:
      summary: ''
      description: Delete blog
      operationId: blog-76aa70a6-1478-4284-a249-5f21d7220b84
      parameters: []
      tags:
        - Blog
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/90900f03-f119-468e-a2bb-af26c9827f94'
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/cms/course/{courseId}/lesson:
    post:
      summary: ''
      description: Create lesson
      operationId: course
      parameters:
        - name: courseId
          in: path
          description: ''
          required: true
          schema:
            $ref: '#/components/schemas/courseId'
      tags:
        - Course
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/04615c57-34df-4e89-ac53-3d5acdc957e0'
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/cms/course/{courseId}/lesson/{lessonId}:
    get:
      summary: ''
      description: Get lesson
      operationId: course-f8f05e11-15ce-4840-b353-5824f1e427c8
      parameters:
        - name: courseId
          in: path
          description: ''
          required: true
          schema:
            $ref: '#/components/schemas/courseId'
        - name: lessonId
          in: path
          description: ''
          required: true
          schema:
            $ref: '#/components/schemas/lessonId'
      tags:
        - Course
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
    put:
      summary: ''
      description: Update lesson
      operationId: course-62fedb5c-8206-4819-9e05-cb3d4f77fc19
      parameters:
        - name: courseId
          in: path
          description: ''
          required: true
          schema:
            $ref: '#/components/schemas/courseId'
        - name: lessonId
          in: path
          description: ''
          required: true
          schema:
            $ref: '#/components/schemas/lessonId'
      tags:
        - Course
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/e45957f1-0e95-489a-8daa-230b0c260d78'
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
    delete:
      summary: ''
      description: Delete lesson
      operationId: course-ed89349d-56e1-4b6b-a605-e8ae24c9d17b
      parameters:
        - name: courseId
          in: path
          description: ''
          required: true
          schema:
            $ref: '#/components/schemas/courseId'
        - name: lessonId
          in: path
          description: ''
          required: true
          schema:
            $ref: '#/components/schemas/lessonId'
      tags:
        - Course
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/home:
    get:
      summary: ''
      description: Get homepage
      operationId: homepage
      parameters: []
      tags:
        - Page
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/concerts:
    get:
      summary: ''
      description: Get concert page
      operationId: concert
      parameters: []
      tags:
        - Page
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/concerts/recap:
    get:
      summary: ''
      description: Get recap
      operationId: concert-262e64d1-4ebc-4f5b-a676-7762e968726c
      parameters: []
      tags:
        - Page
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/podcasts:
    get:
      summary: ''
      description: Get podcast page
      operationId: podcast
      parameters: []
      tags:
        - Page
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/podcast/{podcastId}:
    get:
      summary: ''
      description: Get podcast detail
      operationId: podcast-5d9d17b1-2ed4-496e-a8f5-547af2f61c99
      parameters:
        - name: podcastId
          in: path
          description: ''
          required: true
          schema:
            $ref: '#/components/schemas/podcastId'
      tags:
        - Podcast
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/podcast/album/{albumId}:
    get:
      summary: ''
      description: Get album detail
      operationId: podcast-1f2543cb-1baf-40ad-9036-eb1e05fe794c
      parameters:
        - name: albumId
          in: path
          description: ''
          required: true
          schema:
            $ref: '#/components/schemas/albumId'
      tags:
        - Podcast
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/courses:
    get:
      summary: ''
      description: Get course page
      operationId: course-0eba68cf-020a-4441-9bbb-2b07defad7c3
      parameters: []
      tags:
        - Page
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/course/{courseId}:
    get:
      summary: ''
      description: Get course detail
      operationId: course-bc884098-acc4-4087-9733-73e37bf4f253
      parameters:
        - name: courseId
          in: path
          description: ''
          required: true
          schema:
            $ref: '#/components/schemas/courseId'
      tags:
        - Course
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/auth/register:
    post:
      summary: ''
      description: Register
      operationId: register
      parameters: []
      tags:
        - Auth
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/1852582d-6e7a-42da-b095-2396d36e6525'
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/auth/otp-verification:
    post:
      summary: ''
      description: Verify OTP
      operationId: register-44521d60-9073-4fe5-94f1-f527bacb6b20
      parameters: []
      tags:
        - Auth
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/b1d691cc-181d-4033-af03-d9a11a657bc9'
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/auth/otp-send:
    post:
      summary: ''
      description: Request OTP
      operationId: register-980300eb-2f3f-4943-9a77-2c976ec85bf9
      parameters: []
      tags:
        - Auth
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/212c75bc-70e1-44b4-9357-7f9272a5c3ba'
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/auth/login:
    post:
      summary: ''
      description: Login
      operationId: register-eaf3d41e-ca1b-4769-8ff3-1283e8f04851
      parameters: []
      tags:
        - Auth
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ae323ecd-590e-44e3-9594-56a5d986f088'
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/auth/reset-password:
    put:
      summary: ''
      description: Reset password
      operationId: register-00aa2389-398c-4a38-8620-93dcf8c7683e
      parameters: []
      tags:
        - Auth
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/a26b92eb-dd28-470d-bf8c-b7c14ecc3832'
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/auth/token:
    post:
      summary: ''
      description: Get token
      operationId: register-56d6deff-8ec7-405e-8eb5-44afba5eff05
      parameters: []
      tags:
        - Auth
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/2970501f-c47e-4d4e-beef-a0138e486974'
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/auth/profile:
    put:
      summary: ''
      description: Update info
      operationId: register-05d6138d-4fa8-4de1-b18a-68e8752f0802
      parameters: []
      tags:
        - Auth
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/f209095e-57fc-48be-807e-2462e62d8252'
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/auth/me:
    get:
      summary: ''
      description: Get info
      operationId: register-fbe9a3ee-ef42-48e6-97d5-8b1059b5063e
      parameters: []
      tags:
        - Auth
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/auth/change-password:
    put:
      summary: ''
      description: Change password
      operationId: register-86b21e86-e5e8-4c0f-9e7a-6f052c4a4cc9
      parameters: []
      tags:
        - Auth
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/5de52dc6-6e0d-4724-ac33-048b780f35cf'
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/auth/delete-account:
    put:
      summary: ''
      description: Delete account
      operationId: register-e6d675f6-8aba-4842-80d3-fefe0e29b6f4
      parameters: []
      tags:
        - Auth
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/auth/student-verification:
    put:
      summary: ''
      description: Verify student
      operationId: register-f825c442-d1ff-4522-a68d-fca2f008d0da
      parameters: []
      tags:
        - Auth
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/e7007f7d-646e-42a2-bb68-d5311feae241'
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/auth/cert:
    get:
      summary: ''
      description: Get certificate
      operationId: register-f2c063ea-13e4-4129-81e7-dc24209f5713
      parameters: []
      tags:
        - Auth
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/order/create:
    post:
      summary: ''
      description: Create order
      operationId: order
      parameters: []
      tags:
        - Order
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/a84192e7-7af7-4ea3-a56e-98fe89e8b5da'
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/order/event/{eventId}/cart:
    get:
      summary: ''
      description: Get cart
      operationId: order-524e9849-33ee-4eee-80cf-9951fd4210c4
      parameters:
        - name: eventId
          in: path
          description: ''
          required: true
          schema:
            $ref: '#/components/schemas/eventId'
      tags:
        - Order
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/order/event/{eventId}/cancel:
    post:
      summary: ''
      description: Cancel order
      operationId: order-b8af057e-38d2-4965-97e7-c9a2cf470cd5
      parameters:
        - name: eventId
          in: path
          description: ''
          required: true
          schema:
            $ref: '#/components/schemas/eventId'
      tags:
        - Order
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/order/event/{eventId}/cart/confirmation:
    post:
      summary: ''
      description: Confirm order
      operationId: order-ebf3cf79-1938-483f-8133-1690fd2a5e33
      parameters:
        - name: eventId
          in: path
          description: ''
          required: true
          schema:
            $ref: '#/components/schemas/eventId'
      tags:
        - Order
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/event/{eventId}:
    get:
      summary: ''
      description: Get event detail
      operationId: event
      parameters:
        - name: eventId
          in: path
          description: ''
          required: true
          schema:
            $ref: '#/components/schemas/eventId'
      tags:
        - Event
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/event/{eventId}/ticket-classes:
    get:
      summary: ''
      description: Get ticket classes
      operationId: event-7e1e3b26-bc0a-42a9-879e-5a80bc9daead
      parameters:
        - name: eventId
          in: path
          description: ''
          required: true
          schema:
            $ref: '#/components/schemas/eventId'
      tags:
        - Event
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/user/tickets:
    get:
      summary: ''
      description: Get purchased tickets
      operationId: user
      parameters: []
      tags:
        - User
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/user/order/{orderId}:
    get:
      summary: ''
      description: Get tickets by order
      operationId: user-1225d22d-5c4a-42d0-bf8d-b3876f346293
      parameters:
        - name: orderId
          in: path
          description: ''
          required: true
          schema:
            $ref: '#/components/schemas/orderId'
      tags:
        - User
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/upload/image-upload:
    post:
      summary: ''
      description: Upload
      operationId: upload
      parameters: []
      tags:
        - Upload
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/6ed05bdb-e21e-4e2d-81c0-4be85f42787d'
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/cms/upload/podcast-upload:
    post:
      summary: ''
      description: Upload
      operationId: upload-8b3f7395-1801-4372-b444-5bee8dc322aa
      parameters: []
      tags:
        - Upload
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/a5b0a8d6-4aec-42ad-8105-019b9afeab65'
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/cms/upload/course-upload:
    post:
      summary: ''
      description: Upload
      operationId: upload-734ad6ad-7b1d-41c5-a75b-7fd85381cbe9
      parameters: []
      tags:
        - Upload
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/a4d041c2-80ae-4ca3-a5b9-363b3765fd93'
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
  /v1/cms/upload/image-upload:
    post:
      summary: ''
      description: Upload
      operationId: upload-857a611c-180d-4075-a3a4-63adb2b1c0a1
      parameters: []
      tags:
        - Upload
      requestBody:
        description: ''
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/7bf667fb-cb63-4d73-81e5-d815227943a7'
      responses:
        '200':
          description: ''
          headers: {}
        '404':
          description: Not found
          headers: {}
        '500':
          description: Internal server error.
          headers: {}
servers:
  - url: https://3qybiunijh.execute-api.ap-southeast-1.amazonaws.com
    description: Dev Api
  - url: http://localhost:4000
    description: Local Api
