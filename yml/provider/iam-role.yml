- Effect: Allow
  Action:
    - dynamodb:GetItem
    - dynamodb:PutItem
    - dynamodb:UpdateItem
    - dynamodb:DeleteItem
    - dynamodb:Query
    - dynamodb:Scan
    - dynamodb:DescribeTable
    - dynamodb:BatchGetItem
    - dynamodb:BatchWriteItem
  Resource:
    - arn:aws:dynamodb:*:*:table/*-*/index/*
    - arn:aws:dynamodb:*:*:table/*-*
- Effect: Allow
  Action:
    - 's3:PutObject'
    - 's3:GetObject'
  Resource:
    - arn:aws:s3:::${param:web_bucket}/thumbnails/*
    - arn:aws:s3:::${param:web_bucket}/uploads/*
- Effect: Allow
  Action:
    - secretsmanager:GetSecretValue
  Resource:
    - arn:aws:secretsmanager:*:*:secret:*
- Effect: Allow
  Action:
    - amplify:CreateBranch
    - amplify:UpdateBranch
  Resource:
    - arn:aws:amplify:*:${aws:accountId}:apps/*
    - arn:aws:amplify:*:${aws:accountId}:apps/*/branches/*
- Effect: Allow
  Action:
    - 'ses:SendEmail'
    - 'ses:SendRawEmail'
  Resource:
    - arn:aws:ses:*:${aws:accountId}:identity/*
- Effect: Allow
  Action:
    - lambda:InvokeFunction
  Resource:
    - arn:aws:lambda:*:${aws:accountId}:function:trecenter-api-*
