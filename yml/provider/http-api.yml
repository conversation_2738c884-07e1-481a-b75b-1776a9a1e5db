authorizers:
  superAdminAuthorizer:
    type: request
    functionName: superAdminAuthorizer
    identitySource:
      - '$request.header.Authorization'
  adminAuthorizer:
    type: request
    functionName: adminAuthorizer
    identitySource:
      - '$request.header.Authorization'
  staffAuthorizer:
    type: request
    functionName: staffAuthorizer
    identitySource:
      - '$request.header.Authorization'
  clientAuthorizer:
    type: request
    functionName: clientAuthorizer
    identitySource:
      - '$request.header.Authorization'
  cmsAuthorizer:
    type: request
    functionName: cmsAuthorizer
    identitySource:
      - '$request.header.Authorization'
  reportAuthorizer:
    type: request
    functionName: reportAuthorizer
    identitySource:
      - '$request.header.Authorization'
  customAuthorizer:
    type: request
    functionName: customAuthorizer
    identitySource:
      - '$request.header.Authorization'

cors:
  allowedOrigins:
    - ${param:web_app_domain}
    - ${param:cms_domain}
    - ${param:ticketops_domain}
    - https://g0xuwik145.execute-api.ap-southeast-1.amazonaws.com
    - http://localhost:3000 # web's port
    - http://localhost:3003 # cms's port
  allowedHeaders: '*'
  allowedMethods:
    - GET
    - PUT
    - POST
    - DELETE
    - OPTIONS
  allowCredentials: true
  exposedResponseHeaders: '*'
  maxAge: 6000 # In seconds
