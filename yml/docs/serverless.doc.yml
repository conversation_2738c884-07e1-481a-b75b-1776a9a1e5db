documentation:
  version: "1.0"
  title: "Trẻ Center API"
  description: "Trẻ Center API"
  termsOfService: https://google.com
  servers:
    - url: https://3qybiunijh.execute-api.ap-southeast-1.amazonaws.com
      description: Dev Api
    - url: http://localhost:4000
      description: Local Api
  securitySchemes:
    auth:
      type: apiKey
      name: Authorization
      in: header
  security:
    - auth: []

endpoints:
  login:
    description: "Login"
    tags:
      - "Auth"
    requestBody:
      description: ""
      required: true
    requestModels:
      application/json:
        schema:
          type: object
          properties:
            email:
              type: string
            password:
              type: string
          required:
            - email
            - password
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  register:
    description: "Register"
    tags:
      - "Auth"
    requestBody:
      description: ""
      required: true
    requestModels:
      application/json:
        schema:
          type: object
          properties:
            email:
              type: string
            phone:
              type: string
            password:
              type: string
          required:
            - email
            - password
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  changePassword:
    description: "Change password"
    tags:
      - "Auth"
    requestBody:
      description: ""
      required: true
    requestModels:
      application/json:
        schema:
          type: object
          properties:
            oldPassword:
              type: string
            newPassword:
              type: string
          required:
            - oldPassword
            - newPassword
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  resetPassword:
    description: "Reset password"
    tags:
      - "Auth"
    requestBody:
      description: ""
      required: true
    requestModels:
      application/json:
        schema:
          type: object
          properties:
            newPassword:
              type: string
          required:
            - newPassword
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  sendOTP:
    description: "Request OTP"
    tags:
      - "Auth"
    requestBody:
      description: ""
      required: true
    requestModels:
      application/json:
        schema:
          type: object
          properties:
            email:
              type: string
          required:
            - email
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  verifyOTP:
    description: "Verify OTP"
    tags:
      - "Auth"
    requestBody:
      description: ""
      required: true
    requestModels:
      application/json:
        schema:
          type: object
          properties:
            email:
              type: string
            otp:
              type: string
          required:
            - email
            - otp
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  updateProfile:
    description: "Update info"
    tags:
      - "Auth"
    requestBody:
      description: ""
      required: true
    requestModels:
      application/json:
        schema:
          type: object
          properties:
            name:
              type: string
            icCard:
              type: string
            birthday:
              type: string
            phone:
              type: string
            gender:
              type: number
            studentId:
              type: string
            profileImg:
              type: string
            university:
              type: string
            major:
              type: string
            schoolYear:
              type: string
            address:
              type: string
          required:
            - name
            - icCard
            - birthday
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  getProfile:
    description: "Get info"
    tags:
      - "Auth"
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  deleteAccount:
    description: "Delete account"
    tags:
      - "Auth"
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  verifyStudent:
    description: "Verify student"
    tags:
      - "Auth"
    requestBody:
      description: ""
      required: true
    requestModels:
      application/json:
        schema:
          type: object
          properties:
            name:
              type: string
            icCard:
              type: string
            birthday:
              type: string
            phone:
              type: string
            gender:
              type: number
            studentId:
              type: string
            profileImg:
              type: string
            university:
              type: string
            major:
              type: string
            schoolYear:
              type: string
            schoolCertificate:
              type: string
          required:
            - name
            - icCard
            - birthday
            - gender
            - phone
            - university
            - major
            - studentId
            - schoolYear
            - schoolCertificate
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  getCertificate:
    description: "Get certificate"
    tags:
      - "Auth"
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  getToken:
    description: "Get token"
    tags:
      - "Auth"
    requestBody:
      description: ""
      required: true
    requestModels:
      application/json:
        schema:
          type: object
          properties:
            refreshToken:
              type: string
          required:
            - refreshToken
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  homepage:
    description: "Get homepage"
    tags:
      - "Page"
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  concertPage:
    description: "Get concert page"
    tags:
      - "Page"
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  recap:
    description: "Get recap"
    tags:
      - "Page"
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  podcastPage:
    description: "Get podcast page"
    tags:
      - "Page"
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  podcastDetail:
    description: "Get podcast detail"
    tags:
      - "Podcast"
    pathParams:
      - name: "podcastId"
        description: ""
        schema:
          type: "string"
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  coursePage:
    description: "Get course page"
    tags:
      - "Page"
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  courseDetail:
    description: "Get course detail"
    tags:
      - "Course"
    pathParams:
      - name: "courseId"
        description: ""
        schema:
          type: "string"
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  createLesson:
    description: "Create lesson"
    tags:
      - "Course"
    pathParams:
      - name: "courseId"
        description: ""
        schema:
          type: "string"
    requestBody:
      description: ""
      required: true
    requestModels:
      application/json:
        schema:
          type: object
          properties:
            title:
              type: string
            type:
              type: string
            videoUrl:
              type: string
            sort:
              type: number
              example: 1
          required:
            - title
            - type
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  getLesson:
    description: "Get lesson"
    tags:
      - "Course"
    pathParams:
      - name: "courseId"
        description: ""
        schema:
          type: "string"
      - name: "lessonId"
        description: ""
        schema:
          type: "string"
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  updateLesson:
    description: "Update lesson"
    tags:
      - "Course"
    pathParams:
      - name: "courseId"
        description: ""
        schema:
          type: "string"
      - name: "lessonId"
        description: ""
        schema:
          type: "string"
    requestBody:
      description: ""
      required: true
    requestModels:
      application/json:
        schema:
          type: object
          properties:
            title:
              type: string
            sort:
              type: number
              example: 1
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  deleteLesson:
    description: "Delete lesson"
    tags:
      - "Course"
    pathParams:
      - name: "courseId"
        description: ""
        schema:
          type: "string"
      - name: "lessonId"
        description: ""
        schema:
          type: "string"
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  albumDetail:
    description: "Get album detail"
    tags:
      - "Podcast"
    pathParams:
      - name: "albumId"
        description: ""
        schema:
          type: "string"
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  getConfigs:
    description: "Get page's configs"
    tags:
      - "Config"
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  sortConfigs:
    description: "Sort configs"
    tags:
      - "Config"
    requestBody:
      description: ""
      required: true
    requestModels:
      application/json:
        schema:
          type: object
          properties:
            configs:
              type: array
              items:
                type: object
                properties:
                  PK:
                    type: string
                    example: HOMEPAGE
                  SK:
                    type: string
                    example: SECTION_EVENT
                  sort:
                    type: number
                    example: 1
          required:
            - configs
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  createConfig:
    description: "Create config"
    tags:
      - "Config"
    requestBody:
      description: ""
      required: true
    requestModels:
      application/json:
        schema:
          type: object
          properties:
            page:
              type: string
              example: HOMEPAGE
            position:
              type: string
              example: BANNER
            navigationUrl:
              type: string
            title:
              type: string
            sort:
              type: number
              example: 1
            items:
              type: array
              items:
                type: object
                properties:
                  PK:
                    type: string
                  SK:
                    type: string
                  sort:
                    type: number
                    example: 1
          required:
            - page
            - position
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  updateConfig:
    description: "Update config"
    tags:
      - "Config"
    requestBody:
      description: ""
      required: true
    requestModels:
      application/json:
        schema:
          type: object
          properties:
            PK:
              type: string
            SK:
              type: string
            position:
              type: string
              example: BANNER
            navigationUrl:
              type: string
            title:
              type: string
            sort:
              type: number
              example: 1
            items:
              type: array
              items:
                type: object
                properties:
                  PK:
                    type: string
                  SK:
                    type: string
                  sort:
                    type: number
                    example: 1
          required:
            - PK
            - SK
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  deleteConfig:
    description: "Delete config"
    tags:
      - "Config"
    requestBody:
      description: ""
      required: true
    requestModels:
      application/json:
        schema:
          type: object
          properties:
            PK:
              type: string
            SK:
              type: string
          required:
            - PK
            - SK
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  getBlogs:
    description: "Get all blogs"
    tags:
      - "Blog"
    queryParams:
      - name: "type"
        description: ""
        schema:
          type: "string"
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  createBlog:
    description: "Create blog"
    tags:
      - "Blog"
    requestBody:
      description: ""
      required: true
    requestModels:
      application/json:
        schema:
          type: object
          properties:
            title:
              type: string
            description:
              type: string
            type:
              type: string
            banner:
              type: string
            button:
              type: object
              properties:
                  title:
                    type: string
                  url:
                    type: string
            startTime:
              type: string
            endTime:
              type: string
            author:
              type: string
            file:
              type: string
            children:
              type: array
              items:
                type: string
            sort:
              type: number
              example: 1
          required:
            - title
            - description
            - banner
            - type
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  updateBlog:
    description: "Update blog"
    tags:
      - "Blog"
    requestBody:
      description: ""
      required: true
    requestModels:
      application/json:
        schema:
          type: object
          properties:
            PK:
              type: string
            SK:
              type: string
            title:
              type: string
            description:
              type: string
            banner:
              type: string
            button:
              type: object
              properties:
                title:
                  type: string
                url:
                  type: string
            sort:
              type: number
              example: 1
          required:
            - PK
            - SK
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  deleteBlog:
    description: "Delete blog"
    tags:
      - "Blog"
    requestBody:
      description: ""
      required: true
    requestModels:
      application/json:
        schema:
          type: object
          properties:
            PK:
              type: string
            SK:
              type: string
          required:
            - PK
            - SK
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  ticketClasses:
    description: "Get ticket classes"
    tags:
      - "Event"
    pathParams:
      - name: "eventId"
        description: ""
        schema:
          type: "string"
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  getEventDetail:
    description: "Get event detail"
    tags:
      - "Event"
    pathParams:
      - name: "eventId"
        description: ""
        schema:
          type: "string"
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  createOrder:
    description: "Create order"
    tags:
      - "Order"
    requestBody:
      description: ""
      required: true
    requestModels:
      application/json:
        schema:
          type: object
          properties:
            productId:
              type: string
            orderId:
              type: string
            paymentGateway:
              type: string
            paymentInfo:
              type: string
          required:
            - productId
            - orderId
            - paymentGateway
            - paymentInfo
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  getCart:
    description: "Get cart"
    tags:
      - "Order"
    pathParams:
      - name: "eventId"
        description: ""
        schema:
          type: "string"
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  cancelOrder:
    description: "Cancel order"
    tags:
      - "Order"
    pathParams:
      - name: "eventId"
        description: ""
        schema:
          type: "string"
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  confirmOrder:
    description: "Confirm order"
    tags:
      - "Order"
    pathParams:
      - name: "eventId"
        description: ""
        schema:
          type: "string"
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  myTickets:
    description: "Get purchased tickets"
    tags:
      - "User"
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  orderTickets:
    description: "Get tickets by order"
    tags:
      - "User"
    pathParams:
      - name: "orderId"
        description: ""
        schema:
          type: "string"
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
  upload:
    description: "Upload"
    tags:
      - "Upload"
    requestBody:
      description: ""
      required: true
    requestModels:
      application/json:
        schema:
          type: object
          properties:
            srcKey:
              type: string
          required:
            - srcKey
    methodResponses:
      - statusCode: 200
        responseBody:
          description: ""
      - statusCode: 404
        responseBody:
          description: "Not found"
      - statusCode: 500
        responseBody:
          description: "Internal server error."
