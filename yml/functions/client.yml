clientAuthorizer:
  handler: src/handler/client/auth/authorizer.auth

customAuthorizer:
  handler: src/handler/client/auth/custom-authorizer.auth

client-homepage:
  handler: src/handler/client/home/<USER>
  events:
    - httpApi:
        path: /v1/home
        method: get
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.homepage}

client-concert:
  handler: src/handler/client/concert/index.handler
  events:
    - httpApi:
        path: /v1/concerts
        method: get
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.concertPage}
    - httpApi:
        path: /v1/concerts/recap
        method: get
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.recap}

client-podcast:
  handler: src/handler/client/podcast/index.handler
  events:
    - httpApi:
        path: /v1/podcasts
        method: get
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.podcastPage}
    - httpApi:
        path: /v1/podcast/{podcastId}
        method: get
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.podcastDetail}
    - httpApi:
        path: /v1/podcast/album/{albumId}
        method: get
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.albumDetail}

# client-course:
#   handler: src/handler/client/course/index.handler
#   events:
#     - httpApi:
#         path: /v1/courses
#         method: get
#         documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.coursePage}
#     - httpApi:
#         path: /v1/course/{courseId}
#         method: get
#         documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.courseDetail}

auth-register:
  handler: src/handler/client/auth/index.handler
  events:
    - httpApi:
        path: /v1/auth/register
        method: post
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.register}
    - httpApi:
        path: /v1/auth/otp-verification
        method: post
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.verifyOTP}
    - httpApi:
        path: /v1/auth/otp-send
        method: post
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.sendOTP}
    - httpApi:
        path: /v1/auth/login
        method: post
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.login}
    - httpApi:
        path: /v1/auth/reset-password
        method: post
    - httpApi:
        path: /v1/auth/reset-password
        method: put
        authorizer:
          name: clientAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.resetPassword}
    - httpApi:
        path: /v1/auth/login/google
        method: post
    - httpApi:
        path: /v1/auth/login/apple
        method: post
    - httpApi:
        path: /v1/auth/token
        method: post
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.getToken}
    - httpApi:
        path: /v1/auth/profile
        method: put
        authorizer:
          name: clientAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.updateProfile}
    - httpApi:
        path: /v1/auth/me
        method: get
        authorizer:
          name: clientAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.getProfile}
    - httpApi:
        path: /v1/auth/change-password
        method: put
        authorizer:
          name: clientAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.changePassword}
    - httpApi:
        path: /v1/auth/delete-account
        method: put
        authorizer:
          name: clientAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.deleteAccount}
    - httpApi:
        path: /v1/auth/student-verification
        method: put
        authorizer:
          name: clientAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.verifyStudent}
    - httpApi:
        path: /v1/auth/cert
        method: get
        authorizer:
          name: clientAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.getCertificate}

client-card:
  handler: src/handler/client/card/index.handler
  events:
    - httpApi:
        path: /v1/card/register
        method: post
        authorizer:
          name: clientAuthorizer

client-order:
  handler: src/handler/client/order/index.handler
  environment:
    QUEUE_URL: ${construct:order-queue.queueUrl}
    TICKET_QUEUE_URL: ${construct:auto-ticket-queue.queueUrl}
  events:
    - httpApi:
        path: /v1/order/create
        method: post
        authorizer:
          name: customAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.createOrder}
    - httpApi:
        path: /v1/order/add-to-cart
        method: post
        authorizer:
          name: customAuthorizer
    - httpApi:
        path: /v1/order/event/{eventId}/cart
        method: get
        authorizer:
          name: customAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.getCart}
    - httpApi:
        path: /v1/order/event/{eventId}/cancel
        method: post
        authorizer:
          name: customAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.cancelOrder}
    - httpApi:
        path: /v1/order/event/{eventId}/cart/confirmation
        method: post
        authorizer:
          name: customAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.confirmOrder}
    - httpApi:
        path: /v1/order/check
        method: post
        authorizer:
          name: customAuthorizer
    - httpApi:
        path: /v1/order/review
        method: post
        authorizer:
          name: customAuthorizer
    - httpApi:
        path: /v1/order/review
        method: get
        authorizer:
          name: customAuthorizer
    - httpApi:
        path: /v2/order/event/{pathName}/remaining-time
        method: get
        authorizer:
          name: customAuthorizer

client-event:
  handler: src/handler/client/event/index.handler
  events:
    - httpApi:
        path: /v1/event/{eventId}
        method: get
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.getEventDetail}
    - httpApi:
        path: /v1/event/{eventId}/ticket-classes
        method: get
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.ticketClasses}
    - httpApi:
        path: /v1/event/categories
        method: get
    - httpApi:
        path: /v1/event/{eventId}/configure
        method: get
    - httpApi:
        path: /v2/event/{pathName}/seatmap
        method: get
    - httpApi:
        path: /v2/event/{pathName}/tickets
        method: get
    - httpApi:
        path: /v2/event/{pathName}/tickets-zone
        method: get

client-user:
  handler: src/handler/client/user/index.handler
  events:
    - httpApi:
        path: /v1/user/tickets
        method: get
        authorizer:
          name: customAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.myTickets}
    - httpApi:
        path: /v1/user/order/{orderId}
        method: get
        authorizer:
          name: customAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.orderTickets}
    - httpApi:
        path: /v1/user/orders
        method: get
        authorizer:
          name: customAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.orderTickets}
    - httpApi:
        path: /v1/user/notification/register
        method: post
        authorizer:
          name: customAuthorizer
    - httpApi:
        path: /v1/user/logout
        method: post
        authorizer:
          name: customAuthorizer

payment-gateway:
  handler: src/handler/client/payment-method/index.handler
  events:
    - httpApi:
        path: /v1/payment-gateway
        method: get
    - httpApi:
        path: /v1/payment-method
        method: get

cart:
  handler: src/handler/client/order/cart.handler
  events:
    - httpApi:
        path: /v1/{merchantId}/cart
        method: post
        authorizer:
          name: customAuthorizer
    - httpApi:
        path: /v1/{merchantId}/cart/{productId}
        method: delete
        authorizer:
          name: customAuthorizer
    - httpApi:
        path: /v1/{merchantId}/cart
        method: get
        authorizer:
          name: customAuthorizer
    - httpApi:
        path: /v1/{merchantId}/shipment/fee
        method: post
        authorizer:
          name: customAuthorizer

promotion:
  handler: src/handler/client/promotion/index.handler
  events:
    - httpApi:
        path: /v1/promotion/check
        method: post

payment:
  handler: src/handler/payment/index.handler
  events:
    - httpApi:
        path: /v1/payment/my-location
        method: get
    - httpApi:
        path: /v1/payment/paymentwall/payment-solution/{countryCode}
        method: get

paypal:
  handler: src/handler/client/order/paypal.handler
  events:
    - httpApi:
        path: /v1/paypal/create-order
        method: post
    - httpApi:
        path: /v1/paypal/client-token
        method: get
    - httpApi:
        path: /v1/paypal/complete-order
        method: post

vietinbank:
  handler: src/handler/vietinbank/index.handler
  environment:
    DST_PRIVATE_KEY: ${ssm:DST_PRIVATE_KEY}
    PASS_PHARSE: ${ssm:PASS_PHARSE}
  events:
    - httpApi:
        path: /v1/deeplink/create
        method: post

client-notification:
  handler: src/handler/client/notification/index.handler
  events:
    - httpApi:
        path: /v1/notifications
        method: get
        authorizer:
          name: customAuthorizer
    - httpApi:
        path: /v1/notifications/{notificationId}
        method: get
        authorizer:
          name: customAuthorizer
    - httpApi:
        path: /v1/notifications/read
        method: put
        authorizer:
          name: customAuthorizer
    - httpApi:
        path: /v1/notifications/{notificationId}/read
        method: put
        authorizer:
          name: customAuthorizer
    - httpApi:
        path: /v1/notifications/{notificationId}
        method: delete
        authorizer:
          name: customAuthorizer
    - httpApi:
        path: /v1/notifications
        method: delete
        authorizer:
          name: customAuthorizer
