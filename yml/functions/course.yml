adminAuthorizer:
  handler: src/handler/cms/auth/authorizer.admin

customAuthorizer:
  handler: src/handler/client/auth/custom-authorizer.auth

course:
  handler: src/handler/courses/index.handler
  events:
    - httpApi:
        path: /v1/courses/home
        method: get
    - httpApi:
        path: /v1/courses/categories
        method: get
    - httpApi:
        path: /v1/cms/courses/categories
        method: post
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/courses/categories
        method: get
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/courses/categories/{categoryId}
        method: get
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/courses/categories/{categoryId}
        method: put
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/courses/categories/{categoryId}
        method: delete
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/courses
        method: post
        authorizer:
          name: admin<PERSON>uthorizer
    - httpApi:
        path: /v1/courses
        method: get
    - httpApi:
        path: /v1/courses/{courseId}
        method: get
    - httpApi:
        path: /v1/cms/courses/{courseId}
        method: get
        authorizer:
          name: admin<PERSON>uthorizer
    - httpApi:
        path: /v1/cms/courses/{courseId}
        method: put
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/courses/{courseId}
        method: delete
        authorizer:
          name: adminAuthorizer

creator:
  handler: src/handler/creators/index.handler
  events:
    - httpApi:
        path: /v1/cms/creators
        method: post
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/creators
        method: get
    - httpApi:
        path: /v1/creators/{creatorId}
        method: get
    - httpApi:
        path: /v1/cms/creators/{creatorId}
        method: put
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/creators/{creatorId}
        method: delete
        authorizer:
          name: adminAuthorizer

lesson:
  handler: src/handler/courses/lesson.handler
  events:
    - httpApi:
        path: /v1/cms/courses/{courseId}/lessons
        method: post
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/courses/lessons
        method: get
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/courses/lessons/{lessonId}
        method: get
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/courses/lessons/{lessonId}
        method: put
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/courses/lessons/{lessonId}
        method: delete
        authorizer:
          name: adminAuthorizer

quiz:
  handler: src/handler/quiz/index.handler
  events:
    - httpApi:
        path: /v1/quiz/{quizId}
        method: get
        authorizer:
          name: customAuthorizer
    - httpApi:
        path: /v1/quiz/{quizId}
        method: post
        authorizer:
          name: customAuthorizer
    - httpApi:
        path: /v1/cms/quiz
        method: post
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/quiz/{quizId}
        method: put
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/quiz/{quizId}
        method: delete
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/quiz/{quizId}/questions
        method: post
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/quiz/results/{quizType}
        method: get
        authorizer:
          name: customAuthorizer
