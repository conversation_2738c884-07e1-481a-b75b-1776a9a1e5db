holding-ticket-resolver:
  handler: src/handler/client/order/order-schedule.resolveHoldingTicket
  events:
    - schedule: rate(1 minute)

event-status-scheduler:
  handler: src/handler/client/event/event-schedule.handler
  events:
    - schedule: rate(1 minute)

order-status-scheduler:
  handler: src/handler/client/order/order-schedule.updateOrderExpired
  events:
    - schedule: rate(1 minute)

holding-ticket-resolver-v2:
  handler: src/handler/schedule/order-schedule.resoleHoldingTickets
  events:
    - schedule: rate(1 minute)

cleanup-expired-otp:
  handler: src/handler/cron/index.handler
  events:
    - schedule:
        rate: rate(1 minute)
        enabled: true