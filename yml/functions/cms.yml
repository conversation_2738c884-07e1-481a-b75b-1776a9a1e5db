superAdminAuthorizer:
  handler: src/handler/cms/auth/authorizer.superAdmin

adminAuthorizer:
  handler: src/handler/cms/auth/authorizer.admin

staffAuthorizer:
  handler: src/handler/cms/auth/authorizer.staff

cmsAuthorizer:
  handler: src/handler/cms/auth/authorizer.cmsAccess

reportAuthorizer:
  handler: src/handler/cms/auth/authorizer.report

cms-auth:
  handler: src/handler/cms/auth/index.handler
  events:
    - httpApi:
        path: /v1/cms/login
        method: post

cms-user:
  handler: src/handler/cms/user/index.handler
  events:
    - httpApi:
        path: /v1/cms/user
        method: post
        authorizer:
          name: cmsAuthorizer
    - httpApi:
        path: /v1/cms/user/qrcode
        method: post
        authorizer:
          name: superAdminAuthorizer
    - httpApi:
        path: /v1/cms/user/me
        method: get
        authorizer:
          name: cmsAuthorizer
    - httpApi:
        path: /v1/cms/users
        method: get
        authorizer:
          name: cmsAuthorizer
    - httpApi:
        path: /v1/cms/user/{userId}/change-password
        method: post
        authorizer:
          name: superAdminAuthorizer
    - httpApi:
        path: /v1/cms/users/{userId}
        method: put
        authorizer:
          name: cmsAuthorizer
    - httpApi:
        path: /v1/cms/users/{userId}
        method: delete
        authorizer:
          name: cmsAuthorizer
    - httpApi:
        path: /v1/cms/users/{userId}
        method: get
        authorizer:
          name: cmsAuthorizer

cms-ticket:
  handler: src/handler/cms/ticket/index.handler
  environment:
    LAMBDA_BROADCAST: !Ref BroadcastLambdaFunction
  events:
    - httpApi:
        path: /v1/cms/ticket/check-in
        method: post
        authorizer:
          name: staffAuthorizer
    - httpApi:
        path: /v1/cms/ticket/check-in
        method: put
        authorizer:
          name: staffAuthorizer
    - httpApi:
        path: /v1/cms/ticket
        method: get
        authorizer:
          name: staffAuthorizer
    - httpApi:
        path: /v2/cms/ticket
        method: delete
        authorizer:
          name: staffAuthorizer
    - httpApi:
        path: /v2/cms/ticket/actions
        method: post
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v2/cms/tickets
        method: get
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v2/cms/seatmap
        method: get
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v2/cms/seatmap
        method: post
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v2/cms/seatmap
        method: put
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v2/cms/event/{eventId}/ticket/send
        method: post
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v2/cms/tickets
        method: put
        authorizer:
          name: adminAuthorizer

cms-events:
  handler: src/handler/cms/events/index.handler
  events:
    - httpApi:
        path: /v1/cms/event
        method: get
        authorizer:
          name: cmsAuthorizer
    - httpApi:
        path: /v1/cms/event/categories
        method: get
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/event/{eventId}
        method: get
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/event/{eventId}/status
        method: post
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/event
        method: post
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/event/{eventId}/ticket
        method: post
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/event/{eventId}/ticket/{ticketId}
        method: get
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/event/{eventId}/ticket/{ticketId}
        method: put
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/event/{eventId}/ticket/{ticketId}
        method: delete
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/event/{eventId}/{updateFor}
        method: put
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v2/cms/event/{eventId}/calendar/{calendarId}/zone
        method: post
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v2/cms/event/{eventId}/calendar/{calendarId}
        method: get
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v2/cms/event/{eventId}/calendar/{calendarId}/zone/{zoneId}/row
        method: post
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v2/cms/event/{eventId}/calendar/{calendarId}/zone/{zoneId}
        method: delete
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v2/cms/event/{eventId}/calendar/{calendarId}/zone/{zoneId}/row/{rowId}
        method: delete
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v2/cms/event/{eventId}/calendar/{calendarId}/ticket-class
        method: post
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v2/cms/event/{eventId}/calendar/{calendarId}/ticket-class/{ticketClassId}
        method: delete
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v2/cms/event/{eventId}/calendar/{calendarId}/ticket-class/{ticketClassId}
        method: put
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v2/cms/event/{eventId}/calendar/{calendarId}/ticket-classes
        method: get
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v2/cms/event/{eventId}/calendar/{calendarId}/zone/{zoneId}/row/{rowId}
        method: put
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v2/cms/event/{eventId}/calendar/{calendarId}/zones
        method: get
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v2/cms/event/{eventId}/calendar/{calendarId}/zone/{zoneId}/rows
        method: get
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v2/cms/event/{eventId}/calendar/{calendarId}/zone/{zoneId}/row/{rowId}/tickets
        method: get
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v2/cms/event/{eventId}/calendar/{calendarId}/tickets
        method: get
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v2/cms/event/{eventId}/calendar/{calendarId}/sort-zones
        method: post
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v2/cms/event/{eventId}/calendar/{calendarId}/zone/{zoneId}/sort-rows
        method: post
        authorizer:
          name: adminAuthorizer

cms-reports:
  handler: src/handler/cms/reports/index.handler
  timeout: 180
  events:
    - httpApi:
        path: /v1/cms/report/{productType}
        method: get
        authorizer:
          name: reportAuthorizer
    - httpApi:
        path: /v1/cms/report/analytics
        method: get
        authorizer:
          name: reportAuthorizer
    - httpApi:
        path: /v1/cms/report/orders/{productType}
        method: get
        authorizer:
          name: reportAuthorizer
    - httpApi:
        path: /v1/cms/report/delivery/{orderId}
        method: post
        authorizer:
          name: reportAuthorizer
    - httpApi:
        path: /v1/cms/report/orders/{orderId}/{productId}
        method: get
        authorizer:
          name: reportAuthorizer

cms-merchant:
  handler: src/handler/cms/merchant/index.handler
  events:
    - httpApi:
        path: /v1/cms/merchant
        method: post
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/merchant/products
        method: post
        authorizer:
          name: adminAuthorizer

cms-tenant:
  handler: src/handler/cms/tenant/index.handler
  events:
    - httpApi:
        path: /v1/cms/tenant
        method: post
        authorizer:
          name: superAdminAuthorizer
    - httpApi:
        path: /v1/cms/tenants
        method: get
        authorizer:
          name: cmsAuthorizer
    - httpApi:
        path: /v1/cms/tenant/{tenantId}
        method: get
        authorizer:
          name: cmsAuthorizer
    - httpApi:
        path: /v1/cms/tenant/{tenantId}
        method: put
        authorizer:
          name: superAdminAuthorizer

cms-promotion:
  handler: src/handler/cms/promotion/index.handler
  events:
    - httpApi:
        path: /v1/cms/promotion/{eventId}
        method: post
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/promotion/{eventId}
        method: get
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/promotion/{eventId}/tickets
        method: get
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/promotion/{eventId}/{code}
        method: get
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/promotion/{eventId}/{code}
        method: delete
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/promotion/{eventId}/{code}
        method: put
        authorizer:
          name: adminAuthorizer
    - httpApi:
        path: /v1/cms/promotion/{eventId}/{code}/status
        method: patch
        authorizer:
          name: adminAuthorizer

cms-config:
  handler: src/handler/cms/config/index.handler
  events:
    - httpApi:
        path: /v1/cms/configs
        method: get
        authorizer:
          name: adminAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.getConfigs}
    - httpApi:
        path: /v1/cms/configs/sort
        method: post
        authorizer:
          name: adminAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.sortConfigs}
    - httpApi:
        path: /v1/cms/config
        method: post
        authorizer:
          name: adminAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.createConfig}
    - httpApi:
        path: /v1/cms/config
        method: put
        authorizer:
          name: adminAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.updateConfig}
    - httpApi:
        path: /v1/cms/config
        method: delete
        authorizer:
          name: adminAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.deleteConfig}

cms-blog:
  handler: src/handler/cms/blog/index.handler
  events:
    - httpApi:
        path: /v1/cms/blogs
        method: get
        authorizer:
          name: adminAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.getBlogs}
    - httpApi:
        path: /v1/cms/blog
        method: post
        authorizer:
          name: adminAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.createBlog}
    - httpApi:
        path: /v1/cms/blog
        method: put
        authorizer:
          name: adminAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.updateBlog}
    - httpApi:
        path: /v1/cms/blog
        method: delete
        authorizer:
          name: adminAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.deleteBlog}

cms-course:
  handler: src/handler/cms/course/index.handler
  events:
    - httpApi:
        path: /v1/cms/course/{courseId}/lesson
        method: post
        authorizer:
          name: adminAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.createLesson}
    - httpApi:
        path: /v1/cms/course/{courseId}/lesson/{lessonId}
        method: get
        authorizer:
          name: adminAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.getLesson}
    - httpApi:
        path: /v1/cms/course/{courseId}/lesson/{lessonId}
        method: put
        authorizer:
          name: adminAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.updateLesson}
    - httpApi:
        path: /v1/cms/course/{courseId}/lesson/{lessonId}
        method: delete
        authorizer:
          name: adminAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.deleteLesson}

cms-notification:
  handler: src/handler/cms/notification/index.handler
  events:
    - httpApi:
        path: /v1/cms/notifications
        method: post
        # authorizer:
        #   name: adminAuthorizer
