upload:
  handler: src/handler/uploads/index.handler
  events:
    - httpApi:
        path: /v1/upload/image-upload
        method: post
        authorizer:
          name: clientAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.upload}
    - httpApi:
        path: /v1/cms/upload/podcast-upload
        method: post
        authorizer:
          name: adminAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.upload}
    - httpApi:
        path: /v1/cms/upload/course-upload
        method: post
        authorizer:
          name: adminAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.upload}
    - httpApi:
        path: /v1/cms/upload/image-upload
        method: post
        authorizer:
          name: adminAuthorizer
        documentation: ${file(./yml/docs/serverless.doc.yml):endpoints.upload}

swaggerUI:
  handler: src/handler/swagger/index.handler
  enabled: ${self:custom.enabled.${param:env}}
  events:
    - http:
        path: /docs/{proxy+}
        method: any
        integration: lambda-proxy

ffmpegProcessor:
  handler: src/handler/ffmpeg/index.handler
  layers:
    - { Ref: FfmpegLambdaLayer }
  timeout: 900
  events:
    - s3:
        bucket: ${param:web_bucket}
        event: s3:ObjectCreated:*
        rules:
          - prefix: uploads/trecenter/podcast/
          - suffix: .mp3
        existing: true
    - s3:
        bucket: ${param:web_bucket}
        event: s3:ObjectCreated:*
        rules:
          - prefix: uploads/trecenter/course/
          - suffix: .mp4
        existing: true

thumbnail:
  handler: src/handler/uploads/thumbnail.handler
  events:
    - s3:
        bucket: ${param:web_bucket}
        event: s3:ObjectCreated:*
        rules:
          - prefix: uploads/trecenter/client/
        existing: true
  environment:
    WIDTH: 1200
