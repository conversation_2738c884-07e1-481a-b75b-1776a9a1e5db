payment-ipn:
  handler: src/handler/payment/index.handler
  environment:
    QUEUE_URL: ${construct:order-queue.queueUrl}
  events:
    - httpApi:
        path: /v1/payment/momo
        method: post
    - httpApi:
        path: /v1/payment/vnpay
        method: get
    - httpApi:
        path: /v1/payment/paymentwall
        method: get
    - httpApi:
        path: /v1/payment/paypal
        method: post
    - httpApi:
        path: /v1/payment/zalopay
        method: post
